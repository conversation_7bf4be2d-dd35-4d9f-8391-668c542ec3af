"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON><PERSON>, Zap, TrendingUp } from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { ModernCard, ModernCardContent } from "@/components/ui/modern-card"
import type { SearchFilters } from "../types/catalog"

interface AISuggestion {
  id: string
  type: "search" | "filter" | "tip"
  title: string
  description: string
  action: string
  filters?: Partial<SearchFilters>
}

interface AISearchSuggestionsProps {
  query: string
  resultsCount: number
  onApplyFilters: (filters: Partial<SearchFilters>) => void
  onUpdateQuery: (query: string) => void
}

export function AISearchSuggestions({ query, resultsCount, onApplyFilters, onUpdateQuery }: AISearchSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([])
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const generateSuggestions = () => {
      const newSuggestions: AI<PERSON>uggestion[] = []

      // No results suggestions
      if (resultsCount === 0 && query) {
        newSuggestions.push({
          id: "no-results-1",
          type: "search",
          title: "Попробуйте упростить запрос",
          description: "Используйте более общие термины",
          action: "Упростить",
        })

        newSuggestions.push({
          id: "no-results-2",
          type: "filter",
          title: "Расширить поиск по категориям",
          description: "Включить все категории уплотнений",
          action: "Расширить",
          filters: { categoryIds: [] },
        })
      }

      // Few results suggestions
      if (resultsCount > 0 && resultsCount < 5 && query) {
        newSuggestions.push({
          id: "few-results-1",
          type: "search",
          title: "Найти похожие детали",
          description: "Поиск по синонимам и аналогам",
          action: "Найти похожие",
        })
      }

      // Many results suggestions
      if (resultsCount > 50) {
        newSuggestions.push({
          id: "many-results-1",
          type: "filter",
          title: "Уточнить по бренду",
          description: "Выберите предпочтительного производителя",
          action: "Показать бренды",
        })

        newSuggestions.push({
          id: "many-results-2",
          type: "tip",
          title: "Добавьте технические параметры",
          description: "Укажите размеры или материал для точного поиска",
          action: "Добавить параметры",
        })
      }

      // Smart suggestions based on query
      if (query.toLowerCase().includes("сальник") && !query.toLowerCase().includes("двигател")) {
        newSuggestions.push({
          id: "smart-1",
          type: "filter",
          title: "Сальники двигателя",
          description: "Самая популярная категория",
          action: "Показать",
          filters: { categoryIds: [1] },
        })
      }

      setSuggestions(newSuggestions)
      setIsVisible(newSuggestions.length > 0)
    }

    const timer = setTimeout(generateSuggestions, 500)
    return () => clearTimeout(timer)
  }, [query, resultsCount])

  const handleSuggestionClick = (suggestion: AISuggestion) => {
    if (suggestion.filters) {
      onApplyFilters(suggestion.filters)
    } else if (suggestion.type === "search") {
      // Handle search suggestions
      if (suggestion.id === "no-results-1") {
        const simplifiedQuery = query.split(" ").slice(0, 2).join(" ")
        onUpdateQuery(simplifiedQuery)
      }
    }
  }

  if (!isVisible) return null

  return (
    <ModernCard variant="glass" className="mb-4 border-primary/20 bg-primary/5">
      <ModernCardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className="h-8 w-8 rounded-full bg-gradient-to-br from-primary to-accent flex items-center justify-center flex-shrink-0">
            <Sparkles className="h-4 w-4 text-primary-foreground" />
          </div>
          <div className="flex-1 space-y-3">
            <div>
              <h3 className="font-semibold text-sm mb-1">AI предложения</h3>
              <p className="text-xs text-muted-foreground">Основано на вашем запросе и результатах поиска</p>
            </div>

            <div className="space-y-2">
              {suggestions.map((suggestion) => (
                <div
                  key={suggestion.id}
                  className="flex items-center justify-between p-3 rounded-lg bg-background/50 border border-border/30 hover:border-primary/30 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center">
                      {suggestion.type === "search" && <TrendingUp className="h-3 w-3" />}
                      {suggestion.type === "filter" && <Zap className="h-3 w-3" />}
                      {suggestion.type === "tip" && <Sparkles className="h-3 w-3" />}
                    </div>
                    <div>
                      <div className="font-medium text-sm">{suggestion.title}</div>
                      <div className="text-xs text-muted-foreground">{suggestion.description}</div>
                    </div>
                  </div>
                  <ModernButton
                    variant="outline"
                    size="sm"
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="h-7 text-xs"
                  >
                    {suggestion.action}
                  </ModernButton>
                </div>
              ))}
            </div>
          </div>
        </div>
      </ModernCardContent>
    </ModernCard>
  )
}
