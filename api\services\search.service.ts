import { getSystemDB } from '../db'
import { SearchPartsInput, SearchCatalogItemsInput, AttributeFilter, GetAttributeStatsInput } from '../schemas/search'

export class SearchService {
  static async searchParts(input: typeof SearchPartsInput._type) {
    const db = getSystemDB()
    const where: any = {}

    if (input.name) where.name = { contains: input.name, mode: 'insensitive' }
    if (input.categoryId) where.partCategoryId = input.categoryId
    if (input.categoryIds && input.categoryIds.length) where.partCategoryId = { in: input.categoryIds }
    if (input.brandIds && input.brandIds.length) {
      // фильтр по брендам через связку применимости с CatalogItem.brandId
      where.applicabilities = { some: { catalogItem: { brandId: { in: input.brandIds } } } }
    }

    if (input.attributeFilters && input.attributeFilters.length > 0) {
      const attributeConditions = [] as any[]
      for (const filter of input.attributeFilters) {
        const attrWhere: any = { templateId: filter.templateId }
        const template = await db.attributeTemplate.findUnique({ where: { id: filter.templateId } })
        if (!template) continue
        if (template.dataType === 'NUMBER') {
          if (filter.matchType === 'range' || (filter.minValue !== undefined || filter.maxValue !== undefined)) {
            const condition: any = {}
            if (filter.minValue !== undefined) condition.gte = filter.minValue
            if (filter.maxValue !== undefined) condition.lte = filter.maxValue
            attrWhere.numericValue = condition
          } else if (filter.value) {
            const numValue = parseFloat(filter.value)
            if (!isNaN(numValue)) attrWhere.numericValue = numValue
          }
        } else {
          if (filter.matchType === 'exact' && filter.value) attrWhere.value = filter.value
          else if (filter.matchType === 'contains' && filter.value) attrWhere.value = { contains: filter.value, mode: 'insensitive' }
          else if (filter.matchType === 'in' && filter.values) attrWhere.value = { in: filter.values }
        }
        attributeConditions.push(attrWhere)
      }
      if (attributeConditions.length > 0) {
        where.attributes = { some: { AND: attributeConditions } }
      }
    }

    const [items, total] = await Promise.all([
      db.part.findMany({
        where,
        include: {
          attributes: { include: { template: { select: { id: true, name: true, title: true, dataType: true, unit: true } } } },
          partCategory: true,
          applicabilities: { include: { catalogItem: { include: { brand: true } } } },
        },
        take: input.limit,
        skip: input.offset,
        orderBy: { [input.orderBy]: input.orderDir },
      }),
      db.part.count({ where }),
    ])

    return { items, total, limit: input.limit, offset: input.offset }
  }

  static async searchCatalogItems(input: typeof SearchCatalogItemsInput._type) {
    const db = getSystemDB()
    const where: any = {}
    if (input.search) where.OR = [{ sku: { contains: input.search, mode: 'insensitive' } }, { description: { contains: input.search, mode: 'insensitive' } }]
    if (input.sku) where.sku = { contains: input.sku, mode: 'insensitive' }
    if (input.brandId) where.brandId = input.brandId

    if (input.attributeFilters && input.attributeFilters.length > 0) {
      const attributeConditions = [] as any[]
      for (const filter of input.attributeFilters) {
        const attrWhere: any = { templateId: filter.templateId }
        const template = await db.attributeTemplate.findUnique({ where: { id: filter.templateId } })
        if (!template) continue
        if (template.dataType === 'NUMBER') {
          if (filter.matchType === 'range' || (filter.minValue !== undefined || filter.maxValue !== undefined)) {
            const condition: any = {}
            if (filter.minValue !== undefined) condition.gte = filter.minValue
            if (filter.maxValue !== undefined) condition.lte = filter.maxValue
            attrWhere.numericValue = condition
          } else if (filter.value) {
            const numValue = parseFloat(filter.value)
            if (!isNaN(numValue)) attrWhere.numericValue = numValue
          }
        } else {
          if (filter.matchType === 'exact' && filter.value) attrWhere.value = filter.value
          else if (filter.matchType === 'contains' && filter.value) attrWhere.value = { contains: filter.value, mode: 'insensitive' }
          else if (filter.matchType === 'in' && filter.values) attrWhere.value = { in: filter.values }
        }
        attributeConditions.push(attrWhere)
      }
      if (attributeConditions.length > 0) {
        where.attributes = { some: { AND: attributeConditions } }
      }
    }

    const [items, total] = await Promise.all([
      db.catalogItem.findMany({
        where,
        include: {
          brand: true,
          attributes: { include: { template: { select: { id: true, name: true, title: true, dataType: true, unit: true } } } },
          applicabilities: { include: { part: true } },
        },
        take: input.limit,
        skip: input.offset,
        orderBy: { [input.orderBy]: input.orderDir },
      }),
      db.catalogItem.count({ where }),
    ])

    return { items, total, limit: input.limit, offset: input.offset }
  }

  static async getAttributeStats(input: typeof GetAttributeStatsInput._type) {
    const db = getSystemDB()
    const tableName = input.entityType === 'part' ? 'PartAttribute' : input.entityType === 'catalogItem' ? 'CatalogItemAttribute' : 'EquipmentModelAttribute'
    const template = await db.attributeTemplate.findUnique({ where: { id: input.templateId } })
    if (!template || template.dataType !== 'NUMBER') return null
    const stats = await db.$queryRaw<any[]>`
      SELECT 
        MIN(CAST("numericValue" AS FLOAT)) as min,
        MAX(CAST("numericValue" AS FLOAT)) as max,
        AVG(CAST("numericValue" AS FLOAT)) as avg,
        COUNT(*) as count,
        COUNT(DISTINCT "numericValue") as distinctCount
      FROM "${tableName}"
      WHERE "templateId" = ${input.templateId}
        AND "numericValue" IS NOT NULL
    `
    return {
      templateId: input.templateId,
      templateName: template.name,
      templateTitle: template.title,
      unit: template.unit,
      min: stats[0]?.min ?? null,
      max: stats[0]?.max ?? null,
      avg: stats[0]?.avg ?? null,
      count: stats[0]?.count ?? 0,
      distinctCount: stats[0]?.distinctCount ?? 0,
    }
  }
}



