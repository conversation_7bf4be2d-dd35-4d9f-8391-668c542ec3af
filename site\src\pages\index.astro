---
import MainLayout from "../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/trpc";
import { TrpcProvider } from "@/components/providers/TrpcProvider";
import { SearchForm } from "@/components/catalog/SearchForm";
import { CategoryCard } from "@/components/catalog/CategoryCard";
import { PartCard } from "@/components/catalog/PartCard";
import type { PartCategoryItem, PartListItem } from "@/types/catalog";

// Загружаем данные для главной страницы
let featuredCategories: PartCategoryItem[] = [];
let recentParts: PartListItem[] = [];
let stats: { parts: number; categories: number; brands: number } = { parts: 0, categories: 0, brands: 0 };

try {
  // Получаем популярные категории (корневые)
  import { rootCategoriesInclude, partListInclude } from "@/lib/queries";
  const categoriesResponse = await trpcClient.crud.partCategory.findMany.query({
    where: { level: 0 },
    include: rootCategoriesInclude,
    take: 6,
    orderBy: { name: 'asc' }
  });
  featuredCategories = (categoriesResponse ?? []) as PartCategoryItem[];

  // Получаем недавно обновленные запчасти
  const partsResponse = await trpcClient.crud.part.findMany.query({
    include: partListInclude,
    take: 8,
    orderBy: { updatedAt: 'desc' }
  });
  recentParts = (partsResponse ?? []) as unknown as PartListItem[];

  // Получаем статистику
  const [partsCount, categoriesCount, brandsCount] = await Promise.all([
    trpcClient.crud.part.count.query(),
    trpcClient.crud.partCategory.count.query(),
    trpcClient.crud.brand.count.query(),
  ]);

  stats = {
    parts: partsCount || 0,
    categories: categoriesCount || 0,
    brands: brandsCount || 0
  };
} catch (error) {
  console.error('Error loading homepage data:', error);
}

---

<MainLayout title="Главная" description="PartTec3 - профессиональный каталог взаимозаменяемых запчастей для техники">
  <TrpcProvider client:load>
    <!-- Hero секция -->
    <section class="w-full py-12 md:py-24 lg:py-32 xl:py-48 bg-gradient-to-b from-background to-muted/20">
      <div class="container px-4 md:mx-auto">
        <div class="flex flex-col items-center space-y-4 text-center">
          <div class="space-y-2">
            <h1 class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
              Каталог взаимозаменяемых запчастей
            </h1>
            <p class="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
              Профессиональная система поиска идентичных и совместимых запчастей от различных производителей
            </p>
          </div>
          <div class="w-full max-w-md space-y-2">
            <SearchForm client:load placeholder="Найти запчасть..." />
          </div>
        </div>
      </div>
    </section>

    <!-- Статистика -->
    <section class="w-full py-12 md:py-24 lg:py-32 bg-muted/20">
      <div class="container px-4 md:mx-auto">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
          <div class="space-y-2">
            <h3 class="text-3xl font-bold">{stats.parts.toLocaleString('ru-RU')}</h3>
            <p class="text-muted-foreground">Групп запчастей</p>
          </div>
          <div class="space-y-2">
            <h3 class="text-3xl font-bold">{stats.categories.toLocaleString('ru-RU')}</h3>
            <p class="text-muted-foreground">Категорий</p>
          </div>
          <div class="space-y-2">
            <h3 class="text-3xl font-bold">{stats.brands.toLocaleString('ru-RU')}</h3>
            <p class="text-muted-foreground">Брендов</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Популярные категории -->
    {featuredCategories.length > 0 && (
      <section class="w-full py-12 md:py-24 lg:py-32">
        <div class="container px-4 md:mx-auto">
          <div class="flex flex-col items-center space-y-4 text-center">
            <div class="space-y-2">
              <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Основные категории
              </h2>
              <p class="mx-auto max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Выберите категорию для поиска нужных запчастей
              </p>
            </div>
          </div>
          <div class="mx-auto grid max-w-5xl grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
            {featuredCategories.map((category) => (
              <CategoryCard category={category} client:load />
            ))}
          </div>
          <div class="flex justify-center mt-8">
            <a href="/categories" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
              Все категории
            </a>
          </div>
        </div>
      </section>
    )}

    <!-- Недавно обновленные запчасти -->
    {recentParts.length > 0 && (
      <section class="w-full py-12 md:py-24 lg:py-32 bg-muted/20">
        <div class="container px-4 md:mx-auto">
          <div class="flex flex-col items-center space-y-4 text-center">
            <div class="space-y-2">
              <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Недавно обновленные
              </h2>
              <p class="mx-auto max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Последние изменения в каталоге запчастей
              </p>
            </div>
          </div>
          <div class="mx-auto grid max-w-6xl grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-8">
            {recentParts.map((part) => (
              <PartCard part={part} client:load />
            ))}
          </div>
          <div class="flex justify-center gap-4 mt-8">
            <a href="/catalog" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-9 px-4 py-2">
              Открыть каталог
            </a>
            <a href="/catalog-search" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
              Профессиональный поиск
            </a>
          </div>
        </div>
      </section>
    )}
  </TrpcProvider>
</MainLayout>