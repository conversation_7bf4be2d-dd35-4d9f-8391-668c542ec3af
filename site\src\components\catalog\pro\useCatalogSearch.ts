"use client"

import { useMemo, useState } from "react"
import { trpc } from "@/lib/trpc"
import type { CatalogSearchFilters, PartApplicability } from "@/types/catalog"

function buildCatalogItemWhere(filters: CatalogSearchFilters) {
  const where: any = {}

  // Текстовый поиск по SKU/description
  if (filters.query && filters.query.trim()) {
    const q = filters.query.trim()
    where.OR = [
      { sku: { contains: q, mode: "insensitive" } },
      { description: { contains: q, mode: "insensitive" } },
    ]
  }

  // Бренды
  if (filters.brandIds && filters.brandIds.length > 0) {
    where.brandId = { in: filters.brandIds }
  }

  // Только OEM
  if (filters.isOemOnly) {
    where.brand = { isOem: true }
  }

  // Категории через применимости -> part.partCategoryId
  if (filters.categoryIds && filters.categoryIds.length > 0) {
    where.applicabilities = {
      some: { part: { partCategoryId: { in: filters.categoryIds } } },
    }
  }

  // Точность применимости (если выбрано) — фильтруем по связанной таблице применимости
  if (filters.accuracyLevels && filters.accuracyLevels.length > 0) {
    where.applicabilities = {
      ...(where.applicabilities || {}),
      some: {
        ...(where.applicabilities?.some || {}),
        accuracy: { in: filters.accuracyLevels as string[] },
      },
    }
  }

  // Атрибуты CatalogItem
  if (filters.attributeFilters && Object.keys(filters.attributeFilters).length > 0) {
    const andConditions: any[] = []
    for (const [templateIdStr, filter] of Object.entries(filters.attributeFilters)) {
      const templateId = Number.parseInt(templateIdStr)
      const cond: any = { templateId }
      if (filter.values && filter.values.length > 0) {
        cond.value = { in: filter.values }
      }
      if (filter.numericRange && filter.numericRange.length === 2) {
        const [min, max] = filter.numericRange
        cond.numericValue = { gte: min, lte: max }
      }
      andConditions.push(cond)
    }
    if (andConditions.length > 0) {
      where.attributes = { some: { AND: andConditions } }
    }
  }

  return where
}

export function useCatalogSearch() {
  const [filters, setFilters] = useState<CatalogSearchFilters>({
    query: "",
    categoryIds: [],
    brandIds: [],
    attributeFilters: {},
    accuracyLevels: [],
    isOemOnly: false,
  })

  const where = useMemo(() => buildCatalogItemWhere(filters), [filters])

  // Загружаем данные через изолированный site API
  const { data: searchResult, isLoading } = trpc.site.search.catalogItems.useQuery({
    search: filters.query || undefined,
    brandIds: filters.brandIds,
    categoryIds: filters.categoryIds,
    isOemOnly: filters.isOemOnly,
    accuracy: (filters.accuracyLevels as any) || undefined,
    attributeFilters: Object.entries(filters.attributeFilters || {}).map(([templateId, f]) => ({
      templateId: Number(templateId),
      values: f.values?.length ? f.values : undefined,
      minValue: f.numericRange?.[0],
      maxValue: f.numericRange?.[1],
    })),
    limit: 50,
    offset: 0,
    sortBy: 'updatedAt',
    sortDir: 'desc',
  })

  // Преобразуем CatalogItem[] -> PartApplicability[] для совместимости с UI
  const results: PartApplicability[] = useMemo(() => {
    if (!searchResult?.items) return []
    const list: PartApplicability[] = []
    for (const ci of searchResult.items as any[]) {
      if (!ci.applicabilities) continue
      for (const ap of ci.applicabilities) {
        list.push({
          id: ap.id,
          partId: ap.partId,
          catalogItemId: ap.catalogItemId,
          accuracy: ap.accuracy,
          notes: ap.notes,
          part: ap.part,
          catalogItem: {
            id: ci.id,
            sku: ci.sku,
            description: ci.description,
            brandId: ci.brandId,
            brand: ci.brand,
            isPublic: true,
            attributes: ci.attributes,
            image: ci.image ?? null,
            mediaAssets: ci.mediaAssets ?? [],
          },
        })
      }
    }
    return list
  }, [searchResult])

  return {
    results,
    filters,
    setFilters,
    totalCount: searchResult?.total || 0,
    filteredCount: results.length,
    isLoading,
  }
}

