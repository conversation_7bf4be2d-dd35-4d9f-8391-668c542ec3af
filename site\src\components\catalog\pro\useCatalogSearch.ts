"use client"

import { useMemo } from "react"
import { trpc } from "@/lib/trpc"
import { useCatalogGlobalState } from "@/lib/catalog-state"
import { useDebounce } from "@/hooks/useDebounce"
import type { CatalogSearchFilters, PartApplicability } from "@/types/catalog"

// Хук для использования в отдельных компонентах
export function useCatalogSearch() {
  const { filters, setFilters, updateFilters, clearFilters } = useCatalogGlobalState()

  // Дебаунсинг для поискового запроса
  const debouncedQuery = useDebounce(filters.query, 300)

  // Загружаем данные через изолированный site API
  const { data: searchResult, isLoading } = trpc.site.search.catalogItems.useQuery({
    search: debouncedQuery || undefined,
    brandIds: filters.brandIds,
    categoryIds: filters.categoryIds,
    isOemOnly: filters.isOemOnly,
    accuracy: (filters.accuracyLevels as any) || undefined,
    attributeFilters: Object.entries(filters.attributeFilters || {}).map(([templateId, f]) => ({
      templateId: Number(templateId),
      values: f.values?.length ? f.values : undefined,
      minValue: f.numericRange?.[0],
      maxValue: f.numericRange?.[1],
    })),
    limit: 50,
    offset: 0,
    sortBy: 'updatedAt',
    sortDir: 'desc',
  }, {
    staleTime: 30_000, // 30 секунд для результатов поиска
    gcTime: 5 * 60_000, // 5 минут в кэше
  })

  // Преобразуем CatalogItem[] -> PartApplicability[] для совместимости с UI
  const results: PartApplicability[] = useMemo(() => {
    if (!searchResult?.items) return []
    const list: PartApplicability[] = []
    for (const ci of searchResult.items as any[]) {
      if (!ci.applicabilities) continue
      for (const ap of ci.applicabilities) {
        list.push({
          id: ap.id,
          partId: ap.partId,
          catalogItemId: ap.catalogItemId,
          accuracy: ap.accuracy,
          notes: ap.notes,
          part: ap.part,
          catalogItem: {
            id: ci.id,
            sku: ci.sku,
            description: ci.description,
            brandId: ci.brandId,
            brand: ci.brand,
            isPublic: true,
            attributes: ci.attributes,
            image: ci.image ?? null,
            mediaAssets: ci.mediaAssets ?? [],
          },
        })
      }
    }
    return list
  }, [searchResult])

  const availableAttributeValues = useMemo(() => {
    const values: Record<number, string[]> = {}
    const numericStats: Record<number, { min: number; max: number; avg: number }> = {}

    results.forEach((applicability) => {
      applicability.catalogItem.attributes.forEach((attr) => {
        if (attr.template.dataType === "NUMBER" && attr.numericValue !== undefined && attr.numericValue !== null) {
          if (!numericStats[attr.templateId]) {
            numericStats[attr.templateId] = { min: attr.numericValue, max: attr.numericValue, avg: 0 }
          } else {
            numericStats[attr.templateId].min = Math.min(numericStats[attr.templateId].min, attr.numericValue)
            numericStats[attr.templateId].max = Math.max(numericStats[attr.templateId].max, attr.numericValue)
          }
        } else {
          if (!values[attr.templateId]) values[attr.templateId] = []
          if (!values[attr.templateId].includes(attr.value)) values[attr.templateId].push(attr.value)
        }
      })
    })

    Object.keys(numericStats).forEach((templateId) => {
      const templateIdNum = Number.parseInt(templateId)
      const allValues = results
        .flatMap((r) => r.catalogItem.attributes)
        .filter((attr) => attr.templateId === templateIdNum && attr.numericValue !== undefined && attr.numericValue !== null)
        .map((attr) => attr.numericValue as number)

      if (allValues.length > 0) {
        numericStats[templateIdNum].avg = allValues.reduce((sum, val) => sum + val, 0) / allValues.length
      }
    })

    return { values, numericStats }
  }, [results])

  return {
    filters,
    setFilters,
    updateFilters,
    clearFilters,
    results,
    totalCount: searchResult?.total || 0,
    filteredCount: results.length,
    isLoading,
    availableAttributeValues,
  }
}

