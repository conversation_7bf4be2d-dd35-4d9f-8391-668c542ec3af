import{j as t}from"./jsx-runtime.D_zvdyIk.js";import{r as u}from"./index.GzzYzsTh.js";import{I as p}from"./input.BvZ2Z-in.js";import{B as f}from"./button.CYGhVy7y.js";import{n as x}from"./router.WLLD8StG.js";import{S as d}from"./search.C9RwVH3H.js";import"./utils.CBfrqCZ4.js";import"./index.3rXK4OIH.js";function N({onSearch:s,placeholder:m="Поиск запчастей...",defaultValue:n=""}){const[o,i]=u.useState(n),l=e=>{const r=e.trim();r&&(s?s(r):x(`/catalog?search=${encodeURIComponent(r)}`))},a=e=>{e.preventDefault(),l(o)},c=e=>{e.key==="Enter"&&a(e)};return t.jsxs("form",{onSubmit:a,className:"flex w-full max-w-sm items-center space-x-2",children:[t.jsxs("div",{className:"relative flex-1",children:[t.jsx(d,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),t.jsx(p,{type:"text",placeholder:m,value:o,onChange:e=>i(e.target.value),onKeyDown:c,className:"pl-10"})]}),t.jsx(f,{type:"submit",size:"sm",children:"Найти"})]})}export{N as SearchForm};
