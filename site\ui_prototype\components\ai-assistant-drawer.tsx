"use client"

import { useState, useRef, useEffect } from "react"
import {
  Bot,
  Send,
  Sparkles,
  MessageCircle,
  Lightbulb,
  Search,
  Filter,
  Zap,
  ChevronRight,
  Minimize2,
  Maximize2,
} from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { ModernCard } from "@/components/ui/modern-card"
import { ModernInput } from "@/components/ui/modern-input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet"
import { cn } from "@/lib/utils"
import type { SearchFilters } from "../types/catalog"

interface Message {
  id: string
  type: "user" | "assistant"
  content: string
  timestamp: Date
  suggestions?: string[]
  filterSuggestion?: Partial<SearchFilters>
}

interface AIAssistantDrawerProps {
  isOpen: boolean
  onToggle: () => void
  onApplyFilters: (filters: Partial<SearchFilters>) => void
  currentFilters: SearchFilters
  resultsCount: number
}

const mockMessages: Message[] = [
  {
    id: "1",
    type: "assistant",
    content:
      "Привет! Я ваш AI помощник по поиску уплотнений. Опишите что ищете или задайте вопрос о технических характеристиках.",
    timestamp: new Date(),
    suggestions: [
      "Найти сальник для двигателя",
      "Подобрать уплотнение по размерам",
      "Показать все NBR материалы",
      "Что такое точность применимости?",
    ],
  },
]

const quickActions = [
  {
    icon: Search,
    label: "Поиск по описанию",
    description: "Опишите деталь своими словами",
    prompt: "Помогите найти уплотнение по описанию",
  },
  {
    icon: Filter,
    label: "Умные фильтры",
    description: "Подберу фильтры автоматически",
    prompt: "Настройте фильтры для моего поиска",
  },
  {
    icon: Lightbulb,
    label: "Техническая помощь",
    description: "Объясню параметры и характеристики",
    prompt: "Объясните технические характеристики",
  },
]

export function AIAssistantDrawer({
  isOpen,
  onToggle,
  onApplyFilters,
  currentFilters,
  resultsCount,
}: AIAssistantDrawerProps) {
  const [messages, setMessages] = useState<Message[]>(mockMessages)
  const [inputValue, setInputValue] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (isOpen && !isMinimized) {
      setTimeout(() => inputRef.current?.focus(), 100)
    }
  }, [isOpen, isMinimized])

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInputValue("")
    setIsTyping(true)

    // Simulate AI response
    setTimeout(() => {
      const aiResponse = generateAIResponse(inputValue, currentFilters, resultsCount)
      setMessages((prev) => [...prev, aiResponse])
      setIsTyping(false)
    }, 1500)
  }

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion)
    setTimeout(() => handleSendMessage(), 100)
  }

  const handleQuickAction = (prompt: string) => {
    setInputValue(prompt)
    setTimeout(() => handleSendMessage(), 100)
  }

  const handleApplyFilterSuggestion = (filterSuggestion: Partial<SearchFilters>) => {
    onApplyFilters(filterSuggestion)
  }

  const generateAIResponse = (userInput: string, filters: SearchFilters, count: number): Message => {
    const input = userInput.toLowerCase()

    if (input.includes("сальник") && input.includes("двигател")) {
      return {
        id: Date.now().toString(),
        type: "assistant",
        content: "Отлично! Ищете сальники для двигателя. Я настрою фильтры для более точного поиска:",
        timestamp: new Date(),
        suggestions: ["Какая марка техники?", "Нужны размеры сальника?", "Какой тип двигателя?"],
        filterSuggestion: {
          categoryIds: [1], // Engine seals
          query: "сальник двигатель",
        },
      }
    }

    if (input.includes("размер") || input.includes("диаметр")) {
      return {
        id: Date.now().toString(),
        type: "assistant",
        content:
          "Для точного поиска по размерам укажите внутренний и наружный диаметр. Например: 'внутренний 25мм, наружный 35мм'",
        timestamp: new Date(),
        suggestions: ["25x35x7 мм", "30x42x8 мм", "Показать популярные размеры"],
      }
    }

    if (input.includes("материал") || input.includes("nbr") || input.includes("fkm")) {
      return {
        id: Date.now().toString(),
        type: "assistant",
        content:
          "Материал критически важен! NBR подходит для стандартных применений (-40°C до +100°C), FKM - для высоких температур (до +200°C).",
        timestamp: new Date(),
        filterSuggestion: {
          attributeFilters: {
            3: { values: input.includes("nbr") ? ["NBR"] : input.includes("fkm") ? ["FKM"] : [] },
          },
        },
      }
    }

    return {
      id: Date.now().toString(),
      type: "assistant",
      content: `Понял ваш запрос "${userInput}". Сейчас найдено ${count} позиций. Могу помочь уточнить поиск или объяснить характеристики.`,
      timestamp: new Date(),
      suggestions: ["Уточнить поиск", "Объяснить результаты", "Показать аналоги"],
    }
  }

  return (
    <Sheet open={isOpen} onOpenChange={onToggle}>
      <SheetContent side="right" className="w-[480px] sm:w-[540px] p-0 border-l-2 border-border-strong">
        <div className="flex flex-col h-full">
          {/* Header */}
          <SheetHeader className="px-6 py-4 border-b border-border-strong bg-surface/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-xl gradient-ai flex items-center justify-center shadow-lg">
                  <Bot className="h-5 w-5 text-white" />
                </div>
                <div>
                  <SheetTitle className="text-lg font-bold">AI Ассистент</SheetTitle>
                  <p className="text-sm text-muted-foreground">Умный помощник по поиску</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {resultsCount} результатов
                </Badge>
                <ModernButton
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsMinimized(!isMinimized)}
                  className="h-8 w-8"
                >
                  {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
                </ModernButton>
              </div>
            </div>
          </SheetHeader>

          {!isMinimized ? (
            <>
              {/* Quick Actions */}
              <div className="px-6 py-4 bg-surface/30 border-b border-border">
                <h3 className="text-sm font-semibold mb-3 text-muted-foreground uppercase tracking-wide">
                  Быстрые действия
                </h3>
                <div className="grid grid-cols-1 gap-2">
                  {quickActions.map((action, index) => (
                    <ModernButton
                      key={index}
                      variant="ghost"
                      className="h-auto p-3 justify-start text-left hover:bg-surface-hover transition-colors"
                      onClick={() => handleQuickAction(action.prompt)}
                    >
                      <div className="h-8 w-8 rounded-lg bg-ai-primary/10 flex items-center justify-center mr-3">
                        <action.icon className="h-4 w-4 text-ai-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-sm">{action.label}</div>
                        <div className="text-xs text-muted-foreground">{action.description}</div>
                      </div>
                      <ChevronRight className="h-4 w-4 text-muted-foreground" />
                    </ModernButton>
                  ))}
                </div>
              </div>

              {/* Messages */}
              <ScrollArea className="flex-1 px-6 py-4">
                <div className="space-y-6">
                  {messages.map((message) => (
                    <div key={message.id} className="space-y-3 animate-slide-in-right">
                      <div className={cn("flex gap-3", message.type === "user" ? "justify-end" : "justify-start")}>
                        {message.type === "assistant" && (
                          <div className="h-8 w-8 rounded-full gradient-ai flex items-center justify-center flex-shrink-0 shadow-md">
                            <Bot className="h-4 w-4 text-white" />
                          </div>
                        )}
                        <div
                          className={cn(
                            "max-w-[85%] rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm",
                            message.type === "user" ? "ai-message-user ml-auto" : "ai-message-assistant",
                          )}
                        >
                          {message.content}
                        </div>
                      </div>

                      {/* Suggestions */}
                      {message.suggestions && (
                        <div className="flex flex-wrap gap-2 ml-11">
                          {message.suggestions.map((suggestion, index) => (
                            <ModernButton
                              key={index}
                              variant="outline"
                              size="sm"
                              className="h-8 text-xs ai-suggestion"
                              onClick={() => handleSuggestionClick(suggestion)}
                            >
                              {suggestion}
                            </ModernButton>
                          ))}
                        </div>
                      )}

                      {/* Filter Suggestion */}
                      {message.filterSuggestion && (
                        <div className="ml-11">
                          <ModernCard variant="default" className="p-4 border-ai-primary/20 bg-ai-primary/5">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="h-8 w-8 rounded-lg bg-ai-primary/20 flex items-center justify-center">
                                  <Zap className="h-4 w-4 text-ai-primary" />
                                </div>
                                <div>
                                  <span className="text-sm font-medium">Применить умные фильтры</span>
                                  <p className="text-xs text-muted-foreground">Настроено на основе вашего запроса</p>
                                </div>
                              </div>
                              <ModernButton
                                size="sm"
                                className="gradient-ai text-white shadow-md"
                                onClick={() => handleApplyFilterSuggestion(message.filterSuggestion!)}
                              >
                                Применить
                              </ModernButton>
                            </div>
                          </ModernCard>
                        </div>
                      )}
                    </div>
                  ))}

                  {/* Typing Indicator */}
                  {isTyping && (
                    <div className="flex gap-3 animate-slide-in-right">
                      <div className="h-8 w-8 rounded-full gradient-ai flex items-center justify-center flex-shrink-0 shadow-md">
                        <Bot className="h-4 w-4 text-white" />
                      </div>
                      <div className="ai-message-assistant rounded-2xl px-4 py-3 text-sm">
                        <div className="flex items-center gap-2">
                          <div className="flex gap-1">
                            <div
                              className="w-2 h-2 bg-ai-primary rounded-full animate-bounce"
                              style={{ animationDelay: "0ms" }}
                            />
                            <div
                              className="w-2 h-2 bg-ai-primary rounded-full animate-bounce"
                              style={{ animationDelay: "150ms" }}
                            />
                            <div
                              className="w-2 h-2 bg-ai-primary rounded-full animate-bounce"
                              style={{ animationDelay: "300ms" }}
                            />
                          </div>
                          <span className="text-muted-foreground">Анализирую запрос...</span>
                        </div>
                      </div>
                    </div>
                  )}

                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              {/* Input */}
              <div className="p-6 border-t border-border-strong bg-surface/30">
                <div className="flex gap-3">
                  <ModernInput
                    ref={inputRef}
                    placeholder="Опишите что ищете или задайте вопрос..."
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault()
                        handleSendMessage()
                      }
                    }}
                    className="flex-1 h-12"
                    variant="filled"
                  />
                  <ModernButton
                    onClick={handleSendMessage}
                    disabled={!inputValue.trim() || isTyping}
                    size="icon"
                    className="h-12 w-12 gradient-ai text-white shadow-md"
                  >
                    <Send className="h-5 w-5" />
                  </ModernButton>
                </div>
                <div className="flex items-center gap-2 mt-3 text-xs text-muted-foreground">
                  <Sparkles className="h-3 w-3 text-ai-primary" />
                  <span>AI поможет найти нужные детали и настроить фильтры автоматически</span>
                </div>
              </div>
            </>
          ) : (
            <div className="p-6 flex items-center justify-center h-full">
              <div className="text-center">
                <div className="h-16 w-16 rounded-full gradient-ai flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <MessageCircle className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold mb-2">Чат свернут</h3>
                <p className="text-sm text-muted-foreground mb-4">{messages.length - 1} сообщений в истории</p>
                <ModernButton variant="outline" onClick={() => setIsMinimized(false)}>
                  Развернуть чат
                </ModernButton>
              </div>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  )
}
