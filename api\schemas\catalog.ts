import { z } from 'zod'
import { PartSchema } from '../generated/zod/models/Part.schema'
import { PartAttributeSchema } from '../generated/zod/models/PartAttribute.schema'
import { AttributeTemplateSchema } from '../generated/zod/models/AttributeTemplate.schema'
import { MediaAssetSchema } from '../generated/zod/models/MediaAsset.schema'
import { PartCategorySchema } from '../generated/zod/models/PartCategory.schema'

// DTO для списка запчастей, собранный ИСКЛЮЧИТЕЛЬНО из модельных Zod-схем
export const PartListItemDto = PartSchema.pick({
  id: true,
  name: true,
  path: true,
  updatedAt: true,
  partCategory: true,
  image: true,
  attributes: true,
  applicabilities: true,
  equipmentApplicabilities: true,
}).extend({
  image: MediaAssetSchema.pick({ id: true, url: true }).nullable().optional(),
  partCategory: PartCategorySchema.pick({ id: true, name: true }),
  attributes: z
    .array(
      PartAttributeSchema.pick({ id: true, value: true, numericValue: true }).extend({
        template: AttributeTemplateSchema.pick({ id: true, name: true, title: true, dataType: true, unit: true }),
      }),
    )
    .default([]),
  applicabilities: z.array(z.object({ id: z.number() })).optional(),
  equipmentApplicabilities: z.array(z.object({ id: z.number() })).optional(),
})

