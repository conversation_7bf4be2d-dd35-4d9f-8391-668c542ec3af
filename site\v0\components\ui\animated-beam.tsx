"use client"

import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import React, { forwardRef, useEffect, useState } from "react"

export interface AnimatedBeamProps {
  className?: string
  containerRef: React.RefObject<HTMLElement>
  fromRef: React.RefObject<HTMLElement>
  toRef: React.RefObject<HTMLElement>
  curvature?: number
  reverse?: boolean
  duration?: number
  delay?: number
}

export const AnimatedBeam = forwardRef<SVGSVGElement, AnimatedBeamProps>(
  ({ className, containerRef, fromRef, toRef, curvature = 0, reverse = false, duration = 2, delay = 0 }, ref) => {
    const id = React.useId()
    const [pathD, setPathD] = useState("")
    const [svgDimensions, setSvgDimensions] = useState({ width: 0, height: 0 })

    useEffect(() => {
      const calculatePath = () => {
        if (containerRef.current && fromRef.current && toRef.current) {
          const containerRect = containerRef.current.getBoundingClientRect()
          const rectA = fromRef.current.getBoundingClientRect()
          const rectB = toRef.current.getBoundingClientRect()

          const svgWidth = containerRect.width
          const svgHeight = containerRect.height
          setSvgDimensions({ width: svgWidth, height: svgHeight })

          const startX = rectA.left - containerRect.left + rectA.width / 2
          const startY = rectA.top - containerRect.top + rectA.height / 2
          const endX = rectB.left - containerRect.left + rectB.width / 2
          const endY = rectB.top - containerRect.top + rectB.height / 2

          const controlPointX = startX + (endX - startX) / 2
          const controlPointY = startY + (endY - startY) / 2 + curvature

          const d = `M ${startX},${startY} Q ${controlPointX},${controlPointY} ${endX},${endY}`
          setPathD(d)
        }
      }

      calculatePath()
      const timeoutId = setTimeout(calculatePath, 100)

      return () => clearTimeout(timeoutId)
    }, [containerRef, fromRef, toRef, curvature])

    return (
      <svg
        ref={ref}
        className={cn("pointer-events-none absolute left-0 top-0", className)}
        width={svgDimensions.width}
        height={svgDimensions.height}
        viewBox={`0 0 ${svgDimensions.width} ${svgDimensions.height}`}
        style={{ overflow: "visible" }}
      >
        <defs>
          <linearGradient id={`${id}-gradient`} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#18CCFC" stopOpacity="0" />
            <stop offset="50%" stopColor="#18CCFC" stopOpacity="1" />
            <stop offset="100%" stopColor="#6344F5" stopOpacity="0" />
          </linearGradient>
        </defs>

        <motion.path
          d={pathD}
          stroke={`url(#${id}-gradient)`}
          strokeWidth="2"
          fill="none"
          initial={{
            strokeDasharray: "0 100",
          }}
          animate={{
            strokeDasharray: reverse ? "0 100" : "100 0",
          }}
          transition={{
            duration,
            delay,
            ease: "easeInOut",
            repeat: Number.POSITIVE_INFINITY,
            repeatType: "loop",
            repeatDelay: 1,
          }}
        />
      </svg>
    )
  },
)

AnimatedBeam.displayName = "AnimatedBeam"
