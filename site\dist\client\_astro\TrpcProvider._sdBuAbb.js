import{j as Ae}from"./jsx-runtime.D_zvdyIk.js";import{r as _,R as Hr}from"./index.GzzYzsTh.js";var fe=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},ce=typeof window>"u"||"Deno"in globalThis;function B(){}function zr(e,t){return typeof e=="function"?e(t):e}function et(e){return typeof e=="number"&&e>=0&&e!==1/0}function Nt(e,t){return Math.max(e+(t||0)-Date.now(),0)}function ne(e,t){return typeof e=="function"?e(t):e}function W(e,t){return typeof e=="function"?e(t):e}function wt(e,t){const{type:r="all",exact:s,fetchStatus:n,predicate:i,queryKey:o,stale:a}=e;if(o){if(s){if(t.queryHash!==ht(o,t.options))return!1}else if(!we(t.queryKey,o))return!1}if(r!=="all"){const u=t.isActive();if(r==="active"&&!u||r==="inactive"&&u)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||n&&n!==t.state.fetchStatus||i&&!i(t))}function Pt(e,t){const{exact:r,status:s,predicate:n,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(r){if(se(t.options.mutationKey)!==se(i))return!1}else if(!we(t.options.mutationKey,i))return!1}return!(s&&t.state.status!==s||n&&!n(t))}function ht(e,t){return(t?.queryKeyHashFn||se)(e)}function se(e){return JSON.stringify(e,(t,r)=>tt(r)?Object.keys(r).sort().reduce((s,n)=>(s[n]=r[n],s),{}):r)}function we(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(r=>we(e[r],t[r])):!1}function pt(e,t){if(e===t)return e;const r=xt(e)&&xt(t);if(r||tt(e)&&tt(t)){const s=r?e:Object.keys(e),n=s.length,i=r?t:Object.keys(t),o=i.length,a=r?[]:{},u=new Set(s);let c=0;for(let f=0;f<o;f++){const l=r?f:i[f];(!r&&u.has(l)||r)&&e[l]===void 0&&t[l]===void 0?(a[l]=void 0,c++):(a[l]=pt(e[l],t[l]),a[l]===e[l]&&e[l]!==void 0&&c++)}return n===o&&c===n?e:a}return t}function ke(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}function xt(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function tt(e){if(!St(e))return!1;const t=e.constructor;if(t===void 0)return!0;const r=t.prototype;return!(!St(r)||!r.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function St(e){return Object.prototype.toString.call(e)==="[object Object]"}function Vr(e){return new Promise(t=>{setTimeout(t,e)})}function rt(e,t,r){return typeof r.structuralSharing=="function"?r.structuralSharing(e,t):r.structuralSharing!==!1?pt(e,t):t}function Wr(e,t,r=0){const s=[...e,t];return r&&s.length>r?s.slice(1):s}function Gr(e,t,r=0){const s=[t,...e];return r&&s.length>r?s.slice(0,-1):s}var z=Symbol();function Lt(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:!e.queryFn||e.queryFn===z?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}function Kt(e,t){return typeof e=="function"?e(...t):!!e}var Yr=class extends fe{#t;#e;#r;constructor(){super(),this.#r=e=>{if(!ce&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#e||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(e){this.#r=e,this.#e?.(),this.#e=e(t=>{typeof t=="boolean"?this.setFocused(t):this.onFocus()})}setFocused(e){this.#t!==e&&(this.#t=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return typeof this.#t=="boolean"?this.#t:globalThis.document?.visibilityState!=="hidden"}},yt=new Yr,Jr=class extends fe{#t=!0;#e;#r;constructor(){super(),this.#r=e=>{if(!ce&&window.addEventListener){const t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#e||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(e){this.#r=e,this.#e?.(),this.#e=e(this.setOnline.bind(this))}setOnline(e){this.#t!==e&&(this.#t=e,this.listeners.forEach(r=>{r(e)}))}isOnline(){return this.#t}},Fe=new Jr;function nt(){let e,t;const r=new Promise((n,i)=>{e=n,t=i});r.status="pending",r.catch(()=>{});function s(n){Object.assign(r,n),delete r.resolve,delete r.reject}return r.resolve=n=>{s({status:"fulfilled",value:n}),e(n)},r.reject=n=>{s({status:"rejected",reason:n}),t(n)},r}function Xr(e){return Math.min(1e3*2**e,3e4)}function Bt(e){return(e??"online")==="online"?Fe.isOnline():!0}var Ht=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function zt(e){let t=!1,r=0,s;const n=nt(),i=()=>n.status!=="pending",o=h=>{i()||(p(new Ht(h)),e.abort?.())},a=()=>{t=!0},u=()=>{t=!1},c=()=>yt.isFocused()&&(e.networkMode==="always"||Fe.isOnline())&&e.canRun(),f=()=>Bt(e.networkMode)&&e.canRun(),l=h=>{i()||(s?.(),n.resolve(h))},p=h=>{i()||(s?.(),n.reject(h))},v=()=>new Promise(h=>{s=R=>{(i()||c())&&h(R)},e.onPause?.()}).then(()=>{s=void 0,i()||e.onContinue?.()}),b=()=>{if(i())return;let h;const R=r===0?e.initialPromise:void 0;try{h=R??e.fn()}catch(j){h=Promise.reject(j)}Promise.resolve(h).then(l).catch(j=>{if(i())return;const I=e.retry??(ce?0:3),J=e.retryDelay??Xr,re=typeof J=="function"?J(r,j):J,O=I===!0||typeof I=="number"&&r<I||typeof I=="function"&&I(r,j);if(t||!O){p(j);return}r++,e.onFail?.(r,j),Vr(re).then(()=>c()?void 0:v()).then(()=>{t?p(j):b()})})};return{promise:n,status:()=>n.status,cancel:o,continue:()=>(s?.(),n),cancelRetry:a,continueRetry:u,canStart:f,start:()=>(f()?b():v().then(b),n)}}var Zr=e=>setTimeout(e,0);function en(){let e=[],t=0,r=a=>{a()},s=a=>{a()},n=Zr;const i=a=>{t?e.push(a):n(()=>{r(a)})},o=()=>{const a=e;e=[],a.length&&n(()=>{s(()=>{a.forEach(u=>{r(u)})})})};return{batch:a=>{let u;t++;try{u=a()}finally{t--,t||o()}return u},batchCalls:a=>(...u)=>{i(()=>{a(...u)})},schedule:i,setNotifyFunction:a=>{r=a},setBatchNotifyFunction:a=>{s=a},setScheduler:a=>{n=a}}}var U=en(),Vt=class{#t;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),et(this.gcTime)&&(this.#t=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(ce?1/0:300*1e3))}clearGcTimeout(){this.#t&&(clearTimeout(this.#t),this.#t=void 0)}},tn=class extends Vt{#t;#e;#r;#n;#s;#i;#a;constructor(e){super(),this.#a=!1,this.#i=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#n=e.client,this.#r=this.#n.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#t=rn(this.options),this.state=e.state??this.#t,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#s?.promise}setOptions(e){this.options={...this.#i,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&this.#r.remove(this)}setData(e,t){const r=rt(this.state.data,e,this.options);return this.#o({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#o({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#s?.promise;return this.#s?.cancel(e),t?t.then(B).catch(B):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#t)}isActive(){return this.observers.some(e=>W(e.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===z||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(e=>ne(e.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(e=0){return this.state.data===void 0?!0:e==="static"?!1:this.state.isInvalidated?!0:!Nt(this.state.dataUpdatedAt,e)}onFocus(){this.observers.find(t=>t.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),this.#s?.continue()}onOnline(){this.observers.find(t=>t.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),this.#s?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#r.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#s&&(this.#a?this.#s.cancel({revert:!0}):this.#s.cancelRetry()),this.scheduleGc()),this.#r.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#o({type:"invalidate"})}async fetch(e,t){if(this.state.fetchStatus!=="idle"&&this.#s?.status()!=="rejected"){if(this.state.data!==void 0&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#s)return this.#s.continueRetry(),this.#s.promise}if(e&&this.setOptions(e),!this.options.queryFn){const a=this.observers.find(u=>u.options.queryFn);a&&this.setOptions(a.options)}const r=new AbortController,s=a=>{Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(this.#a=!0,r.signal)})},n=()=>{const a=Lt(this.options,t),c=(()=>{const f={client:this.#n,queryKey:this.queryKey,meta:this.meta};return s(f),f})();return this.#a=!1,this.options.persister?this.options.persister(a,c,this):a(c)},o=(()=>{const a={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#n,state:this.state,fetchFn:n};return s(a),a})();this.options.behavior?.onFetch(o,this),this.#e=this.state,(this.state.fetchStatus==="idle"||this.state.fetchMeta!==o.fetchOptions?.meta)&&this.#o({type:"fetch",meta:o.fetchOptions?.meta}),this.#s=zt({initialPromise:t?.initialPromise,fn:o.fetchFn,abort:r.abort.bind(r),onFail:(a,u)=>{this.#o({type:"failed",failureCount:a,error:u})},onPause:()=>{this.#o({type:"pause"})},onContinue:()=>{this.#o({type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0});try{const a=await this.#s.start();if(a===void 0)throw new Error(`${this.queryHash} data is undefined`);return this.setData(a),this.#r.config.onSuccess?.(a,this),this.#r.config.onSettled?.(a,this.state.error,this),a}catch(a){if(a instanceof Ht){if(a.silent)return this.#s.promise;if(a.revert)return this.setState({...this.#e,fetchStatus:"idle"}),this.state.data}throw this.#o({type:"error",error:a}),this.#r.config.onError?.(a,this),this.#r.config.onSettled?.(this.state.data,a,this),a}finally{this.scheduleGc()}}#o(e){const t=r=>{switch(e.type){case"failed":return{...r,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...Wt(r.data,this.options),fetchMeta:e.meta??null};case"success":const s={...r,data:e.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};return this.#e=e.manual?s:void 0,s;case"error":const n=e.error;return{...r,error:n,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...e.state}}};this.state=t(this.state),U.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),this.#r.notify({query:this,type:"updated",action:e})})}};function Wt(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Bt(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function rn(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,r=t!==void 0,s=r?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}var nn=class extends fe{constructor(e={}){super(),this.config=e,this.#t=new Map}#t;build(e,t,r){const s=t.queryKey,n=t.queryHash??ht(s,t);let i=this.get(n);return i||(i=new tn({client:e,queryKey:s,queryHash:n,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(s)}),this.add(i)),i}add(e){this.#t.has(e.queryHash)||(this.#t.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#t.get(e.queryHash);t&&(e.destroy(),t===e&&this.#t.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){U.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#t.get(e)}getAll(){return[...this.#t.values()]}find(e){const t={exact:!0,...e};return this.getAll().find(r=>wt(t,r))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter(r=>wt(e,r)):t}notify(e){U.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){U.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){U.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},sn=class extends Vt{#t;#e;#r;constructor(e){super(),this.mutationId=e.mutationId,this.#e=e.mutationCache,this.#t=[],this.state=e.state||Gt(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#t.includes(e)||(this.#t.push(e),this.clearGcTimeout(),this.#e.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#t=this.#t.filter(t=>t!==e),this.scheduleGc(),this.#e.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#t.length||(this.state.status==="pending"?this.scheduleGc():this.#e.remove(this))}continue(){return this.#r?.continue()??this.execute(this.state.variables)}async execute(e){const t=()=>{this.#n({type:"continue"})};this.#r=zt({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(n,i)=>{this.#n({type:"failed",failureCount:n,error:i})},onPause:()=>{this.#n({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#e.canRun(this)});const r=this.state.status==="pending",s=!this.#r.canStart();try{if(r)t();else{this.#n({type:"pending",variables:e,isPaused:s}),await this.#e.config.onMutate?.(e,this);const i=await this.options.onMutate?.(e);i!==this.state.context&&this.#n({type:"pending",context:i,variables:e,isPaused:s})}const n=await this.#r.start();return await this.#e.config.onSuccess?.(n,e,this.state.context,this),await this.options.onSuccess?.(n,e,this.state.context),await this.#e.config.onSettled?.(n,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(n,null,e,this.state.context),this.#n({type:"success",data:n}),n}catch(n){try{throw await this.#e.config.onError?.(n,e,this.state.context,this),await this.options.onError?.(n,e,this.state.context),await this.#e.config.onSettled?.(void 0,n,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,n,e,this.state.context),n}finally{this.#n({type:"error",error:n})}}finally{this.#e.runNext(this)}}#n(e){const t=r=>{switch(e.type){case"failed":return{...r,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...r,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:e.error,failureCount:r.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}};this.state=t(this.state),U.batch(()=>{this.#t.forEach(r=>{r.onMutationUpdate(e)}),this.#e.notify({mutation:this,type:"updated",action:e})})}};function Gt(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var on=class extends fe{constructor(e={}){super(),this.config=e,this.#t=new Set,this.#e=new Map,this.#r=0}#t;#e;#r;build(e,t,r){const s=new sn({mutationCache:this,mutationId:++this.#r,options:e.defaultMutationOptions(t),state:r});return this.add(s),s}add(e){this.#t.add(e);const t=Ee(e);if(typeof t=="string"){const r=this.#e.get(t);r?r.push(e):this.#e.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#t.delete(e)){const t=Ee(e);if(typeof t=="string"){const r=this.#e.get(t);if(r)if(r.length>1){const s=r.indexOf(e);s!==-1&&r.splice(s,1)}else r[0]===e&&this.#e.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){const t=Ee(e);if(typeof t=="string"){const s=this.#e.get(t)?.find(n=>n.state.status==="pending");return!s||s===e}else return!0}runNext(e){const t=Ee(e);return typeof t=="string"?this.#e.get(t)?.find(s=>s!==e&&s.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){U.batch(()=>{this.#t.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#t.clear(),this.#e.clear()})}getAll(){return Array.from(this.#t)}find(e){const t={exact:!0,...e};return this.getAll().find(r=>Pt(t,r))}findAll(e={}){return this.getAll().filter(t=>Pt(e,t))}notify(e){U.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){const e=this.getAll().filter(t=>t.state.isPaused);return U.batch(()=>Promise.all(e.map(t=>t.continue().catch(B))))}};function Ee(e){return e.options.scope?.id}function Ue(e){return{onFetch:(t,r)=>{const s=t.options,n=t.fetchOptions?.meta?.fetchMore?.direction,i=t.state.data?.pages||[],o=t.state.data?.pageParams||[];let a={pages:[],pageParams:[]},u=0;const c=async()=>{let f=!1;const l=b=>{Object.defineProperty(b,"signal",{enumerable:!0,get:()=>(t.signal.aborted?f=!0:t.signal.addEventListener("abort",()=>{f=!0}),t.signal)})},p=Lt(t.options,t.fetchOptions),v=async(b,h,R)=>{if(f)return Promise.reject();if(h==null&&b.pages.length)return Promise.resolve(b);const I=(()=>{const g={client:t.client,queryKey:t.queryKey,pageParam:h,direction:R?"backward":"forward",meta:t.options.meta};return l(g),g})(),J=await p(I),{maxPages:re}=t.options,O=R?Gr:Wr;return{pages:O(b.pages,J,re),pageParams:O(b.pageParams,h,re)}};if(n&&i.length){const b=n==="backward",h=b?Yt:st,R={pages:i,pageParams:o},j=h(s,R);a=await v(R,j,b)}else{const b=e??i.length;do{const h=u===0?o[0]??s.initialPageParam:st(s,a);if(u>0&&h==null)break;a=await v(a,h),u++}while(u<b)}return a};t.options.persister?t.fetchFn=()=>t.options.persister?.(c,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=c}}}function st(e,{pages:t,pageParams:r}){const s=t.length-1;return t.length>0?e.getNextPageParam(t[s],t,r[s],r):void 0}function Yt(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}function an(e,t){return t?st(e,t)!=null:!1}function un(e,t){return!t||!e.getPreviousPageParam?!1:Yt(e,t)!=null}var cn=class{#t;#e;#r;#n;#s;#i;#a;#o;constructor(e={}){this.#t=e.queryCache||new nn,this.#e=e.mutationCache||new on,this.#r=e.defaultOptions||{},this.#n=new Map,this.#s=new Map,this.#i=0}mount(){this.#i++,this.#i===1&&(this.#a=yt.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#t.onFocus())}),this.#o=Fe.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#t.onOnline())}))}unmount(){this.#i--,this.#i===0&&(this.#a?.(),this.#a=void 0,this.#o?.(),this.#o=void 0)}isFetching(e){return this.#t.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#e.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#t.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),r=this.#t.build(this,t),s=r.state.data;return s===void 0?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime(ne(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(s))}getQueriesData(e){return this.#t.findAll(e).map(({queryKey:t,state:r})=>{const s=r.data;return[t,s]})}setQueryData(e,t,r){const s=this.defaultQueryOptions({queryKey:e}),i=this.#t.get(s.queryHash)?.state.data,o=zr(t,i);if(o!==void 0)return this.#t.build(this,s).setData(o,{...r,manual:!0})}setQueriesData(e,t,r){return U.batch(()=>this.#t.findAll(e).map(({queryKey:s})=>[s,this.setQueryData(s,t,r)]))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#t.get(t.queryHash)?.state}removeQueries(e){const t=this.#t;U.batch(()=>{t.findAll(e).forEach(r=>{t.remove(r)})})}resetQueries(e,t){const r=this.#t;return U.batch(()=>(r.findAll(e).forEach(s=>{s.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const r={revert:!0,...t},s=U.batch(()=>this.#t.findAll(e).map(n=>n.cancel(r)));return Promise.all(s).then(B).catch(B)}invalidateQueries(e,t={}){return U.batch(()=>(this.#t.findAll(e).forEach(r=>{r.invalidate()}),e?.refetchType==="none"?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t)))}refetchQueries(e,t={}){const r={...t,cancelRefetch:t.cancelRefetch??!0},s=U.batch(()=>this.#t.findAll(e).filter(n=>!n.isDisabled()&&!n.isStatic()).map(n=>{let i=n.fetch(void 0,r);return r.throwOnError||(i=i.catch(B)),n.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(s).then(B)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const r=this.#t.build(this,t);return r.isStaleByTime(ne(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(B).catch(B)}fetchInfiniteQuery(e){return e.behavior=Ue(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(B).catch(B)}ensureInfiniteQueryData(e){return e.behavior=Ue(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Fe.isOnline()?this.#e.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#t}getMutationCache(){return this.#e}getDefaultOptions(){return this.#r}setDefaultOptions(e){this.#r=e}setQueryDefaults(e,t){this.#n.set(se(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#n.values()],r={};return t.forEach(s=>{we(e,s.queryKey)&&Object.assign(r,s.defaultOptions)}),r}setMutationDefaults(e,t){this.#s.set(se(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#s.values()],r={};return t.forEach(s=>{we(e,s.mutationKey)&&Object.assign(r,s.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#r.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=ht(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===z&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#r.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#t.clear(),this.#e.clear()}},_e=class extends fe{constructor(e,t){super(),this.options=t,this.#t=e,this.#o=null,this.#a=nt(),this.options.experimental_prefetchInRender||this.#a.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#t;#e=void 0;#r=void 0;#n=void 0;#s;#i;#a;#o;#h;#f;#d;#c;#l;#u;#y=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(this.#e.addObserver(this),_t(this.#e,this.options)?this.#p():this.updateResult(),this.#g())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return it(this.#e,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return it(this.#e,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#O(),this.#w(),this.#e.removeObserver(this)}setOptions(e){const t=this.options,r=this.#e;if(this.options=this.#t.defaultQueryOptions(e),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof W(this.options.enabled,this.#e)!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#P(),this.#e.setOptions(this.options),t._defaulted&&!ke(this.options,t)&&this.#t.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#e,observer:this});const s=this.hasListeners();s&&Rt(this.#e,r,this.options,t)&&this.#p(),this.updateResult(),s&&(this.#e!==r||W(this.options.enabled,this.#e)!==W(t.enabled,this.#e)||ne(this.options.staleTime,this.#e)!==ne(t.staleTime,this.#e))&&this.#v();const n=this.#m();s&&(this.#e!==r||W(this.options.enabled,this.#e)!==W(t.enabled,this.#e)||n!==this.#u)&&this.#b(n)}getOptimisticResult(e){const t=this.#t.getQueryCache().build(this.#t,e),r=this.createResult(t,e);return fn(this,r)&&(this.#n=r,this.#i=this.options,this.#s=this.#e.state),r}getCurrentResult(){return this.#n}trackResult(e,t){return new Proxy(e,{get:(r,s)=>(this.trackProp(s),t?.(s),Reflect.get(r,s))})}trackProp(e){this.#y.add(e)}getCurrentQuery(){return this.#e}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#t.defaultQueryOptions(e),r=this.#t.getQueryCache().build(this.#t,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#p({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#n))}#p(e){this.#P();let t=this.#e.fetch(this.options,e);return e?.throwOnError||(t=t.catch(B)),t}#v(){this.#O();const e=ne(this.options.staleTime,this.#e);if(ce||this.#n.isStale||!et(e))return;const r=Nt(this.#n.dataUpdatedAt,e)+1;this.#c=setTimeout(()=>{this.#n.isStale||this.updateResult()},r)}#m(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.#e):this.options.refetchInterval)??!1}#b(e){this.#w(),this.#u=e,!(ce||W(this.options.enabled,this.#e)===!1||!et(this.#u)||this.#u===0)&&(this.#l=setInterval(()=>{(this.options.refetchIntervalInBackground||yt.isFocused())&&this.#p()},this.#u))}#g(){this.#v(),this.#b(this.#m())}#O(){this.#c&&(clearTimeout(this.#c),this.#c=void 0)}#w(){this.#l&&(clearInterval(this.#l),this.#l=void 0)}createResult(e,t){const r=this.#e,s=this.options,n=this.#n,i=this.#s,o=this.#i,u=e!==r?e.state:this.#r,{state:c}=e;let f={...c},l=!1,p;if(t._optimisticResults){const m=this.hasListeners(),P=!m&&_t(e,t),w=m&&Rt(e,r,t,s);(P||w)&&(f={...f,...Wt(c.data,e.options)}),t._optimisticResults==="isRestoring"&&(f.fetchStatus="idle")}let{error:v,errorUpdatedAt:b,status:h}=f;p=f.data;let R=!1;if(t.placeholderData!==void 0&&p===void 0&&h==="pending"){let m;n?.isPlaceholderData&&t.placeholderData===o?.placeholderData?(m=n.data,R=!0):m=typeof t.placeholderData=="function"?t.placeholderData(this.#d?.state.data,this.#d):t.placeholderData,m!==void 0&&(h="success",p=rt(n?.data,m,t),l=!0)}if(t.select&&p!==void 0&&!R)if(n&&p===i?.data&&t.select===this.#h)p=this.#f;else try{this.#h=t.select,p=t.select(p),p=rt(n?.data,p,t),this.#f=p,this.#o=null}catch(m){this.#o=m}this.#o&&(v=this.#o,p=this.#f,b=Date.now(),h="error");const j=f.fetchStatus==="fetching",I=h==="pending",J=h==="error",re=I&&j,O=p!==void 0,d={status:h,fetchStatus:f.fetchStatus,isPending:I,isSuccess:h==="success",isError:J,isInitialLoading:re,isLoading:re,data:p,dataUpdatedAt:f.dataUpdatedAt,error:v,errorUpdatedAt:b,failureCount:f.fetchFailureCount,failureReason:f.fetchFailureReason,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>u.dataUpdateCount||f.errorUpdateCount>u.errorUpdateCount,isFetching:j,isRefetching:j&&!I,isLoadingError:J&&!O,isPaused:f.fetchStatus==="paused",isPlaceholderData:l,isRefetchError:J&&O,isStale:vt(e,t),refetch:this.refetch,promise:this.#a,isEnabled:W(t.enabled,e)!==!1};if(this.options.experimental_prefetchInRender){const m=x=>{d.status==="error"?x.reject(d.error):d.data!==void 0&&x.resolve(d.data)},P=()=>{const x=this.#a=d.promise=nt();m(x)},w=this.#a;switch(w.status){case"pending":e.queryHash===r.queryHash&&m(w);break;case"fulfilled":(d.status==="error"||d.data!==w.value)&&P();break;case"rejected":(d.status!=="error"||d.error!==w.reason)&&P();break}}return d}updateResult(){const e=this.#n,t=this.createResult(this.#e,this.options);if(this.#s=this.#e.state,this.#i=this.options,this.#s.data!==void 0&&(this.#d=this.#e),ke(t,e))return;this.#n=t;const r=()=>{if(!e)return!0;const{notifyOnChangeProps:s}=this.options,n=typeof s=="function"?s():s;if(n==="all"||!n&&!this.#y.size)return!0;const i=new Set(n??this.#y);return this.options.throwOnError&&i.add("error"),Object.keys(this.#n).some(o=>{const a=o;return this.#n[a]!==e[a]&&i.has(a)})};this.#x({listeners:r()})}#P(){const e=this.#t.getQueryCache().build(this.#t,this.options);if(e===this.#e)return;const t=this.#e;this.#e=e,this.#r=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#g()}#x(e){U.batch(()=>{e.listeners&&this.listeners.forEach(t=>{t(this.#n)}),this.#t.getQueryCache().notify({query:this.#e,type:"observerResultsUpdated"})})}};function ln(e,t){return W(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function _t(e,t){return ln(e,t)||e.state.data!==void 0&&it(e,t,t.refetchOnMount)}function it(e,t,r){if(W(t.enabled,e)!==!1&&ne(t.staleTime,e)!=="static"){const s=typeof r=="function"?r(e):r;return s==="always"||s!==!1&&vt(e,t)}return!1}function Rt(e,t,r,s){return(e!==t||W(s.enabled,e)===!1)&&(!r.suspense||e.state.status!=="error")&&vt(e,r)}function vt(e,t){return W(t.enabled,e)!==!1&&e.isStaleByTime(ne(t.staleTime,e))}function fn(e,t){return!ke(e.getCurrentResult(),t)}function Ct(e,t){const r=new Set(t);return e.filter(s=>!r.has(s))}function dn(e,t,r){const s=e.slice(0);return s[t]=r,s}var hn=class extends fe{#t;#e;#r;#n;#s;#i;#a;#o;#h=[];constructor(e,t,r){super(),this.#t=e,this.#n=r,this.#r=[],this.#s=[],this.#e=[],this.setQueries(t)}onSubscribe(){this.listeners.size===1&&this.#s.forEach(e=>{e.subscribe(t=>{this.#l(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#s.forEach(e=>{e.destroy()})}setQueries(e,t){this.#r=e,this.#n=t,U.batch(()=>{const r=this.#s,s=this.#c(this.#r);this.#h=s,s.forEach(a=>a.observer.setOptions(a.defaultedQueryOptions));const n=s.map(a=>a.observer),i=n.map(a=>a.getCurrentResult()),o=n.some((a,u)=>a!==r[u]);r.length===n.length&&!o||(this.#s=n,this.#e=i,this.hasListeners()&&(Ct(r,n).forEach(a=>{a.destroy()}),Ct(n,r).forEach(a=>{a.subscribe(u=>{this.#l(a,u)})}),this.#u()))})}getCurrentResult(){return this.#e}getQueries(){return this.#s.map(e=>e.getCurrentQuery())}getObservers(){return this.#s}getOptimisticResult(e,t){const r=this.#c(e),s=r.map(n=>n.observer.getOptimisticResult(n.defaultedQueryOptions));return[s,n=>this.#d(n??s,t),()=>this.#f(s,r)]}#f(e,t){return t.map((r,s)=>{const n=e[s];return r.defaultedQueryOptions.notifyOnChangeProps?n:r.observer.trackResult(n,i=>{t.forEach(o=>{o.observer.trackProp(i)})})})}#d(e,t){return t?((!this.#i||this.#e!==this.#o||t!==this.#a)&&(this.#a=t,this.#o=this.#e,this.#i=pt(this.#i,t(e))),this.#i):e}#c(e){const t=new Map(this.#s.map(s=>[s.options.queryHash,s])),r=[];return e.forEach(s=>{const n=this.#t.defaultQueryOptions(s),i=t.get(n.queryHash);i?r.push({defaultedQueryOptions:n,observer:i}):r.push({defaultedQueryOptions:n,observer:new _e(this.#t,n)})}),r}#l(e,t){const r=this.#s.indexOf(e);r!==-1&&(this.#e=dn(this.#e,r,t),this.#u())}#u(){if(this.hasListeners()){const e=this.#i,t=this.#f(this.#e,this.#h),r=this.#d(t,this.#n?.combine);e!==r&&U.batch(()=>{this.listeners.forEach(s=>{s(this.#e)})})}}},Jt=class extends _e{constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e){super.setOptions({...e,behavior:Ue()})}getOptimisticResult(e){return e.behavior=Ue(),super.getOptimisticResult(e)}fetchNextPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"backward"}}})}createResult(e,t){const{state:r}=e,s=super.createResult(e,t),{isFetching:n,isRefetching:i,isError:o,isRefetchError:a}=s,u=r.fetchMeta?.fetchMore?.direction,c=o&&u==="forward",f=n&&u==="forward",l=o&&u==="backward",p=n&&u==="backward";return{...s,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:an(t,r.data),hasPreviousPage:un(t,r.data),isFetchNextPageError:c,isFetchingNextPage:f,isFetchPreviousPageError:l,isFetchingPreviousPage:p,isRefetchError:a&&!c&&!l,isRefetching:i&&!f&&!p}}},pn=class extends fe{#t;#e=void 0;#r;#n;constructor(e,t){super(),this.#t=e,this.setOptions(t),this.bindMethods(),this.#s()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#t.defaultMutationOptions(e),ke(this.options,t)||this.#t.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&se(t.mutationKey)!==se(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#s(),this.#i(e)}getCurrentResult(){return this.#e}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#s(),this.#i()}mutate(e,t){return this.#n=t,this.#r?.removeObserver(this),this.#r=this.#t.getMutationCache().build(this.#t,this.options),this.#r.addObserver(this),this.#r.execute(e)}#s(){const e=this.#r?.state??Gt();this.#e={...e,isPending:e.status==="pending",isSuccess:e.status==="success",isError:e.status==="error",isIdle:e.status==="idle",mutate:this.mutate,reset:this.reset}}#i(e){U.batch(()=>{if(this.#n&&this.hasListeners()){const t=this.#e.variables,r=this.#e.context;e?.type==="success"?(this.#n.onSuccess?.(e.data,t,r),this.#n.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#n.onError?.(e.error,t,r),this.#n.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(t=>{t(this.#e)})})}},Xt=_.createContext(void 0),Re=e=>{const t=_.useContext(Xt);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},yn=({client:e,children:t})=>(_.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),Ae.jsx(Xt.Provider,{value:e,children:t})),Zt=_.createContext(!1),er=()=>_.useContext(Zt);Zt.Provider;function vn(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var mn=_.createContext(vn()),tr=()=>_.useContext(mn),rr=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},nr=e=>{_.useEffect(()=>{e.clearReset()},[e])},sr=({result:e,errorResetBoundary:t,throwOnError:r,query:s,suspense:n})=>e.isError&&!t.isReset()&&!e.isFetching&&s&&(n&&e.data===void 0||Kt(r,[e.error,s])),mt=(e,t)=>t.state.data===void 0,ir=e=>{if(e.suspense){const t=s=>s==="static"?s:Math.max(s??1e3,1e3),r=e.staleTime;e.staleTime=typeof r=="function"?(...s)=>t(r(...s)):t(r),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},or=(e,t)=>e.isLoading&&e.isFetching&&!t,ot=(e,t)=>e?.suspense&&t.isPending,$e=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function ar({queries:e,...t},r){const s=Re(r),n=er(),i=tr(),o=_.useMemo(()=>e.map(h=>{const R=s.defaultQueryOptions(h);return R._optimisticResults=n?"isRestoring":"optimistic",R}),[e,s,n]);o.forEach(h=>{ir(h),rr(h,i)}),nr(i);const[a]=_.useState(()=>new hn(s,o,t)),[u,c,f]=a.getOptimisticResult(o,t.combine),l=!n&&t.subscribed!==!1;_.useSyncExternalStore(_.useCallback(h=>l?a.subscribe(U.batchCalls(h)):B,[a,l]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),_.useEffect(()=>{a.setQueries(o,t)},[o,t,a]);const v=u.some((h,R)=>ot(o[R],h))?u.flatMap((h,R)=>{const j=o[R];if(j){const I=new _e(s,j);if(ot(j,h))return $e(j,I,i);or(h,n)&&$e(j,I,i)}return[]}):[];if(v.length>0)throw Promise.all(v);const b=u.find((h,R)=>{const j=o[R];return j&&sr({result:h,errorResetBoundary:i,throwOnError:j.throwOnError,query:s.getQueryCache().get(j.queryHash),suspense:j.suspense})});if(b?.error)throw b.error;return c(f())}function Le(e,t,r){const s=er(),n=tr(),i=Re(r),o=i.defaultQueryOptions(e);i.getDefaultOptions().queries?._experimental_beforeQuery?.(o),o._optimisticResults=s?"isRestoring":"optimistic",ir(o),rr(o,n),nr(n);const a=!i.getQueryCache().get(o.queryHash),[u]=_.useState(()=>new t(i,o)),c=u.getOptimisticResult(o),f=!s&&e.subscribed!==!1;if(_.useSyncExternalStore(_.useCallback(l=>{const p=f?u.subscribe(U.batchCalls(l)):B;return u.updateResult(),p},[u,f]),()=>u.getCurrentResult(),()=>u.getCurrentResult()),_.useEffect(()=>{u.setOptions(o)},[o,u]),ot(o,c))throw $e(o,u,n);if(sr({result:c,errorResetBoundary:n,throwOnError:o.throwOnError,query:i.getQueryCache().get(o.queryHash),suspense:o.suspense}))throw c.error;return i.getDefaultOptions().queries?._experimental_afterQuery?.(o,c),o.experimental_prefetchInRender&&!ce&&or(c,s)&&(a?$e(o,u,n):i.getQueryCache().get(o.queryHash)?.promise)?.catch(B).finally(()=>{u.updateResult()}),o.notifyOnChangeProps?c:u.trackResult(c)}function bn(e,t){return Le(e,_e,t)}function gn(e,t){return Le({...e,enabled:!0,suspense:!0,throwOnError:mt,placeholderData:void 0},_e,t)}function On(e,t){return Le({...e,enabled:!0,suspense:!0,throwOnError:mt},Jt,t)}function wn(e,t){return ar({...e,queries:e.queries.map(r=>({...r,suspense:!0,throwOnError:mt,enabled:!0,placeholderData:void 0}))},t)}function Pn(e,t){const r=Re(t);r.getQueryState(e.queryKey)||r.prefetchQuery(e)}function xn(e,t){const r=Re(t);r.getQueryState(e.queryKey)||r.prefetchInfiniteQuery(e)}function Sn(e,t){const r=Re(t),[s]=_.useState(()=>new pn(r,e));_.useEffect(()=>{s.setOptions(e)},[s,e]);const n=_.useSyncExternalStore(_.useCallback(o=>s.subscribe(U.batchCalls(o)),[s]),()=>s.getCurrentResult(),()=>s.getCurrentResult()),i=_.useCallback((o,a)=>{s.mutate(o,a).catch(B)},[s]);if(n.error&&Kt(s.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:i,mutateAsync:n.mutate}}function _n(e,t){return Le(e,Jt,t)}var Rn=Object.create,ur=Object.defineProperty,Cn=Object.getOwnPropertyDescriptor,cr=Object.getOwnPropertyNames,jn=Object.getPrototypeOf,En=Object.prototype.hasOwnProperty,ee=(e,t)=>function(){return t||(0,e[cr(e)[0]])((t={exports:{}}).exports,t),t.exports},Qn=(e,t,r,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(var n=cr(t),i=0,o=n.length,a;i<o;i++)a=n[i],!En.call(e,a)&&a!==r&&ur(e,a,{get:(u=>t[u]).bind(null,a),enumerable:!(s=Cn(t,a))||s.enumerable});return e},$=(e,t,r)=>(r=e!=null?Rn(jn(e)):{},Qn(ur(r,"default",{value:e,enumerable:!0}),e)),lr=ee({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(e,t){function r(s){"@babel/helpers - typeof";return t.exports=r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},t.exports.__esModule=!0,t.exports.default=t.exports,r(s)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),In=ee({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(e,t){var r=lr().default;function s(n,i){if(r(n)!="object"||!n)return n;var o=n[Symbol.toPrimitive];if(o!==void 0){var a=o.call(n,i||"default");if(r(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return(i==="string"?String:Number)(n)}t.exports=s,t.exports.__esModule=!0,t.exports.default=t.exports}}),qn=ee({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(e,t){var r=lr().default,s=In();function n(i){var o=s(i,"string");return r(o)=="symbol"?o:o+""}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}}),de=ee({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(e,t){var r=qn();function s(n,i,o){return(i=r(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o,n}t.exports=s,t.exports.__esModule=!0,t.exports.default=t.exports}}),Y=ee({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(e,t){var r=de();function s(i,o){var a=Object.keys(i);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(i);o&&(u=u.filter(function(c){return Object.getOwnPropertyDescriptor(i,c).enumerable})),a.push.apply(a,u)}return a}function n(i){for(var o=1;o<arguments.length;o++){var a=arguments[o]!=null?arguments[o]:{};o%2?s(Object(a),!0).forEach(function(u){r(i,u,a[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(a)):s(Object(a)).forEach(function(u){Object.defineProperty(i,u,Object.getOwnPropertyDescriptor(a,u))})}return i}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}});function me(e){const t={subscribe(r){let s=null,n=!1,i=!1,o=!1;function a(){if(s===null){o=!0;return}i||(i=!0,typeof s=="function"?s():s&&s.unsubscribe())}return s=e({next(u){var c;n||(c=r.next)===null||c===void 0||c.call(r,u)},error(u){var c;n||(n=!0,(c=r.error)===null||c===void 0||c.call(r,u),a())},complete(){var u;n||(n=!0,(u=r.complete)===null||u===void 0||u.call(r),a())}}),o&&a(),{unsubscribe:a}},pipe(...r){return r.reduce(Mn,t)}};return t}function Mn(e,t){return t(e)}function Dn(e){const t=new AbortController;return new Promise((s,n)=>{let i=!1;function o(){i||(i=!0,a.unsubscribe())}t.signal.addEventListener("abort",()=>{n(t.signal.reason)});const a=e.subscribe({next(u){i=!0,s(u),o()},error(u){n(u)},complete(){t.abort(),o()}})})}function Tn(e){return t=>{let r=0,s=null;const n=[];function i(){s||(s=t.subscribe({next(a){for(const c of n){var u;(u=c.next)===null||u===void 0||u.call(c,a)}},error(a){for(const c of n){var u;(u=c.error)===null||u===void 0||u.call(c,a)}},complete(){for(const u of n){var a;(a=u.complete)===null||a===void 0||a.call(u)}}}))}function o(){if(r===0&&s){const a=s;s=null,a.unsubscribe()}}return me(a=>(r++,n.push(a),i(),{unsubscribe(){r--,o();const u=n.findIndex(c=>c===a);u>-1&&n.splice(u,1)}}))}}function An(e){return t=>me(r=>t.subscribe({next(s){var n;(n=e.next)===null||n===void 0||n.call(e,s),r.next(s)},error(s){var n;(n=e.error)===null||n===void 0||n.call(e,s),r.error(s)},complete(){var s;(s=e.complete)===null||s===void 0||s.call(e),r.complete()}}))}function kn(e){let t=e;const r=[],s=o=>{t!==void 0&&o.next(t),r.push(o)},n=o=>{r.splice(r.indexOf(o),1)},i=me(o=>(s(o),()=>{n(o)}));return i.next=o=>{if(t!==o){t=o;for(const a of r)a.next(o)}},i.get=()=>t,i}function Fn(e){return me(t=>{function r(n=0,i=e.op){const o=e.links[n];if(!o)throw new Error("No more links to execute - did you forget to add an ending link?");return o({op:i,next(u){return r(n+1,u)}})}return r().subscribe(t)})}function le(e){return!!e&&!Array.isArray(e)&&typeof e=="object"}const Un=typeof Symbol=="function"&&!!Symbol.asyncIterator;function fr(e){return Un&&le(e)&&Symbol.asyncIterator in e}var $n=Object.create,dr=Object.defineProperty,Nn=Object.getOwnPropertyDescriptor,hr=Object.getOwnPropertyNames,Ln=Object.getPrototypeOf,Kn=Object.prototype.hasOwnProperty,Ce=(e,t)=>function(){return t||(0,e[hr(e)[0]])((t={exports:{}}).exports,t),t.exports},Bn=(e,t,r,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(var n=hr(t),i=0,o=n.length,a;i<o;i++)a=n[i],!Kn.call(e,a)&&a!==r&&dr(e,a,{get:(u=>t[u]).bind(null,a),enumerable:!(s=Nn(t,a))||s.enumerable});return e},Ke=(e,t,r)=>(r=e!=null?$n(Ln(e)):{},Bn(dr(r,"default",{value:e,enumerable:!0}),e));const pr=()=>{},jt=e=>{Object.freeze&&Object.freeze(e)};function yr(e,t,r){var s;const n=t.join(".");return(s=r[n])!==null&&s!==void 0||(r[n]=new Proxy(pr,{get(i,o){if(!(typeof o!="string"||o==="then"))return yr(e,[...t,o],r)},apply(i,o,a){const u=t[t.length-1];let c={args:a,path:t};return u==="call"?c={args:a.length>=2?[a[1]]:[],path:t.slice(0,-1)}:u==="apply"&&(c={args:a.length>=2?a[1]:[],path:t.slice(0,-1)}),jt(c.args),jt(c.path),e(c)}})),r[n]}const Be=e=>yr(e,[],Object.create(null)),bt=e=>new Proxy(pr,{get(t,r){if(r!=="then")return e(r)}});var vr=Ce({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(e,t){function r(s){"@babel/helpers - typeof";return t.exports=r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},t.exports.__esModule=!0,t.exports.default=t.exports,r(s)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),Hn=Ce({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(e,t){var r=vr().default;function s(n,i){if(r(n)!="object"||!n)return n;var o=n[Symbol.toPrimitive];if(o!==void 0){var a=o.call(n,i||"default");if(r(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return(i==="string"?String:Number)(n)}t.exports=s,t.exports.__esModule=!0,t.exports.default=t.exports}}),zn=Ce({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(e,t){var r=vr().default,s=Hn();function n(i){var o=s(i,"string");return r(o)=="symbol"?o:o+""}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}}),mr=Ce({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(e,t){var r=zn();function s(n,i,o){return(i=r(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o,n}t.exports=s,t.exports.__esModule=!0,t.exports.default=t.exports}}),gt=Ce({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(e,t){var r=mr();function s(i,o){var a=Object.keys(i);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(i);o&&(u=u.filter(function(c){return Object.getOwnPropertyDescriptor(i,c).enumerable})),a.push.apply(a,u)}return a}function n(i){for(var o=1;o<arguments.length;o++){var a=arguments[o]!=null?arguments[o]:{};o%2?s(Object(a),!0).forEach(function(u){r(i,u,a[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(a)):s(Object(a)).forEach(function(u){Object.defineProperty(i,u,Object.getOwnPropertyDescriptor(a,u))})}return i}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}});Ke(gt());Ke(mr());var Qe=Ke(gt());function Vn(e,t){if("error"in e){const s=t.deserialize(e.error);return{ok:!1,error:(0,Qe.default)((0,Qe.default)({},e),{},{error:s})}}return{ok:!0,result:(0,Qe.default)((0,Qe.default)({},e.result),(!e.result.type||e.result.type==="data")&&{type:"data",data:t.deserialize(e.result.data)})}}var We=class extends Error{constructor(){super("Unable to transform response from server")}};function Wn(e,t){let r;try{r=Vn(e,t)}catch{throw new We}if(!r.ok&&(!le(r.error.error)||typeof r.error.error.code!="number"))throw new We;if(r.ok&&!le(r.result))throw new We;return r}Ke(gt());var Ie=$(de()),pe=$(Y());function Gn(e){return e instanceof Ne}function Yn(e){return le(e)&&le(e.error)&&typeof e.error.code=="number"&&typeof e.error.message=="string"}function Jn(e,t){return typeof e=="string"?e:le(e)&&typeof e.message=="string"?e.message:t}var Ne=class Me extends Error{constructor(t,r){var s,n;const i=r?.cause;super(t,{cause:i}),(0,Ie.default)(this,"cause",void 0),(0,Ie.default)(this,"shape",void 0),(0,Ie.default)(this,"data",void 0),(0,Ie.default)(this,"meta",void 0),this.meta=r?.meta,this.cause=i,this.shape=r==null||(s=r.result)===null||s===void 0?void 0:s.error,this.data=r==null||(n=r.result)===null||n===void 0?void 0:n.error.data,this.name="TRPCClientError",Object.setPrototypeOf(this,Me.prototype)}static from(t,r={}){const s=t;return Gn(s)?(r.meta&&(s.meta=(0,pe.default)((0,pe.default)({},s.meta),r.meta)),s):Yn(s)?new Me(s.error.message,(0,pe.default)((0,pe.default)({},r),{},{result:s})):new Me(Jn(s,"Unknown error"),(0,pe.default)((0,pe.default)({},r),{},{cause:s}))}};function Xn(e){const t=e;return t?"input"in t?t:{input:t,output:t}:{input:{serialize:r=>r,deserialize:r=>r},output:{serialize:r=>r,deserialize:r=>r}}}const Et=e=>typeof e=="function";function Zn(e){if(e)return e;if(typeof window<"u"&&Et(window.fetch))return window.fetch;if(typeof globalThis<"u"&&Et(globalThis.fetch))return globalThis.fetch;throw new Error("No fetch implementation found")}var ge=$(Y());function es(e){return{url:e.url.toString(),fetch:e.fetch,transformer:Xn(e.transformer),methodOverride:e.methodOverride}}function ts(e){const t={};for(let r=0;r<e.length;r++){const s=e[r];t[r]=s}return t}const rs={query:"GET",mutation:"POST",subscription:"PATCH"};function br(e){return"input"in e?e.transformer.input.serialize(e.input):ts(e.inputs.map(t=>e.transformer.input.serialize(t)))}const gr=e=>{const t=e.url.split("?");let s=t[0].replace(/\/$/,"")+"/"+e.path;const n=[];if(t[1]&&n.push(t[1]),"inputs"in e&&n.push("batch=1"),e.type==="query"||e.type==="subscription"){const i=br(e);i!==void 0&&e.methodOverride!=="POST"&&n.push(`input=${encodeURIComponent(JSON.stringify(i))}`)}return n.length&&(s+="?"+n.join("&")),s},ns=e=>{if(e.type==="query"&&e.methodOverride!=="POST")return;const t=br(e);return t!==void 0?JSON.stringify(t):void 0},ss=e=>us((0,ge.default)((0,ge.default)({},e),{},{contentTypeHeader:"application/json",getUrl:gr,getBody:ns}));var is=class extends Error{constructor(){const e="AbortError";super(e),this.name=e,this.message=e}};const os=e=>{var t;if(e?.aborted)throw(t=e.throwIfAborted)===null||t===void 0||t.call(e),typeof DOMException<"u"?new DOMException("AbortError","AbortError"):new is};async function as(e){var t;os(e.signal);const r=e.getUrl(e),s=e.getBody(e),{type:n}=e,i=await(async()=>{const a=await e.headers();return Symbol.iterator in a?Object.fromEntries(a):a})(),o=(0,ge.default)((0,ge.default)((0,ge.default)({},e.contentTypeHeader?{"content-type":e.contentTypeHeader}:{}),e.trpcAcceptHeader?{"trpc-accept":e.trpcAcceptHeader}:void 0),i);return Zn(e.fetch)(r,{method:(t=e.methodOverride)!==null&&t!==void 0?t:rs[n],signal:e.signal,body:s,headers:o})}async function us(e){const t={},r=await as(e);t.response=r;const s=await r.json();return t.responseJSON=s,{json:s,meta:t}}$(Y());const Qt=()=>{throw new Error("Something went wrong. Please submit an issue at https://github.com/trpc/trpc/issues/new")};function It(e){let t=null,r=null;const s=()=>{clearTimeout(r),r=null,t=null};function n(a){const u=[[]];let c=0;for(;;){const p=a[c];if(!p)break;const v=u[u.length-1];if(p.aborted){var f;(f=p.reject)===null||f===void 0||f.call(p,new Error("Aborted")),c++;continue}if(e.validate(v.concat(p).map(h=>h.key))){v.push(p),c++;continue}if(v.length===0){var l;(l=p.reject)===null||l===void 0||l.call(p,new Error("Input is too big for a single dispatch")),c++;continue}u.push([])}return u}function i(){const a=n(t);s();for(const u of a){if(!u.length)continue;const c={items:u};for(const l of u)l.batch=c;e.fetch(c.items.map(l=>l.key)).then(async l=>{await Promise.all(l.map(async(v,b)=>{const h=c.items[b];try{var R;const I=await Promise.resolve(v);(R=h.resolve)===null||R===void 0||R.call(h,I)}catch(I){var j;(j=h.reject)===null||j===void 0||j.call(h,I)}h.batch=null,h.reject=null,h.resolve=null}));for(const v of c.items){var p;(p=v.reject)===null||p===void 0||p.call(v,new Error("Missing result")),v.batch=null}}).catch(l=>{for(const v of c.items){var p;(p=v.reject)===null||p===void 0||p.call(v,l),v.batch=null}})}}function o(a){var u;const c={aborted:!1,key:a,batch:null,resolve:Qt,reject:Qt},f=new Promise((l,p)=>{var v;c.reject=p,c.resolve=l,(v=t)!==null&&v!==void 0||(t=[]),t.push(c)});return(u=r)!==null&&u!==void 0||(r=setTimeout(i)),f}return{load:o}}function cs(...e){const t=new AbortController,r=e.length;let s=0;const n=()=>{++s===r&&t.abort()};for(const i of e)i?.aborted?n():i?.addEventListener("abort",n,{once:!0});return t.signal}var qe=$(Y());function Or(e){var t,r;const s=es(e),n=(t=e.maxURLLength)!==null&&t!==void 0?t:1/0,i=(r=e.maxItems)!==null&&r!==void 0?r:1/0;return()=>{const o=f=>({validate(l){if(n===1/0&&i===1/0)return!0;if(l.length>i)return!1;const p=l.map(h=>h.path).join(","),v=l.map(h=>h.input);return gr((0,qe.default)((0,qe.default)({},s),{},{type:f,path:p,inputs:v,signal:null})).length<=n},async fetch(l){const p=l.map(I=>I.path).join(","),v=l.map(I=>I.input),b=cs(...l.map(I=>I.signal)),h=await ss((0,qe.default)((0,qe.default)({},s),{},{path:p,inputs:v,type:f,headers(){return e.headers?typeof e.headers=="function"?e.headers({opList:l}):e.headers:{}},signal:b}));return(Array.isArray(h.json)?h.json:l.map(()=>h.json)).map(I=>({meta:h.meta,json:I}))}}),a=It(o("query")),u=It(o("mutation")),c={query:a,mutation:u};return({op:f})=>me(l=>{/* istanbul ignore if -- @preserve */if(f.type==="subscription")throw new Error("Subscriptions are unsupported by `httpLink` - use `httpSubscriptionLink` or `wsLink`");const v=c[f.type].load(f);let b;return v.then(h=>{b=h;const R=Wn(h.json,s.transformer.output);if(!R.ok){l.error(Ne.from(R.error,{meta:h.meta}));return}l.next({context:h.meta,result:R.result}),l.complete()}).catch(h=>{l.error(Ne.from(h,{meta:b?.meta}))}),()=>{}})}}var G=$(Y());function ls(e){return typeof FormData>"u"?!1:e instanceof FormData}const Ge={css:{query:["72e3ff","3fb0d8"],mutation:["c5a3fc","904dfc"],subscription:["ff49e1","d83fbe"]},ansi:{regular:{query:["\x1B[30;46m","\x1B[97;46m"],mutation:["\x1B[30;45m","\x1B[97;45m"],subscription:["\x1B[30;42m","\x1B[97;42m"]},bold:{query:["\x1B[1;30;46m","\x1B[1;97;46m"],mutation:["\x1B[1;30;45m","\x1B[1;97;45m"],subscription:["\x1B[1;30;42m","\x1B[1;97;42m"]}}};function fs(e){const{direction:t,type:r,withContext:s,path:n,id:i,input:o}=e,a=[],u=[];if(e.colorMode==="none")a.push(t==="up"?">>":"<<",r,`#${i}`,n);else if(e.colorMode==="ansi"){const[c,f]=Ge.ansi.regular[r],[l,p]=Ge.ansi.bold[r];a.push(t==="up"?c:f,t==="up"?">>":"<<",r,t==="up"?l:p,`#${i}`,n,"\x1B[0m")}else{const[c,f]=Ge.css[r],l=`
    background-color: #${t==="up"?c:f};
    color: ${t==="up"?"black":"white"};
    padding: 2px;
  `;a.push("%c",t==="up"?">>":"<<",r,`#${i}`,`%c${n}%c`,"%O"),u.push(l,`${l}; font-weight: bold;`,`${l}; font-weight: normal;`)}return t==="up"?u.push(s?{input:o,context:e.context}:{input:o}):u.push((0,G.default)({input:o,result:e.result,elapsedMs:e.elapsedMs},s&&{context:e.context})),{parts:a,args:u}}const ds=({c:e=console,colorMode:t="css",withContext:r})=>s=>{const n=s.input,i=ls(n)?Object.fromEntries(n):n,{parts:o,args:a}=fs((0,G.default)((0,G.default)({},s),{},{colorMode:t,input:i,withContext:r})),u=s.direction==="down"&&s.result&&(s.result instanceof Error||"error"in s.result.result&&s.result.result.error)?"error":"log";e[u].apply(null,[o.join(" ")].concat(a))};function wr(e={}){var t,r;const{enabled:s=()=>!0}=e,n=(t=e.colorMode)!==null&&t!==void 0?t:typeof window>"u"?"ansi":"css",i=(r=e.withContext)!==null&&r!==void 0?r:n==="css",{logger:o=ds({c:e.console,colorMode:n,withContext:i})}=e;return()=>({op:a,next:u})=>me(c=>{s((0,G.default)((0,G.default)({},a),{},{direction:"up"}))&&o((0,G.default)((0,G.default)({},a),{},{direction:"up"}));const f=Date.now();function l(p){const v=Date.now()-f;s((0,G.default)((0,G.default)({},a),{},{direction:"down",result:p}))&&o((0,G.default)((0,G.default)({},a),{},{direction:"down",elapsedMs:v,result:p}))}return u(a).pipe(An({next(p){l(p)},error(p){l(p)}})).subscribe(c)})}const Pr=(e,...t)=>typeof e=="function"?e(...t):e;$(de());function hs(){let e,t;return{promise:new Promise((s,n)=>{e=s,t=n}),resolve:e,reject:t}}async function ps(e){const t=await Pr(e.url);if(!e.connectionParams)return t;const s=`${t.includes("?")?"&":"?"}connectionParams=1`;return t+s}async function ys(e){const t={method:"connectionParams",data:await Pr(e)};return JSON.stringify(t)}$(de());var ue=$(de());function vs(e){const{promise:t,resolve:r,reject:s}=hs();return e.addEventListener("open",()=>{e.removeEventListener("error",s),r()}),e.addEventListener("error",s),t}function ms(e,{intervalMs:t,pongTimeoutMs:r}){let s,n;function i(){s=setTimeout(()=>{e.send("PING"),n=setTimeout(()=>{e.close()},r)},t)}function o(){clearTimeout(s),i()}function a(){clearTimeout(n),o()}e.addEventListener("open",i),e.addEventListener("message",({data:u})=>{clearTimeout(s),i(),u==="PONG"&&a()}),e.addEventListener("close",()=>{clearTimeout(s),clearTimeout(n)})}var bs=class at{constructor(t){var r;if((0,ue.default)(this,"id",++at.connectCount),(0,ue.default)(this,"WebSocketPonyfill",void 0),(0,ue.default)(this,"urlOptions",void 0),(0,ue.default)(this,"keepAliveOpts",void 0),(0,ue.default)(this,"wsObservable",kn(null)),(0,ue.default)(this,"openPromise",null),this.WebSocketPonyfill=(r=t.WebSocketPonyfill)!==null&&r!==void 0?r:WebSocket,!this.WebSocketPonyfill)throw new Error("No WebSocket implementation found - you probably don't want to use this on the server, but if you do you need to pass a `WebSocket`-ponyfill");this.urlOptions=t.urlOptions,this.keepAliveOpts=t.keepAlive}get ws(){return this.wsObservable.get()}set ws(t){this.wsObservable.next(t)}isOpen(){return!!this.ws&&this.ws.readyState===this.WebSocketPonyfill.OPEN&&!this.openPromise}isClosed(){return!!this.ws&&(this.ws.readyState===this.WebSocketPonyfill.CLOSING||this.ws.readyState===this.WebSocketPonyfill.CLOSED)}async open(){var t=this;if(t.openPromise)return t.openPromise;t.id=++at.connectCount;const r=ps(t.urlOptions).then(s=>new t.WebSocketPonyfill(s));t.openPromise=r.then(async s=>{t.ws=s,s.addEventListener("message",function({data:n}){n==="PING"&&this.send("PONG")}),t.keepAliveOpts.enabled&&ms(s,t.keepAliveOpts),s.addEventListener("close",()=>{t.ws===s&&(t.ws=null)}),await vs(s),t.urlOptions.connectionParams&&s.send(await ys(t.urlOptions.connectionParams))});try{await t.openPromise}finally{t.openPromise=null}}async close(){var t=this;try{await t.openPromise}finally{var r;(r=t.ws)===null||r===void 0||r.close()}}};(0,ue.default)(bs,"connectCount",0);$(de());$(Y());var Ye=$(de()),qt=$(Y()),He=class{constructor(e){(0,Ye.default)(this,"links",void 0),(0,Ye.default)(this,"runtime",void 0),(0,Ye.default)(this,"requestId",void 0),this.requestId=0,this.runtime={},this.links=e.links.map(t=>t(this.runtime))}$request(e){var t;return Fn({links:this.links,op:(0,qt.default)((0,qt.default)({},e),{},{context:(t=e.context)!==null&&t!==void 0?t:{},id:++this.requestId})}).pipe(Tn())}async requestAsPromise(e){var t=this;try{const r=t.$request(e);return(await Dn(r)).result.data}catch(r){throw Ne.from(r)}}query(e,t,r){return this.requestAsPromise({type:"query",path:e,input:t,context:r?.context,signal:r?.signal})}mutation(e,t,r){return this.requestAsPromise({type:"mutation",path:e,input:t,context:r?.context,signal:r?.signal})}subscription(e,t,r){return this.$request({type:"subscription",path:e,input:t,context:r.context,signal:r.signal}).subscribe({next(n){switch(n.result.type){case"state":{var i;(i=r.onConnectionStateChange)===null||i===void 0||i.call(r,n.result);break}case"started":{var o;(o=r.onStarted)===null||o===void 0||o.call(r,{context:n.context});break}case"stopped":{var a;(a=r.onStopped)===null||a===void 0||a.call(r);break}case"data":case void 0:{var u;(u=r.onData)===null||u===void 0||u.call(r,n.result.data);break}}},error(n){var i;(i=r.onError)===null||i===void 0||i.call(r,n)},complete(){var n;(n=r.onComplete)===null||n===void 0||n.call(r)}})}};const xr=Symbol.for("trpc_untypedClient"),gs={query:"query",mutate:"mutation",subscribe:"subscription"},Os=e=>gs[e];function Sr(e){const t=Be(({path:r,args:s})=>{const n=[...r],i=Os(n.pop()),o=n.join(".");return e[i](o,...s)});return bt(r=>r===xr?e:t[r])}function _r(e){const t=new He(e);return Sr(t)}function Ot(e){return e[xr]}$(Y());$(Y());var ws=ee({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js"(e,t){function r(n){var i,o,a,u=2;for(typeof Symbol<"u"&&(o=Symbol.asyncIterator,a=Symbol.iterator);u--;){if(o&&(i=n[o])!=null)return i.call(n);if(a&&(i=n[a])!=null)return new s(i.call(n));o="@@asyncIterator",a="@@iterator"}throw new TypeError("Object is not async iterable")}function s(n){function i(o){if(Object(o)!==o)return Promise.reject(new TypeError(o+" is not an object."));var a=o.done;return Promise.resolve(o.value).then(function(u){return{value:u,done:a}})}return s=function(a){this.s=a,this.n=a.next},s.prototype={s:null,n:null,next:function(){return i(this.n.apply(this.s,arguments))},return:function(a){var u=this.s.return;return u===void 0?Promise.resolve({value:a,done:!0}):i(u.apply(this.s,arguments))},throw:function(a){var u=this.s.return;return u===void 0?Promise.reject(a):i(u.apply(this.s,arguments))}},new s(n)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}});$(ws());$(Y());var Ps=ee({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js"(e,t){function r(){var s=typeof SuppressedError=="function"?SuppressedError:function(a,u){var c=Error();return c.name="SuppressedError",c.error=a,c.suppressed=u,c},n={},i=[];function o(a,u){if(u!=null){if(Object(u)!==u)throw new TypeError("using declarations can only be used with objects, functions, null, or undefined.");if(a)var c=u[Symbol.asyncDispose||Symbol.for("Symbol.asyncDispose")];if(c===void 0&&(c=u[Symbol.dispose||Symbol.for("Symbol.dispose")],a))var f=c;if(typeof c!="function")throw new TypeError("Object is not disposable.");f&&(c=function(){try{f.call(u)}catch(p){return Promise.reject(p)}}),i.push({v:u,d:c,a})}else a&&i.push({d:u,a});return u}return{e:n,u:o.bind(null,!1),a:o.bind(null,!0),d:function(){var u,c=this.e,f=0;function l(){for(;u=i.pop();)try{if(!u.a&&f===1)return f=0,i.push(u),Promise.resolve().then(l);if(u.d){var v=u.d.call(u.v);if(u.a)return f|=2,Promise.resolve(v).then(l,p)}else f|=1}catch(b){return p(b)}if(f===1)return c!==n?Promise.reject(c):Promise.resolve();if(c!==n)throw c}function p(v){return c=c!==n?new s(v,c):v,l()}return l()}}}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),Rr=ee({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/OverloadYield.js"(e,t){function r(s,n){this.v=s,this.k=n}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),xs=ee({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/awaitAsyncGenerator.js"(e,t){var r=Rr();function s(n){return new r(n,0)}t.exports=s,t.exports.__esModule=!0,t.exports.default=t.exports}}),Ss=ee({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/wrapAsyncGenerator.js"(e,t){var r=Rr();function s(i){return function(){return new n(i.apply(this,arguments))}}function n(i){var o,a;function u(f,l){try{var p=i[f](l),v=p.value,b=v instanceof r;Promise.resolve(b?v.v:v).then(function(h){if(b){var R=f==="return"?"return":"next";if(!v.k||h.done)return u(R,h);h=i[R](h).value}c(p.done?"return":"normal",h)},function(h){u("throw",h)})}catch(h){c("throw",h)}}function c(f,l){switch(f){case"return":o.resolve({value:l,done:!0});break;case"throw":o.reject(l);break;default:o.resolve({value:l,done:!1})}(o=o.next)?u(o.key,o.arg):a=null}this._invoke=function(f,l){return new Promise(function(p,v){var b={key:f,arg:l,resolve:p,reject:v,next:null};a?a=a.next=b:(o=a=b,u(f,l))})},typeof i.return!="function"&&(this.return=void 0)}n.prototype[typeof Symbol=="function"&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},n.prototype.next=function(i){return this._invoke("next",i)},n.prototype.throw=function(i){return this._invoke("throw",i)},n.prototype.return=function(i){return this._invoke("return",i)},t.exports=s,t.exports.__esModule=!0,t.exports.default=t.exports}});$(Ps());$(xs());$(Ss());$(Y());class _s{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(t,r){this.keyToValue.set(t,r),this.valueToKey.set(r,t)}getByKey(t){return this.keyToValue.get(t)}getByValue(t){return this.valueToKey.get(t)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}}class Cr{constructor(t){this.generateIdentifier=t,this.kv=new _s}register(t,r){this.kv.getByValue(t)||(r||(r=this.generateIdentifier(t)),this.kv.set(r,t))}clear(){this.kv.clear()}getIdentifier(t){return this.kv.getByValue(t)}getValue(t){return this.kv.getByKey(t)}}class Rs extends Cr{constructor(){super(t=>t.name),this.classToAllowedProps=new Map}register(t,r){typeof r=="object"?(r.allowProps&&this.classToAllowedProps.set(t,r.allowProps),super.register(t,r.identifier)):super.register(t,r)}getAllowedProps(t){return this.classToAllowedProps.get(t)}}function Cs(e){if("values"in Object)return Object.values(e);const t=[];for(const r in e)e.hasOwnProperty(r)&&t.push(e[r]);return t}function js(e,t){const r=Cs(e);if("find"in r)return r.find(t);const s=r;for(let n=0;n<s.length;n++){const i=s[n];if(t(i))return i}}function ve(e,t){Object.entries(e).forEach(([r,s])=>t(s,r))}function De(e,t){return e.indexOf(t)!==-1}function Mt(e,t){for(let r=0;r<e.length;r++){const s=e[r];if(t(s))return s}}class Es{constructor(){this.transfomers={}}register(t){this.transfomers[t.name]=t}findApplicable(t){return js(this.transfomers,r=>r.isApplicable(t))}findByName(t){return this.transfomers[t]}}const Qs=e=>Object.prototype.toString.call(e).slice(8,-1),jr=e=>typeof e>"u",Is=e=>e===null,Pe=e=>typeof e!="object"||e===null||e===Object.prototype?!1:Object.getPrototypeOf(e)===null?!0:Object.getPrototypeOf(e)===Object.prototype,ut=e=>Pe(e)&&Object.keys(e).length===0,ie=e=>Array.isArray(e),qs=e=>typeof e=="string",Ms=e=>typeof e=="number"&&!isNaN(e),Ds=e=>typeof e=="boolean",Ts=e=>e instanceof RegExp,xe=e=>e instanceof Map,Se=e=>e instanceof Set,Er=e=>Qs(e)==="Symbol",As=e=>e instanceof Date&&!isNaN(e.valueOf()),ks=e=>e instanceof Error,Dt=e=>typeof e=="number"&&isNaN(e),Fs=e=>Ds(e)||Is(e)||jr(e)||Ms(e)||qs(e)||Er(e),Us=e=>typeof e=="bigint",$s=e=>e===1/0||e===-1/0,Ns=e=>ArrayBuffer.isView(e)&&!(e instanceof DataView),Ls=e=>e instanceof URL,Qr=e=>e.replace(/\./g,"\\."),Je=e=>e.map(String).map(Qr).join("."),Oe=e=>{const t=[];let r="";for(let n=0;n<e.length;n++){let i=e.charAt(n);if(i==="\\"&&e.charAt(n+1)==="."){r+=".",n++;continue}if(i==="."){t.push(r),r="";continue}r+=i}const s=r;return t.push(s),t};function X(e,t,r,s){return{isApplicable:e,annotation:t,transform:r,untransform:s}}const Ir=[X(jr,"undefined",()=>null,()=>{}),X(Us,"bigint",e=>e.toString(),e=>typeof BigInt<"u"?BigInt(e):(console.error("Please add a BigInt polyfill."),e)),X(As,"Date",e=>e.toISOString(),e=>new Date(e)),X(ks,"Error",(e,t)=>{const r={name:e.name,message:e.message};return t.allowedErrorProps.forEach(s=>{r[s]=e[s]}),r},(e,t)=>{const r=new Error(e.message);return r.name=e.name,r.stack=e.stack,t.allowedErrorProps.forEach(s=>{r[s]=e[s]}),r}),X(Ts,"regexp",e=>""+e,e=>{const t=e.slice(1,e.lastIndexOf("/")),r=e.slice(e.lastIndexOf("/")+1);return new RegExp(t,r)}),X(Se,"set",e=>[...e.values()],e=>new Set(e)),X(xe,"map",e=>[...e.entries()],e=>new Map(e)),X(e=>Dt(e)||$s(e),"number",e=>Dt(e)?"NaN":e>0?"Infinity":"-Infinity",Number),X(e=>e===0&&1/e===-1/0,"number",()=>"-0",Number),X(Ls,"URL",e=>e.toString(),e=>new URL(e))];function ze(e,t,r,s){return{isApplicable:e,annotation:t,transform:r,untransform:s}}const qr=ze((e,t)=>Er(e)?!!t.symbolRegistry.getIdentifier(e):!1,(e,t)=>["symbol",t.symbolRegistry.getIdentifier(e)],e=>e.description,(e,t,r)=>{const s=r.symbolRegistry.getValue(t[1]);if(!s)throw new Error("Trying to deserialize unknown symbol");return s}),Ks=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((e,t)=>(e[t.name]=t,e),{}),Mr=ze(Ns,e=>["typed-array",e.constructor.name],e=>[...e],(e,t)=>{const r=Ks[t[1]];if(!r)throw new Error("Trying to deserialize unknown typed array");return new r(e)});function Dr(e,t){return e?.constructor?!!t.classRegistry.getIdentifier(e.constructor):!1}const Tr=ze(Dr,(e,t)=>["class",t.classRegistry.getIdentifier(e.constructor)],(e,t)=>{const r=t.classRegistry.getAllowedProps(e.constructor);if(!r)return{...e};const s={};return r.forEach(n=>{s[n]=e[n]}),s},(e,t,r)=>{const s=r.classRegistry.getValue(t[1]);if(!s)throw new Error(`Trying to deserialize unknown class '${t[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(s.prototype),e)}),Ar=ze((e,t)=>!!t.customTransformerRegistry.findApplicable(e),(e,t)=>["custom",t.customTransformerRegistry.findApplicable(e).name],(e,t)=>t.customTransformerRegistry.findApplicable(e).serialize(e),(e,t,r)=>{const s=r.customTransformerRegistry.findByName(t[1]);if(!s)throw new Error("Trying to deserialize unknown custom value");return s.deserialize(e)}),Bs=[Tr,qr,Ar,Mr],Tt=(e,t)=>{const r=Mt(Bs,n=>n.isApplicable(e,t));if(r)return{value:r.transform(e,t),type:r.annotation(e,t)};const s=Mt(Ir,n=>n.isApplicable(e,t));if(s)return{value:s.transform(e,t),type:s.annotation}},kr={};Ir.forEach(e=>{kr[e.annotation]=e});const Hs=(e,t,r)=>{if(ie(t))switch(t[0]){case"symbol":return qr.untransform(e,t,r);case"class":return Tr.untransform(e,t,r);case"custom":return Ar.untransform(e,t,r);case"typed-array":return Mr.untransform(e,t,r);default:throw new Error("Unknown transformation: "+t)}else{const s=kr[t];if(!s)throw new Error("Unknown transformation: "+t);return s.untransform(e,r)}},ye=(e,t)=>{if(t>e.size)throw new Error("index out of bounds");const r=e.keys();for(;t>0;)r.next(),t--;return r.next().value};function Fr(e){if(De(e,"__proto__"))throw new Error("__proto__ is not allowed as a property");if(De(e,"prototype"))throw new Error("prototype is not allowed as a property");if(De(e,"constructor"))throw new Error("constructor is not allowed as a property")}const zs=(e,t)=>{Fr(t);for(let r=0;r<t.length;r++){const s=t[r];if(Se(e))e=ye(e,+s);else if(xe(e)){const n=+s,i=+t[++r]==0?"key":"value",o=ye(e,n);switch(i){case"key":e=o;break;case"value":e=e.get(o);break}}else e=e[s]}return e},ct=(e,t,r)=>{if(Fr(t),t.length===0)return r(e);let s=e;for(let i=0;i<t.length-1;i++){const o=t[i];if(ie(s)){const a=+o;s=s[a]}else if(Pe(s))s=s[o];else if(Se(s)){const a=+o;s=ye(s,a)}else if(xe(s)){if(i===t.length-2)break;const u=+o,c=+t[++i]==0?"key":"value",f=ye(s,u);switch(c){case"key":s=f;break;case"value":s=s.get(f);break}}}const n=t[t.length-1];if(ie(s)?s[+n]=r(s[+n]):Pe(s)&&(s[n]=r(s[n])),Se(s)){const i=ye(s,+n),o=r(i);i!==o&&(s.delete(i),s.add(o))}if(xe(s)){const i=+t[t.length-2],o=ye(s,i);switch(+n==0?"key":"value"){case"key":{const u=r(o);s.set(u,s.get(o)),u!==o&&s.delete(o);break}case"value":{s.set(o,r(s.get(o)));break}}}return e};function lt(e,t,r=[]){if(!e)return;if(!ie(e)){ve(e,(i,o)=>lt(i,t,[...r,...Oe(o)]));return}const[s,n]=e;n&&ve(n,(i,o)=>{lt(i,t,[...r,...Oe(o)])}),t(s,r)}function Vs(e,t,r){return lt(t,(s,n)=>{e=ct(e,n,i=>Hs(i,s,r))}),e}function Ws(e,t){function r(s,n){const i=zs(e,Oe(n));s.map(Oe).forEach(o=>{e=ct(e,o,()=>i)})}if(ie(t)){const[s,n]=t;s.forEach(i=>{e=ct(e,Oe(i),()=>e)}),n&&ve(n,r)}else ve(t,r);return e}const Gs=(e,t)=>Pe(e)||ie(e)||xe(e)||Se(e)||Dr(e,t);function Ys(e,t,r){const s=r.get(e);s?s.push(t):r.set(e,[t])}function Js(e,t){const r={};let s;return e.forEach(n=>{if(n.length<=1)return;t||(n=n.map(a=>a.map(String)).sort((a,u)=>a.length-u.length));const[i,...o]=n;i.length===0?s=o.map(Je):r[Je(i)]=o.map(Je)}),s?ut(r)?[s]:[s,r]:ut(r)?void 0:r}const Ur=(e,t,r,s,n=[],i=[],o=new Map)=>{const a=Fs(e);if(!a){Ys(e,n,t);const v=o.get(e);if(v)return s?{transformedValue:null}:v}if(!Gs(e,r)){const v=Tt(e,r),b=v?{transformedValue:v.value,annotations:[v.type]}:{transformedValue:e};return a||o.set(e,b),b}if(De(i,e))return{transformedValue:null};const u=Tt(e,r),c=u?.value??e,f=ie(c)?[]:{},l={};ve(c,(v,b)=>{if(b==="__proto__"||b==="constructor"||b==="prototype")throw new Error(`Detected property ${b}. This is a prototype pollution risk, please remove it from your object.`);const h=Ur(v,t,r,s,[...n,b],[...i,e],o);f[b]=h.transformedValue,ie(h.annotations)?l[b]=h.annotations:Pe(h.annotations)&&ve(h.annotations,(R,j)=>{l[Qr(b)+"."+j]=R})});const p=ut(l)?{transformedValue:f,annotations:u?[u.type]:void 0}:{transformedValue:f,annotations:u?[u.type,l]:l};return a||o.set(e,p),p};function $r(e){return Object.prototype.toString.call(e).slice(8,-1)}function At(e){return $r(e)==="Array"}function Xs(e){if($r(e)!=="Object")return!1;const t=Object.getPrototypeOf(e);return!!t&&t.constructor===Object&&t===Object.prototype}function Zs(e,t,r,s,n){const i={}.propertyIsEnumerable.call(s,t)?"enumerable":"nonenumerable";i==="enumerable"&&(e[t]=r),n&&i==="nonenumerable"&&Object.defineProperty(e,t,{value:r,enumerable:!1,writable:!0,configurable:!0})}function ft(e,t={}){if(At(e))return e.map(n=>ft(n,t));if(!Xs(e))return e;const r=Object.getOwnPropertyNames(e),s=Object.getOwnPropertySymbols(e);return[...r,...s].reduce((n,i)=>{if(At(t.props)&&!t.props.includes(i))return n;const o=e[i],a=ft(o,t);return Zs(n,i,a,e,t.nonenumerable),n},{})}class C{constructor({dedupe:t=!1}={}){this.classRegistry=new Rs,this.symbolRegistry=new Cr(r=>r.description??""),this.customTransformerRegistry=new Es,this.allowedErrorProps=[],this.dedupe=t}serialize(t){const r=new Map,s=Ur(t,r,this,this.dedupe),n={json:s.transformedValue};s.annotations&&(n.meta={...n.meta,values:s.annotations});const i=Js(r,this.dedupe);return i&&(n.meta={...n.meta,referentialEqualities:i}),n}deserialize(t){const{json:r,meta:s}=t;let n=ft(r);return s?.values&&(n=Vs(n,s.values,this)),s?.referentialEqualities&&(n=Ws(n,s.referentialEqualities)),n}stringify(t){return JSON.stringify(this.serialize(t))}parse(t){return this.deserialize(JSON.parse(t))}registerClass(t,r){this.classRegistry.register(t,r)}registerSymbol(t,r){this.symbolRegistry.register(t,r)}registerCustom(t,r){this.customTransformerRegistry.register({name:r,...t})}allowErrorProps(...t){this.allowedErrorProps.push(...t)}}C.defaultInstance=new C;C.serialize=C.defaultInstance.serialize.bind(C.defaultInstance);C.deserialize=C.defaultInstance.deserialize.bind(C.defaultInstance);C.stringify=C.defaultInstance.stringify.bind(C.defaultInstance);C.parse=C.defaultInstance.parse.bind(C.defaultInstance);C.registerClass=C.defaultInstance.registerClass.bind(C.defaultInstance);C.registerSymbol=C.defaultInstance.registerSymbol.bind(C.defaultInstance);C.registerCustom=C.defaultInstance.registerCustom.bind(C.defaultInstance);C.allowErrorProps=C.defaultInstance.allowErrorProps.bind(C.defaultInstance);C.serialize;C.deserialize;C.stringify;C.parse;C.registerClass;C.registerCustom;C.registerSymbol;C.allowErrorProps;var ei=Object.create,Nr=Object.defineProperty,ti=Object.getOwnPropertyDescriptor,Lr=Object.getOwnPropertyNames,ri=Object.getPrototypeOf,ni=Object.prototype.hasOwnProperty,oe=(e,t)=>function(){return t||(0,e[Lr(e)[0]])((t={exports:{}}).exports,t),t.exports},si=(e,t,r,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(var n=Lr(t),i=0,o=n.length,a;i<o;i++)a=n[i],!ni.call(e,a)&&a!==r&&Nr(e,a,{get:(u=>t[u]).bind(null,a),enumerable:!(s=ti(t,a))||s.enumerable});return e},he=(e,t,r)=>(r=e!=null?ei(ri(e)):{},si(t||!e||!e.__esModule?Nr(r,"default",{value:e,enumerable:!0}):r,e)),ii=oe({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutPropertiesLoose.js"(e,t){function r(s,n){if(s==null)return{};var i={};for(var o in s)if({}.hasOwnProperty.call(s,o)){if(n.includes(o))continue;i[o]=s[o]}return i}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),oi=oe({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectWithoutProperties.js"(e,t){var r=ii();function s(n,i){if(n==null)return{};var o,a,u=r(n,i);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(n);for(a=0;a<c.length;a++)o=c[a],i.includes(o)||{}.propertyIsEnumerable.call(n,o)&&(u[o]=n[o])}return u}t.exports=s,t.exports.__esModule=!0,t.exports.default=t.exports}}),Kr=oe({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js"(e,t){function r(s){"@babel/helpers - typeof";return t.exports=r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},t.exports.__esModule=!0,t.exports.default=t.exports,r(s)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),ai=oe({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js"(e,t){var r=Kr().default;function s(n,i){if(r(n)!="object"||!n)return n;var o=n[Symbol.toPrimitive];if(o!==void 0){var a=o.call(n,i||"default");if(r(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return(i==="string"?String:Number)(n)}t.exports=s,t.exports.__esModule=!0,t.exports.default=t.exports}}),ui=oe({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js"(e,t){var r=Kr().default,s=ai();function n(i){var o=s(i,"string");return r(o)=="symbol"?o:o+""}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}}),ci=oe({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js"(e,t){var r=ui();function s(n,i,o){return(i=r(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o,n}t.exports=s,t.exports.__esModule=!0,t.exports.default=t.exports}}),je=oe({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js"(e,t){var r=ci();function s(i,o){var a=Object.keys(i);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(i);o&&(u=u.filter(function(c){return Object.getOwnPropertyDescriptor(i,c).enumerable})),a.push.apply(a,u)}return a}function n(i){for(var o=1;o<arguments.length;o++){var a=arguments[o]!=null?arguments[o]:{};o%2?s(Object(a),!0).forEach(function(u){r(i,u,a[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(a)):s(Object(a)).forEach(function(u){Object.defineProperty(i,u,Object.getOwnPropertyDescriptor(a,u))})}return i}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}}),li=he(oi(),1),kt=he(je(),1);const fi=["cursor","direction"];function Z(e,t,r){const s=e.flatMap(n=>n.split("."));if(!t&&(!r||r==="any"))return s.length?[s]:[];if(r==="infinite"&&le(t)&&("direction"in t||"cursor"in t)){const{cursor:n,direction:i}=t,o=(0,li.default)(t,fi);return[s,{input:o,type:"infinite"}]}return[s,(0,kt.default)((0,kt.default)({},typeof t<"u"&&t!==z&&{input:t}),r&&r!=="any"&&{type:r})]}function Te(e){return Z(e,void 0,"any")}function di(e){return Be(({path:t,args:r})=>{var s;const n=[...t],i=n.pop();if(i==="useMutation")return e[i](n,...r);if(i==="_def")return{path:n};const[o,...a]=r,u=(s=a[0])!==null&&s!==void 0?s:{};return e[i](n,o,u)})}var Xe;const hi=["client","ssrContext","ssrState","abortOnUnmount"],pi=(Xe=_.createContext)===null||Xe===void 0?void 0:Xe.call(Hr,null),yi=e=>{switch(e){case"queryOptions":case"fetch":case"ensureData":case"prefetch":case"getData":case"setData":case"setQueriesData":return"query";case"infiniteQueryOptions":case"fetchInfinite":case"prefetchInfinite":case"getInfiniteData":case"setInfiniteData":return"infinite";case"setMutationDefaults":case"getMutationDefaults":case"isMutating":case"cancel":case"invalidate":case"refetch":case"reset":return"any"}};function vi(e){return Be(t=>{const r=[...t.path],s=r.pop(),n=[...t.args],i=n.shift(),o=yi(s),a=Z(r,i,o);return{infiniteQueryOptions:()=>e.infiniteQueryOptions(r,a,n[0]),queryOptions:()=>e.queryOptions(r,a,...n),fetch:()=>e.fetchQuery(a,...n),fetchInfinite:()=>e.fetchInfiniteQuery(a,n[0]),prefetch:()=>e.prefetchQuery(a,...n),prefetchInfinite:()=>e.prefetchInfiniteQuery(a,n[0]),ensureData:()=>e.ensureQueryData(a,...n),invalidate:()=>e.invalidateQueries(a,...n),reset:()=>e.resetQueries(a,...n),refetch:()=>e.refetchQueries(a,...n),cancel:()=>e.cancelQuery(a,...n),setData:()=>{e.setQueryData(a,n[0],n[1])},setQueriesData:()=>e.setQueriesData(a,n[0],n[1],n[2]),setInfiniteData:()=>{e.setInfiniteQueryData(a,n[0],n[1])},getData:()=>e.getQueryData(a),getInfiniteData:()=>e.getInfiniteQueryData(a),setMutationDefaults:()=>e.setMutationDefaults(Te(r),i),getMutationDefaults:()=>e.getMutationDefaults(Te(r)),isMutating:()=>e.isMutating({mutationKey:Te(r)})}[s]()})}function mi(e){const t=Sr(e.client),r=vi(e);return bt(s=>{const n=s;return n==="client"?t:hi.includes(n)?e[n]:r[s]})}var bi=he(je(),1);function Ft(e){const t=e instanceof He?e:Ot(e);return Be(r=>{const s=r.path,n=s.join("."),[i,o]=r.args;return(0,bi.default)({queryKey:Z(s,i,"query"),queryFn:()=>t.query(n,i,o?.trpc)},o)})}var Ze=he(je(),1);function H(e,t,r){var s;const n=e[0];let i=(s=e[1])===null||s===void 0?void 0:s.input;if(r){var o;i=(0,Ze.default)((0,Ze.default)((0,Ze.default)({},(o=i)!==null&&o!==void 0?o:{}),r.pageParam?{cursor:r.pageParam}:{}),{},{direction:r.direction})}return[n.join("."),i,t?.trpc]}var gi=oe({"../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js"(e,t){function r(n){var i,o,a,u=2;for(typeof Symbol<"u"&&(o=Symbol.asyncIterator,a=Symbol.iterator);u--;){if(o&&(i=n[o])!=null)return i.call(n);if(a&&(i=n[a])!=null)return new s(i.call(n));o="@@asyncIterator",a="@@iterator"}throw new TypeError("Object is not async iterable")}function s(n){function i(o){if(Object(o)!==o)return Promise.reject(new TypeError(o+" is not an object."));var a=o.done;return Promise.resolve(o.value).then(function(u){return{value:u,done:a}})}return s=function(a){this.s=a,this.n=a.next},s.prototype={s:null,n:null,next:function(){return i(this.n.apply(this.s,arguments))},return:function(a){var u=this.s.return;return u===void 0?Promise.resolve({value:a,done:!0}):i(u.apply(this.s,arguments))},throw:function(a){var u=this.s.return;return u===void 0?Promise.reject(a):i(u.apply(this.s,arguments))}},new s(n)}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports}}),Oi=he(gi(),1);function dt(e){return{path:e.path.join(".")}}function be(e){const t=dt(e);return _.useMemo(()=>t,[t])}async function Br(e,t,r){const n=t.getQueryCache().build(t,{queryKey:r});n.setState({data:[],status:"success"});const i=[];var o=!1,a=!1,u;try{for(var c=(0,Oi.default)(e),f;o=!(f=await c.next()).done;o=!1){const l=f.value;i.push(l),n.setState({data:[...i]})}}catch(l){a=!0,u=l}finally{try{o&&c.return!=null&&await c.return()}finally{if(a)throw u}}return i}var E=he(je(),1);function wi(e){const{client:t,queryClient:r}=e,s=t instanceof He?t:Ot(t);return{infiniteQueryOptions:(n,i,o)=>{var a,u;const c=((a=i[1])===null||a===void 0?void 0:a.input)===z,f=async l=>{var p;const v=(0,E.default)((0,E.default)({},o),{},{trpc:(0,E.default)((0,E.default)({},o?.trpc),!(o==null||(p=o.trpc)===null||p===void 0)&&p.abortOnUnmount?{signal:l.signal}:{signal:null})});return await s.query(...H(i,v,{direction:l.direction,pageParam:l.pageParam}))};return Object.assign((0,E.default)((0,E.default)({},o),{},{initialData:o?.initialData,queryKey:i,queryFn:c?z:f,initialPageParam:(u=o?.initialCursor)!==null&&u!==void 0?u:null}),{trpc:dt({path:n})})},queryOptions:(n,i,o)=>{var a;const u=((a=i[1])===null||a===void 0?void 0:a.input)===z,c=async f=>{var l;const p=(0,E.default)((0,E.default)({},o),{},{trpc:(0,E.default)((0,E.default)({},o?.trpc),!(o==null||(l=o.trpc)===null||l===void 0)&&l.abortOnUnmount?{signal:f.signal}:{signal:null})}),v=await s.query(...H(i,p));return fr(v)?Br(v,r,i):v};return Object.assign((0,E.default)((0,E.default)({},o),{},{initialData:o?.initialData,queryKey:i,queryFn:u?z:c}),{trpc:dt({path:n})})},fetchQuery:(n,i)=>r.fetchQuery((0,E.default)((0,E.default)({},i),{},{queryKey:n,queryFn:()=>s.query(...H(n,i))})),fetchInfiniteQuery:(n,i)=>{var o;return r.fetchInfiniteQuery((0,E.default)((0,E.default)({},i),{},{queryKey:n,queryFn:({pageParam:a,direction:u})=>s.query(...H(n,i,{pageParam:a,direction:u})),initialPageParam:(o=i?.initialCursor)!==null&&o!==void 0?o:null}))},prefetchQuery:(n,i)=>r.prefetchQuery((0,E.default)((0,E.default)({},i),{},{queryKey:n,queryFn:()=>s.query(...H(n,i))})),prefetchInfiniteQuery:(n,i)=>{var o;return r.prefetchInfiniteQuery((0,E.default)((0,E.default)({},i),{},{queryKey:n,queryFn:({pageParam:a,direction:u})=>s.query(...H(n,i,{pageParam:a,direction:u})),initialPageParam:(o=i?.initialCursor)!==null&&o!==void 0?o:null}))},ensureQueryData:(n,i)=>r.ensureQueryData((0,E.default)((0,E.default)({},i),{},{queryKey:n,queryFn:()=>s.query(...H(n,i))})),invalidateQueries:(n,i,o)=>r.invalidateQueries((0,E.default)((0,E.default)({},i),{},{queryKey:n}),o),resetQueries:(n,i,o)=>r.resetQueries((0,E.default)((0,E.default)({},i),{},{queryKey:n}),o),refetchQueries:(n,i,o)=>r.refetchQueries((0,E.default)((0,E.default)({},i),{},{queryKey:n}),o),cancelQuery:(n,i)=>r.cancelQueries({queryKey:n},i),setQueryData:(n,i,o)=>r.setQueryData(n,i,o),setQueriesData:(n,i,o,a)=>r.setQueriesData((0,E.default)((0,E.default)({},i),{},{queryKey:n}),o,a),getQueryData:n=>r.getQueryData(n),setInfiniteQueryData:(n,i,o)=>r.setQueryData(n,i,o),getInfiniteQueryData:n=>r.getQueryData(n),setMutationDefaults:(n,i)=>{const o=n[0],a=u=>s.mutation(...H([o,{input:u}],e));return r.setMutationDefaults(n,typeof i=="function"?i({canonicalMutationFn:a}):i)},getMutationDefaults:n=>r.getMutationDefaults(n),isMutating:n=>r.isMutating((0,E.default)((0,E.default)({},n),{},{exact:!0}))}}var y=he(je());const Ut=(e,t)=>new Proxy(e,{get(s,n){return t(n),s[n]}});function Pi(e){var t,r;const s=(t=void 0)!==null&&t!==void 0?t:O=>O.originalFn(),n=(r=void 0)!==null&&r!==void 0?r:pi,i=_r,o=O=>{var g;const{abortOnUnmount:d=!1,queryClient:m,ssrContext:P}=O,[w,x]=_.useState((g=O.ssrState)!==null&&g!==void 0?g:!1),S=O.client instanceof He?O.client:Ot(O.client),M=_.useMemo(()=>wi({client:S,queryClient:m}),[S,m]),Q=_.useMemo(()=>(0,y.default)({abortOnUnmount:d,queryClient:m,client:S,ssrContext:P??null,ssrState:w},M),[d,S,M,m,P,w]);return _.useEffect(()=>{x(T=>T?"mounted":!1)},[]),Ae.jsx(n.Provider,{value:Q,children:O.children})};function a(){const O=_.useContext(n);if(!O)throw new Error("Unable to find tRPC Context. Did you forget to wrap your App inside `withTRPC` HoC?");return O}function u(O,g){var d;const{queryClient:m,ssrState:P}=a();return P&&P!=="mounted"&&((d=m.getQueryCache().find({queryKey:O}))===null||d===void 0?void 0:d.state.status)==="error"?(0,y.default)({retryOnMount:!1},g):g}function c(O,g,d){var m,P,w,x,S;const M=a(),{abortOnUnmount:Q,client:T,ssrState:q,queryClient:k,prefetchQuery:F}=M,L=Z(O,g,"query"),te=k.getQueryDefaults(L),N=g===z;typeof window>"u"&&q==="prepass"&&(d==null||(m=d.trpc)===null||m===void 0?void 0:m.ssr)!==!1&&((P=d?.enabled)!==null&&P!==void 0?P:te?.enabled)!==!1&&!N&&!k.getQueryCache().find({queryKey:L})&&F(L,d);const V=u(L,(0,y.default)((0,y.default)({},te),d)),A=(w=(x=d==null||(S=d.trpc)===null||S===void 0?void 0:S.abortOnUnmount)!==null&&x!==void 0?x:void 0)!==null&&w!==void 0?w:Q,D=bn((0,y.default)((0,y.default)({},V),{},{queryKey:L,queryFn:N?g:async K=>{const ae=(0,y.default)((0,y.default)({},V),{},{trpc:(0,y.default)((0,y.default)({},V?.trpc),A?{signal:K.signal}:{signal:null})}),Ve=await T.query(...H(L,ae));return fr(Ve)?Br(Ve,k,L):Ve}}),k);return D.trpc=be({path:O}),D}function f(O,g,d){var m,P,w;const x=a(),S=Z(O,g,"query"),M=g===z,Q=(m=(P=d==null||(w=d.trpc)===null||w===void 0?void 0:w.abortOnUnmount)!==null&&P!==void 0?P:void 0)!==null&&m!==void 0?m:x.abortOnUnmount;Pn((0,y.default)((0,y.default)({},d),{},{queryKey:S,queryFn:M?g:T=>{const q={trpc:(0,y.default)((0,y.default)({},d?.trpc),Q?{signal:T.signal}:{})};return x.client.query(...H(S,q))}}))}function l(O,g,d){var m,P,w;const x=a(),S=Z(O,g,"query"),M=(m=(P=d==null||(w=d.trpc)===null||w===void 0?void 0:w.abortOnUnmount)!==null&&P!==void 0?P:void 0)!==null&&m!==void 0?m:x.abortOnUnmount,Q=gn((0,y.default)((0,y.default)({},d),{},{queryKey:S,queryFn:T=>{const q=(0,y.default)((0,y.default)({},d),{},{trpc:(0,y.default)((0,y.default)({},d?.trpc),M?{signal:T.signal}:{signal:null})});return x.client.query(...H(S,q))}}),x.queryClient);return Q.trpc=be({path:O}),[Q.data,Q]}function p(O,g){const{client:d,queryClient:m}=a(),P=Te(O),w=m.defaultMutationOptions(m.getMutationDefaults(P)),x=Sn((0,y.default)((0,y.default)({},g),{},{mutationKey:P,mutationFn:S=>d.mutation(...H([O,{input:S}],g)),onSuccess(...S){var M,Q;return s({originalFn:()=>{var q,k,F;return(q=g==null||(k=g.onSuccess)===null||k===void 0?void 0:k.call(g,...S))!==null&&q!==void 0?q:w==null||(F=w.onSuccess)===null||F===void 0?void 0:F.call(w,...S)},queryClient:m,meta:(M=(Q=g?.meta)!==null&&Q!==void 0?Q:w?.meta)!==null&&M!==void 0?M:{}})}}),m);return x.trpc=be({path:O}),x}const v={data:void 0,error:null,status:"idle"},b={data:void 0,error:null,status:"connecting"};/* istanbul ignore next -- @preserve */function h(O,g,d){var m;const P=(m=d?.enabled)!==null&&m!==void 0?m:g!==z,w=se(Z(O,g,"any")),{client:x}=a(),S=_.useRef(d);_.useEffect(()=>{S.current=d});const[M]=_.useState(new Set([])),Q=_.useCallback(N=>{M.add(N)},[M]),T=_.useRef(null),q=_.useCallback(N=>{const V=F.current,A=F.current=N(V);let D=!1;for(const K of M)if(V[K]!==A[K]){D=!0;break}D&&te(Ut(A,Q))},[Q,M]),k=_.useCallback(()=>{var N;if((N=T.current)===null||N===void 0||N.unsubscribe(),!P){q(()=>(0,y.default)((0,y.default)({},v),{},{reset:k}));return}q(()=>(0,y.default)((0,y.default)({},b),{},{reset:k}));const V=x.subscription(O.join("."),g??void 0,{onStarted:()=>{var A,D;(A=(D=S.current).onStarted)===null||A===void 0||A.call(D),q(K=>(0,y.default)((0,y.default)({},K),{},{status:"pending",error:null}))},onData:A=>{var D,K;(D=(K=S.current).onData)===null||D===void 0||D.call(K,A),q(ae=>(0,y.default)((0,y.default)({},ae),{},{status:"pending",data:A,error:null}))},onError:A=>{var D,K;(D=(K=S.current).onError)===null||D===void 0||D.call(K,A),q(ae=>(0,y.default)((0,y.default)({},ae),{},{status:"error",error:A}))},onConnectionStateChange:A=>{q(D=>{switch(A.state){case"idle":return(0,y.default)((0,y.default)({},D),{},{status:A.state,error:null,data:void 0});case"connecting":return(0,y.default)((0,y.default)({},D),{},{error:A.error,status:A.state});case"pending":return D}})},onComplete:()=>{var A,D;(A=(D=S.current).onComplete)===null||A===void 0||A.call(D),q(K=>(0,y.default)((0,y.default)({},K),{},{status:"idle",error:null,data:void 0}))}});T.current=V},[x,w,P,q]);_.useEffect(()=>(k(),()=>{var N;(N=T.current)===null||N===void 0||N.unsubscribe()}),[k]);const F=_.useRef(P?(0,y.default)((0,y.default)({},b),{},{reset:k}):(0,y.default)((0,y.default)({},v),{},{reset:k})),[L,te]=_.useState(Ut(F.current,Q));return L}function R(O,g,d){var m,P,w,x,S;const{client:M,ssrState:Q,prefetchInfiniteQuery:T,queryClient:q,abortOnUnmount:k}=a(),F=Z(O,g,"infinite"),L=q.getQueryDefaults(F),te=g===z;typeof window>"u"&&Q==="prepass"&&(d==null||(m=d.trpc)===null||m===void 0?void 0:m.ssr)!==!1&&((P=d?.enabled)!==null&&P!==void 0?P:L?.enabled)!==!1&&!te&&!q.getQueryCache().find({queryKey:F})&&T(F,(0,y.default)((0,y.default)({},L),d));const N=u(F,(0,y.default)((0,y.default)({},L),d)),V=(w=d==null||(x=d.trpc)===null||x===void 0?void 0:x.abortOnUnmount)!==null&&w!==void 0?w:k,A=_n((0,y.default)((0,y.default)({},N),{},{initialPageParam:(S=d.initialCursor)!==null&&S!==void 0?S:null,persister:d.persister,queryKey:F,queryFn:te?g:D=>{var K;const ae=(0,y.default)((0,y.default)({},N),{},{trpc:(0,y.default)((0,y.default)({},N?.trpc),V?{signal:D.signal}:{signal:null})});return M.query(...H(F,ae,{pageParam:(K=D.pageParam)!==null&&K!==void 0?K:d.initialCursor,direction:D.direction}))}}),q);return A.trpc=be({path:O}),A}function j(O,g,d){var m,P,w;const x=a(),S=Z(O,g,"infinite"),M=x.queryClient.getQueryDefaults(S),Q=g===z,T=u(S,(0,y.default)((0,y.default)({},M),d)),q=(m=d==null||(P=d.trpc)===null||P===void 0?void 0:P.abortOnUnmount)!==null&&m!==void 0?m:x.abortOnUnmount;xn((0,y.default)((0,y.default)({},d),{},{initialPageParam:(w=d.initialCursor)!==null&&w!==void 0?w:null,queryKey:S,queryFn:Q?g:k=>{var F;const L=(0,y.default)((0,y.default)({},T),{},{trpc:(0,y.default)((0,y.default)({},T?.trpc),q?{signal:k.signal}:{})});return x.client.query(...H(S,L,{pageParam:(F=k.pageParam)!==null&&F!==void 0?F:d.initialCursor,direction:k.direction}))}}))}function I(O,g,d){var m,P,w;const x=a(),S=Z(O,g,"infinite"),M=x.queryClient.getQueryDefaults(S),Q=u(S,(0,y.default)((0,y.default)({},M),d)),T=(m=d==null||(P=d.trpc)===null||P===void 0?void 0:P.abortOnUnmount)!==null&&m!==void 0?m:x.abortOnUnmount,q=On((0,y.default)((0,y.default)({},d),{},{initialPageParam:(w=d.initialCursor)!==null&&w!==void 0?w:null,queryKey:S,queryFn:k=>{var F;const L=(0,y.default)((0,y.default)({},Q),{},{trpc:(0,y.default)((0,y.default)({},Q?.trpc),T?{signal:k.signal}:{})});return x.client.query(...H(S,L,{pageParam:(F=k.pageParam)!==null&&F!==void 0?F:d.initialCursor,direction:k.direction}))}}),x.queryClient);return q.trpc=be({path:O}),[q.data,q]}return{Provider:o,createClient:i,useContext:a,useUtils:a,useQuery:c,usePrefetchQuery:f,useSuspenseQuery:l,useQueries:(O,g)=>{const{ssrState:d,queryClient:m,prefetchQuery:P,client:w}=a(),x=Ft(w),S=O(x);if(typeof window>"u"&&d==="prepass")for(const Q of S){var M;const T=Q;((M=T.trpc)===null||M===void 0?void 0:M.ssr)!==!1&&!m.getQueryCache().find({queryKey:T.queryKey})&&P(T.queryKey,T)}return ar({queries:S.map(Q=>(0,y.default)((0,y.default)({},Q),{},{queryKey:Q.queryKey})),combine:g?.combine},m)},useSuspenseQueries:O=>{const{queryClient:g,client:d}=a(),m=Ft(d),P=O(m),w=wn({queries:P.map(x=>(0,y.default)((0,y.default)({},x),{},{queryFn:x.queryFn,queryKey:x.queryKey}))},g);return[w.map(x=>x.data),w]},useMutation:p,useSubscription:h,useInfiniteQuery:R,usePrefetchInfiniteQuery:j,useSuspenseInfiniteQuery:I}}function xi(e){const t=di(e);return bt(r=>r==="useContext"||r==="useUtils"?()=>{const s=e.useUtils();return _.useMemo(()=>mi(s),[s])}:e.hasOwnProperty(r)?e[r]:t[r])}function Si(e){const t=Pi();return xi(t)}const _i="http://localhost:3000",$t=Si();_r({links:[wr({enabled:()=>!1}),Or({url:`${_i}/trpc`,transformer:C,fetch:(e,t)=>fetch(e,{...t,credentials:"include"})})]});const Ri="http://localhost:3000";function Ei({children:e}){const[t]=_.useState(()=>new cn({defaultOptions:{queries:{staleTime:3e4,gcTime:3e5,retry:1,refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1}}})),[r]=_.useState(()=>$t.createClient({links:[wr({enabled:()=>!1}),Or({url:`${Ri}/trpc`,transformer:C,fetch:(s,n)=>fetch(s,{...n,credentials:"include"})})]}));return Ae.jsx($t.Provider,{client:r,queryClient:t,children:Ae.jsx(yn,{client:t,children:e})})}export{Ei as T,$t as t};
