// Скрипт для создания тестовых данных через API
// Запускать из браузерной консоли на странице http://localhost:4321/admin/parts

async function createTestData() {
  console.log('🚀 Создание тестовых данных...');
  
  try {
    // Получаем tRPC клиент из глобального объекта (если доступен)
    // Или используем fetch для прямых запросов к API
    
    // Создаем тестовые категории
    const categories = [
      {
        name: 'Двигатель',
        slug: 'engine',
        description: 'Запчасти для двигателя',
        level: 0,
        path: '/engine'
      },
      {
        name: 'Гидравлика',
        slug: 'hydraulics', 
        description: 'Гидравлические компоненты',
        level: 0,
        path: '/hydraulics'
      },
      {
        name: 'Сальники и уплотнения',
        slug: 'seals',
        description: 'Уплотнительные элементы',
        level: 0,
        path: '/seals'
      }
    ];

    console.log('📁 Создание категорий...');
    const createdCategories = [];
    
    for (const category of categories) {
      try {
        const response = await fetch('http://localhost:3000/trpc/crud.partCategory.create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            data: category
          })
        });
        
        if (response.ok) {
          const result = await response.json();
          createdCategories.push(result.result.data);
          console.log(`✅ Создана категория: ${category.name}`);
        } else {
          console.log(`⚠️ Категория ${category.name} уже существует или ошибка создания`);
        }
      } catch (err) {
        console.log(`⚠️ Ошибка создания категории ${category.name}:`, err.message);
      }
    }

    // Создаем тестовые бренды
    const brands = [
      {
        name: 'Caterpillar',
        slug: 'caterpillar',
        country: 'США',
        isOem: true
      },
      {
        name: 'Komatsu',
        slug: 'komatsu',
        country: 'Япония',
        isOem: true
      },
      {
        name: 'SKF',
        slug: 'skf',
        country: 'Швеция',
        isOem: false
      }
    ];

    console.log('🏷️ Создание брендов...');
    const createdBrands = [];
    
    for (const brand of brands) {
      try {
        const response = await fetch('http://localhost:3000/trpc/crud.brand.create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            data: brand
          })
        });
        
        if (response.ok) {
          const result = await response.json();
          createdBrands.push(result.result.data);
          console.log(`✅ Создан бренд: ${brand.name}`);
        } else {
          console.log(`⚠️ Бренд ${brand.name} уже существует или ошибка создания`);
        }
      } catch (err) {
        console.log(`⚠️ Ошибка создания бренда ${brand.name}:`, err.message);
      }
    }

    // Получаем созданные категории для создания запчастей
    const categoriesResponse = await fetch('http://localhost:3000/trpc/crud.partCategory.findMany');
    const categoriesData = await categoriesResponse.json();
    const availableCategories = categoriesData.result.data;

    if (availableCategories.length === 0) {
      console.log('❌ Нет доступных категорий для создания запчастей');
      return;
    }

    // Создаем тестовые запчасти
    const parts = [
      {
        name: 'Сальник коленвала передний',
        partCategoryId: availableCategories.find(c => c.slug === 'seals')?.id || availableCategories[0].id,
        level: 0,
        path: '/seals/crankshaft-front'
      },
      {
        name: 'Фильтр масляный',
        partCategoryId: availableCategories.find(c => c.slug === 'engine')?.id || availableCategories[0].id,
        level: 0,
        path: '/engine/oil-filter'
      },
      {
        name: 'Гидравлический насос',
        partCategoryId: availableCategories.find(c => c.slug === 'hydraulics')?.id || availableCategories[0].id,
        level: 0,
        path: '/hydraulics/pump'
      },
      {
        name: 'Прокладка головки блока цилиндров',
        partCategoryId: availableCategories.find(c => c.slug === 'engine')?.id || availableCategories[0].id,
        level: 0,
        path: '/engine/head-gasket'
      }
    ];

    console.log('🔧 Создание запчастей...');
    
    for (const part of parts) {
      try {
        const response = await fetch('http://localhost:3000/trpc/crud.part.create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            data: part
          })
        });
        
        if (response.ok) {
          const result = await response.json();
          console.log(`✅ Создана запчасть: ${part.name}`);
        } else {
          console.log(`⚠️ Запчасть ${part.name} уже существует или ошибка создания`);
        }
      } catch (err) {
        console.log(`⚠️ Ошибка создания запчасти ${part.name}:`, err.message);
      }
    }

    console.log('🎉 Создание тестовых данных завершено!');
    console.log('🔄 Обновите страницу, чтобы увидеть новые данные');
    
  } catch (error) {
    console.error('❌ Критическая ошибка:', error);
  }
}

// Запускаем создание тестовых данных
console.log('📋 Для создания тестовых данных выполните: createTestData()');

// Экспортируем функцию в глобальную область
window.createTestData = createTestData;
