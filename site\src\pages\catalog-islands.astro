---
import MainLayout from '@/layouts/MainLayout.astro'
import SearchHeaderIslandWrapper from '@/components/catalog/islands/SearchHeaderIslandWrapper.tsx'
import FiltersIslandWrapper from '@/components/catalog/islands/FiltersIslandWrapper.tsx'
import ResultsIslandWrapper from '@/components/catalog/islands/ResultsIslandWrapper.tsx'
import AIAssistantIslandWrapper from '@/components/catalog/islands/AIAssistantIslandWrapper.tsx'
import ItemDetailsIslandWrapper from '@/components/catalog/islands/ItemDetailsIslandWrapper.tsx'
---

<MainLayout title="Каталог (React Islands архитектура)">
  <div class="min-h-screen bg-background">
    <!-- Заголовок и поиск - загружается сразу для лучшего UX -->
    <SearchHeaderIslandWrapper client:load />

    <!-- Основной контент -->
    <div class="flex">
      <!-- Фильтры - загружается при видимости для экономии ресурсов -->
      <FiltersIslandWrapper client:visible />

      <!-- Результаты поиска - приоритетная загрузка -->
      <ResultsIslandWrapper client:load />
    </div>

    <!-- AI Ассистент - загружается только при взаимодействии -->
    <AIAssistantIslandWrapper client:idle />

    <!-- Детали элемента - загружается только при взаимодействии -->
    <ItemDetailsIslandWrapper client:idle />
  </div>
</MainLayout>
