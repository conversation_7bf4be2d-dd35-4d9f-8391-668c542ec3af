import { d as defineMiddleware, s as sequence } from './chunks/index_BYVtJ9ki.mjs';
import { betterAuth } from 'better-auth';
import { prismaAdapter } from 'better-auth/adapters/prisma';
import require$$0$1 from '.prisma/client/default';
import crypto$1 from 'crypto';
import require$$0$2 from 'zod/v3';
import { unlink } from 'fs/promises';
import { join } from 'path';
import { phoneNumber, anonymous, username, admin as admin$1 } from 'better-auth/plugins';
import { createAccessControl } from 'better-auth/plugins/access';
import { defaultStatements, adminAc } from 'better-auth/plugins/admin/access';
import 'es-module-lexer';
import './chunks/astro-designed-error-pages_NtkQcIoZ.mjs';
import 'kleur/colors';
import './chunks/astro/server_D7mwM5eH.mjs';
import 'clsx';
import 'cookie';

function getAugmentedNamespace(n) {
  if (Object.prototype.hasOwnProperty.call(n, '__esModule')) return n;
  var f = n.default;
	if (typeof f == "function") {
		var a = function a () {
			if (this instanceof a) {
        return Reflect.construct(f, arguments, this.constructor);
			}
			return f.apply(this, arguments);
		};
		a.prototype = f.prototype;
  } else a = {};
  Object.defineProperty(a, '__esModule', {value: true});
	Object.keys(n).forEach(function (k) {
		var d = Object.getOwnPropertyDescriptor(n, k);
		Object.defineProperty(a, k, d.get ? d : {
			enumerable: true,
			get: function () {
				return n[k];
			}
		});
	});
	return a;
}

var _default;
var hasRequired_default;

function require_default () {
	if (hasRequired_default) return _default;
	hasRequired_default = 1;
	_default = {
	  ...require$$0$1,
	};
	return _default;
}

var _defaultExports = /*@__PURE__*/ require_default();

var runtime = {};

var constants = {};

var hasRequiredConstants;

function requireConstants () {
	if (hasRequiredConstants) return constants;
	hasRequiredConstants = 1;
	Object.defineProperty(constants, "__esModule", { value: true });
	constants.ACTIONS_WITH_WRITE_PAYLOAD = constants.DELEGATE_AUX_RELATION_PREFIX = constants.PRISMA_MINIMUM_VERSION = constants.PRISMA_PROXY_ENHANCER = constants.PrismaErrorCode = constants.CrudFailureReason = constants.DEFAULT_PASSWORD_SALT_LENGTH = constants.DEFAULT_RUNTIME_LOAD_PATH = void 0;
	/**
	 * Default path for loading CLI-generated code
	 */
	constants.DEFAULT_RUNTIME_LOAD_PATH = '.zenstack';
	/**
	 * Default length of password hash salt (used by bcryptjs to hash password)
	 */
	constants.DEFAULT_PASSWORD_SALT_LENGTH = 12;
	/**
	 * Reasons for a CRUD operation to fail
	 */
	var CrudFailureReason;
	(function (CrudFailureReason) {
	    /**
	     * CRUD failed because of access policy violation.
	     */
	    CrudFailureReason["ACCESS_POLICY_VIOLATION"] = "ACCESS_POLICY_VIOLATION";
	    /**
	     * CRUD succeeded but the result was not readable.
	     */
	    CrudFailureReason["RESULT_NOT_READABLE"] = "RESULT_NOT_READABLE";
	    /**
	     * CRUD failed because of a data validation rule violation.
	     */
	    CrudFailureReason["DATA_VALIDATION_VIOLATION"] = "DATA_VALIDATION_VIOLATION";
	})(CrudFailureReason || (constants.CrudFailureReason = CrudFailureReason = {}));
	/**
	 * Prisma error codes used
	 */
	var PrismaErrorCode;
	(function (PrismaErrorCode) {
	    /**
	     * Unique constraint failed
	     */
	    PrismaErrorCode["UNIQUE_CONSTRAINT_FAILED"] = "P2002";
	    /**
	     * A constraint failed on the database
	     */
	    PrismaErrorCode["CONSTRAINT_FAILED"] = "P2004";
	    /**
	     * The required connected records were not found
	     */
	    PrismaErrorCode["REQUIRED_CONNECTED_RECORD_NOT_FOUND"] = "P2018";
	    /**
	     * An operation failed because it depends on one or more records that were required but not found
	     */
	    PrismaErrorCode["DEPEND_ON_RECORD_NOT_FOUND"] = "P2025";
	})(PrismaErrorCode || (constants.PrismaErrorCode = PrismaErrorCode = {}));
	/**
	 * Field name for getting current enhancer
	 */
	constants.PRISMA_PROXY_ENHANCER = '$__zenstack_enhancer';
	/**
	 * Minimum Prisma version supported
	 */
	constants.PRISMA_MINIMUM_VERSION = '5.0.0';
	/**
	 * Prefix for auxiliary relation field generated for delegated models
	 */
	constants.DELEGATE_AUX_RELATION_PREFIX = 'delegate_aux';
	/**
	 * Prisma actions that can have a write payload
	 */
	constants.ACTIONS_WITH_WRITE_PAYLOAD = [
	    'create',
	    'createMany',
	    'createManyAndReturn',
	    'update',
	    'updateMany',
	    'updateManyAndReturn',
	    'upsert',
	];
	
	return constants;
}

const rnds8Pool = new Uint8Array(256); // # of random values to pre-allocate

let poolPtr = rnds8Pool.length;
function rng() {
  if (poolPtr > rnds8Pool.length - 16) {
    crypto$1.randomFillSync(rnds8Pool);
    poolPtr = 0;
  }

  return rnds8Pool.slice(poolPtr, poolPtr += 16);
}

/**
 * Convert array of 16 byte values to UUID string format of the form:
 * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
 */

const byteToHex = [];

for (let i = 0; i < 256; ++i) {
  byteToHex.push((i + 0x100).toString(16).slice(1));
}

function unsafeStringify(arr, offset = 0) {
  // Note: Be careful editing this code!  It's been tuned for performance
  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434
  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];
}

const native = {
  randomUUID: crypto$1.randomUUID
};

function v4(options, buf, offset) {
  if (native.randomUUID && true && !options) {
    return native.randomUUID();
  }

  options = options || {};
  const rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`

  rnds[6] = rnds[6] & 0x0f | 0x40;
  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided

  return unsafeStringify(rnds);
}

var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/local-helpers/is-plain-object.ts
function isObject(o) {
  return Object.prototype.toString.call(o) === "[object Object]";
}
function isPlainObject(o) {
  if (isObject(o) === false) return false;
  const ctor = o.constructor;
  if (ctor === void 0) return true;
  const prot = ctor.prototype;
  if (isObject(prot) === false) return false;
  if (Object.prototype.hasOwnProperty.call(prot, "isPrototypeOf") === false) {
    return false;
  }
  return true;
}

// src/local-helpers/lower-case-first.ts
function lowerCaseFirst(input) {
  return input.charAt(0).toLowerCase() + input.slice(1);
}

// src/local-helpers/tiny-invariant.ts
process.env.NODE_ENV === "production";

// src/cross/clone.ts
function clone(value) {
  if (Array.isArray(value)) {
    return value.map((v) => clone(v));
  }
  if (typeof value === "object") {
    if (!value || !isPlainObject(value)) {
      return value;
    }
    const result = {};
    for (const key of Object.keys(value)) {
      result[key] = clone(value[key]);
    }
    return result;
  }
  return value;
}

// src/cross/model-data-visitor.ts
var ModelDataVisitor = class {
  constructor(modelMeta) {
    this.modelMeta = modelMeta;
  }
  /**
   * Visits the given model data.
   */
  visit(model, data, callback) {
    if (!data || typeof data !== "object") {
      return;
    }
    const scalarData = {};
    const subTasks = [];
    for (const [k, v] of Object.entries(data)) {
      const field = resolveField(this.modelMeta, model, k);
      if (field && field.isDataModel) {
        if (field.isArray && Array.isArray(v)) {
          subTasks.push(...v.map((item) => ({ model: field.type, data: item })));
        } else {
          subTasks.push({ model: field.type, data: v });
        }
      } else {
        scalarData[k] = v;
      }
    }
    callback(model, data, scalarData);
    subTasks.forEach(({ model: model2, data: data2 }) => this.visit(model2, data2, callback));
  }
};

// src/cross/model-meta.ts
function resolveField(modelMeta, modelOrTypeDef, field, isTypeDef = false) {
  var _a, _b;
  const container = isTypeDef ? modelMeta.typeDefs : modelMeta.models;
  return (_b = (_a = container == null ? void 0 : container[lowerCaseFirst(modelOrTypeDef)]) == null ? void 0 : _a.fields) == null ? void 0 : _b[field];
}
function requireField(modelMeta, model, field, isTypeDef = false) {
  const f = resolveField(modelMeta, model, field, isTypeDef);
  if (!f) {
    throw new Error(`Field ${model}.${field} cannot be resolved`);
  }
  return f;
}
function getFields(modelMeta, model) {
  var _a;
  return (_a = modelMeta.models[lowerCaseFirst(model)]) == null ? void 0 : _a.fields;
}
function getUniqueConstraints(modelMeta, model) {
  var _a;
  return (_a = modelMeta.models[lowerCaseFirst(model)]) == null ? void 0 : _a.uniqueConstraints;
}
function applyMutation(queryModel, queryOp, queryData, mutationModel, mutationOp, mutationArgs, modelMeta, logging) {
  return __async(this, null, function* () {
    if (!queryData || typeof queryData !== "object" && !Array.isArray(queryData)) {
      return void 0;
    }
    if (!queryOp.startsWith("find")) {
      return void 0;
    }
    return yield doApplyMutation(queryModel, queryData, mutationModel, mutationOp, mutationArgs, modelMeta, logging);
  });
}
function doApplyMutation(queryModel, queryData, mutationModel, mutationOp, mutationArgs, modelMeta, logging) {
  return __async(this, null, function* () {
    let resultData = queryData;
    let updated = false;
    const visitor = new NestedWriteVisitor(modelMeta, {
      create: (model, args) => {
        if (model === queryModel && Array.isArray(resultData)) {
          const r = createMutate(queryModel, resultData, args, modelMeta, logging);
          if (r) {
            resultData = r;
            updated = true;
          }
        }
      },
      createMany: (model, args) => {
        if (model === queryModel && (args == null ? void 0 : args.data) && Array.isArray(resultData)) {
          for (const oneArg of enumerate(args.data)) {
            const r = createMutate(queryModel, resultData, oneArg, modelMeta, logging);
            if (r) {
              resultData = r;
              updated = true;
            }
          }
        }
      },
      update: (model, args) => {
        if (model === queryModel && !Array.isArray(resultData)) {
          const r = updateMutate(queryModel, resultData, model, args, modelMeta, logging);
          if (r) {
            resultData = r;
            updated = true;
          }
        }
      },
      upsert: (model, args) => {
        if (model === queryModel && (args == null ? void 0 : args.where) && (args == null ? void 0 : args.create) && (args == null ? void 0 : args.update)) {
          const r = upsertMutate(queryModel, resultData, model, args, modelMeta, logging);
          if (r) {
            resultData = r;
            updated = true;
          }
        }
      },
      delete: (model, args) => {
        if (model === queryModel) {
          const r = deleteMutate(queryModel, resultData, model, args, modelMeta, logging);
          if (r) {
            resultData = r;
            updated = true;
          }
        }
      }
    });
    yield visitor.visit(mutationModel, mutationOp, mutationArgs);
    const modelFields = getFields(modelMeta, queryModel);
    if (Array.isArray(resultData)) {
      let arrayCloned = false;
      for (let i = 0; i < resultData.length; i++) {
        const item = resultData[i];
        if (!item || typeof item !== "object" || item.$optimistic) {
          continue;
        }
        const r = yield doApplyMutation(
          queryModel,
          item,
          mutationModel,
          mutationOp,
          mutationArgs,
          modelMeta,
          logging
        );
        if (r && typeof r === "object") {
          if (!arrayCloned) {
            resultData = [...resultData];
            arrayCloned = true;
          }
          resultData[i] = r;
          updated = true;
        }
      }
    } else if (resultData !== null && typeof resultData === "object") {
      const currentData = __spreadValues({}, resultData);
      for (const [key, value] of Object.entries(currentData)) {
        const fieldInfo = modelFields[key];
        if (!(fieldInfo == null ? void 0 : fieldInfo.isDataModel)) {
          continue;
        }
        const r = yield doApplyMutation(
          fieldInfo.type,
          value,
          mutationModel,
          mutationOp,
          mutationArgs,
          modelMeta,
          logging
        );
        if (r && typeof r === "object") {
          resultData = __spreadProps(__spreadValues({}, resultData), { [key]: r });
          updated = true;
        }
      }
    }
    return updated ? resultData : void 0;
  });
}
function createMutate(queryModel, currentData, newData, modelMeta, logging) {
  if (!newData) {
    return void 0;
  }
  const modelFields = getFields(modelMeta, queryModel);
  if (!modelFields) {
    return void 0;
  }
  const insert = {};
  const newDataFields = Object.keys(newData);
  Object.entries(modelFields).forEach(([name, field]) => {
    var _a, _b, _c, _d;
    if (field.isDataModel && newData[name]) {
      assignForeignKeyFields(field, insert, newData[name]);
      return;
    }
    if (newDataFields.includes(name)) {
      insert[name] = clone(newData[name]);
    } else {
      const defaultAttr = (_a = field.attributes) == null ? void 0 : _a.find((attr) => attr.name === "@default");
      if (field.type === "DateTime") {
        if (defaultAttr || ((_b = field.attributes) == null ? void 0 : _b.some((attr) => attr.name === "@updatedAt"))) {
          insert[name] = /* @__PURE__ */ new Date();
        }
      } else if (((_d = (_c = defaultAttr == null ? void 0 : defaultAttr.args) == null ? void 0 : _c[0]) == null ? void 0 : _d.value) !== void 0) {
        insert[name] = defaultAttr.args[0].value;
      }
    }
  });
  const idFields = getIdFields(modelMeta, queryModel, false);
  idFields.forEach((f) => {
    if (insert[f.name] === void 0) {
      if (f.type === "Int" || f.type === "BigInt") {
        const currMax = Array.isArray(currentData) ? Math.max(
          ...[...currentData].map((item) => {
            const idv = parseInt(item[f.name]);
            return isNaN(idv) ? 0 : idv;
          })
        ) : 0;
        insert[f.name] = currMax + 1;
      } else {
        insert[f.name] = v4();
      }
    }
  });
  insert.$optimistic = true;
  if (logging) {
    console.log(`Optimistic create for ${queryModel}:`, insert);
  }
  return [insert, ...Array.isArray(currentData) ? currentData : []];
}
function updateMutate(queryModel, currentData, mutateModel, mutateArgs, modelMeta, logging) {
  if (!currentData || typeof currentData !== "object") {
    return void 0;
  }
  if (!(mutateArgs == null ? void 0 : mutateArgs.where) || typeof mutateArgs.where !== "object") {
    return void 0;
  }
  if (!(mutateArgs == null ? void 0 : mutateArgs.data) || typeof mutateArgs.data !== "object") {
    return void 0;
  }
  if (!idFieldsMatch(mutateModel, currentData, mutateArgs.where, modelMeta)) {
    return void 0;
  }
  const modelFields = getFields(modelMeta, queryModel);
  if (!modelFields) {
    return void 0;
  }
  let updated = false;
  let resultData = currentData;
  for (const [key, value] of Object.entries(mutateArgs.data)) {
    const fieldInfo = modelFields[key];
    if (!fieldInfo) {
      continue;
    }
    if (fieldInfo.isDataModel && !(value == null ? void 0 : value.connect)) {
      continue;
    }
    if (!updated) {
      resultData = __spreadValues({}, currentData);
    }
    if (fieldInfo.isDataModel) {
      assignForeignKeyFields(fieldInfo, resultData, value);
    } else {
      resultData[key] = clone(value);
    }
    resultData.$optimistic = true;
    updated = true;
    if (logging) {
      console.log(`Optimistic update for ${queryModel}:`, resultData);
    }
  }
  return updated ? resultData : void 0;
}
function upsertMutate(queryModel, currentData, model, args, modelMeta, logging) {
  let updated = false;
  let resultData = currentData;
  if (Array.isArray(resultData)) {
    const foundIndex = resultData.findIndex((x) => idFieldsMatch(model, x, args.where, modelMeta));
    if (foundIndex >= 0) {
      const updateResult = updateMutate(
        queryModel,
        resultData[foundIndex],
        model,
        { where: args.where, data: args.update },
        modelMeta,
        logging
      );
      if (updateResult) {
        resultData = [...resultData.slice(0, foundIndex), updateResult, ...resultData.slice(foundIndex + 1)];
        updated = true;
      }
    } else {
      const createResult = createMutate(queryModel, resultData, args.create, modelMeta, logging);
      if (createResult) {
        resultData = createResult;
        updated = true;
      }
    }
  } else {
    const updateResult = updateMutate(
      queryModel,
      resultData,
      model,
      { where: args.where, data: args.update },
      modelMeta,
      logging
    );
    if (updateResult) {
      resultData = updateResult;
      updated = true;
    }
  }
  return updated ? resultData : void 0;
}
function deleteMutate(queryModel, currentData, mutateModel, mutateArgs, modelMeta, logging) {
  if (!currentData || !mutateArgs) {
    return void 0;
  }
  if (queryModel !== mutateModel) {
    return void 0;
  }
  let updated = false;
  let result = currentData;
  if (Array.isArray(currentData)) {
    for (const item of currentData) {
      if (idFieldsMatch(mutateModel, item, mutateArgs, modelMeta)) {
        result = result.filter((x) => x !== item);
        updated = true;
        if (logging) {
          console.log(`Optimistic delete for ${queryModel}:`, item);
        }
      }
    }
  } else {
    if (idFieldsMatch(mutateModel, currentData, mutateArgs, modelMeta)) {
      result = null;
      updated = true;
      if (logging) {
        console.log(`Optimistic delete for ${queryModel}:`, currentData);
      }
    }
  }
  return updated ? result : void 0;
}
function idFieldsMatch(model, x, y, modelMeta) {
  if (!x || !y || typeof x !== "object" || typeof y !== "object") {
    return false;
  }
  const idFields = getIdFields(modelMeta, model, false);
  if (idFields.length === 0) {
    return false;
  }
  return idFields.every((f) => x[f.name] === y[f.name]);
}
function assignForeignKeyFields(field, resultData, mutationData) {
  if (!(mutationData == null ? void 0 : mutationData.connect)) {
    return;
  }
  if (!field.foreignKeyMapping) {
    return;
  }
  for (const [idField, fkField] of Object.entries(field.foreignKeyMapping)) {
    if (idField in mutationData.connect) {
      resultData[fkField] = mutationData.connect[idField];
    }
  }
}

// src/cross/nested-read-visitor.ts
var NestedReadVisitor = class {
  constructor(modelMeta, callback) {
    this.modelMeta = modelMeta;
    this.callback = callback;
  }
  doVisit(model, field, kind, args) {
    if (this.callback.field) {
      const r = this.callback.field(model, field, kind, args);
      if (r === false) {
        return;
      }
    }
    if (!args || typeof args !== "object") {
      return;
    }
    let selectInclude;
    let nextKind;
    if (args.select) {
      selectInclude = args.select;
      nextKind = "select";
    } else if (args.include) {
      selectInclude = args.include;
      nextKind = "include";
    }
    if (selectInclude && typeof selectInclude === "object") {
      for (const [k, v] of Object.entries(selectInclude)) {
        if (k === "_count" && typeof v === "object" && v) {
          this.doVisit(model, field, kind, v);
        } else {
          const field2 = resolveField(this.modelMeta, model, k);
          if (field2) {
            this.doVisit(field2.type, field2, nextKind, v);
          }
        }
      }
    }
  }
  visit(model, args) {
    this.doVisit(model, void 0, void 0, args);
  }
};

// src/cross/types.ts
var PrismaWriteActions = [
  "create",
  "createMany",
  "createManyAndReturn",
  "connectOrCreate",
  "update",
  "updateMany",
  "updateManyAndReturn",
  "upsert",
  "connect",
  "disconnect",
  "set",
  "delete",
  "deleteMany"
];

// src/cross/utils.ts
function getModelFields(data) {
  return data ? Object.keys(data) : [];
}
function enumerate(x) {
  if (x === null || x === void 0) {
    return [];
  } else if (Array.isArray(x)) {
    return x;
  } else {
    return [x];
  }
}
function zip(x, y) {
  if (Array.isArray(x)) {
    if (!Array.isArray(y)) {
      throw new Error("x and y should be both array or both scalar");
    }
    if (x.length !== y.length) {
      throw new Error("x and y should have the same length");
    }
    return x.map((_, i) => [x[i], y[i]]);
  } else {
    if (Array.isArray(y)) {
      throw new Error("x and y should be both array or both scalar");
    }
    return [[x, y]];
  }
}
function getIdFields(modelMeta, model, throwIfNotFound = false) {
  var _a, _b;
  const uniqueConstraints = (_b = (_a = modelMeta.models[lowerCaseFirst(model)]) == null ? void 0 : _a.uniqueConstraints) != null ? _b : {};
  const entries = Object.values(uniqueConstraints);
  if (entries.length === 0) {
    if (throwIfNotFound) {
      throw new Error(`Model ${model} does not have any id field`);
    }
    return [];
  }
  return entries[0].fields.map((f) => requireField(modelMeta, model, f));
}
function getModelInfo(modelMeta, model, throwIfNotFound = false) {
  const info = modelMeta.models[lowerCaseFirst(model)];
  if (!info && throwIfNotFound) {
    throw new Error(`Unable to load info for ${model}`);
  }
  return info;
}
function getTypeDefInfo(modelMeta, typeDef, throwIfNotFound = false) {
  var _a;
  const info = (_a = modelMeta.typeDefs) == null ? void 0 : _a[lowerCaseFirst(typeDef)];
  if (!info && throwIfNotFound) {
    throw new Error(`Unable to load info for ${typeDef}`);
  }
  return info;
}
function isDelegateModel(modelMeta, model) {
  var _a, _b;
  return !!((_b = (_a = getModelInfo(modelMeta, model)) == null ? void 0 : _a.attributes) == null ? void 0 : _b.some((attr) => attr.name === "@@delegate"));
}

// src/cross/nested-write-visitor.ts
var NestedWriteVisitor = class {
  constructor(modelMeta, callback) {
    this.modelMeta = modelMeta;
    this.callback = callback;
  }
  isPrismaWriteAction(value) {
    return PrismaWriteActions.includes(value);
  }
  /**
   * Start visiting
   *
   * @see NestedWriterVisitorCallback
   */
  visit(model, action, args) {
    return __async(this, null, function* () {
      if (!args) {
        return;
      }
      let topData = args;
      switch (action) {
        case "create":
          topData = topData.data;
          break;
        case "delete":
        case "deleteMany":
          topData = topData.where;
          break;
      }
      yield this.doVisit(model, action, topData, void 0, void 0, []);
    });
  }
  doVisit(model, action, data, parent, field, nestingPath) {
    return __async(this, null, function* () {
      if (!data) {
        return;
      }
      const toplevel = field == void 0;
      const context = { parent, field, nestingPath: [...nestingPath] };
      const pushNewContext = (field2, model2, where, unique = false) => {
        return __spreadProps(__spreadValues({}, context), { nestingPath: [...context.nestingPath, { field: field2, model: model2, where, unique }] });
      };
      switch (action) {
        case "create":
          for (const item of this.enumerateReverse(data)) {
            const newContext = pushNewContext(field, model, {});
            let callbackResult;
            if (this.callback.create) {
              callbackResult = yield this.callback.create(model, item, newContext);
            }
            if (callbackResult !== false) {
              const subPayload = typeof callbackResult === "object" ? callbackResult : item;
              yield this.visitSubPayload(model, action, subPayload, newContext.nestingPath);
            }
          }
          break;
        case "createMany":
        case "createManyAndReturn":
          if (data) {
            const newContext = pushNewContext(field, model, {});
            let callbackResult;
            if (this.callback.createMany) {
              callbackResult = yield this.callback.createMany(model, data, newContext);
            }
            if (callbackResult !== false) {
              const subPayload = typeof callbackResult === "object" ? callbackResult : data.data;
              yield this.visitSubPayload(model, action, subPayload, newContext.nestingPath);
            }
          }
          break;
        case "connectOrCreate":
          for (const item of this.enumerateReverse(data)) {
            const newContext = pushNewContext(field, model, item.where);
            let callbackResult;
            if (this.callback.connectOrCreate) {
              callbackResult = yield this.callback.connectOrCreate(model, item, newContext);
            }
            if (callbackResult !== false) {
              const subPayload = typeof callbackResult === "object" ? callbackResult : item.create;
              yield this.visitSubPayload(model, action, subPayload, newContext.nestingPath);
            }
          }
          break;
        case "connect":
          if (this.callback.connect) {
            for (const item of this.enumerateReverse(data)) {
              const newContext = pushNewContext(field, model, item, true);
              yield this.callback.connect(model, item, newContext);
            }
          }
          break;
        case "disconnect":
          if (this.callback.disconnect) {
            for (const item of this.enumerateReverse(data)) {
              const newContext = pushNewContext(field, model, item, typeof item === "object");
              yield this.callback.disconnect(model, item, newContext);
            }
          }
          break;
        case "set":
          if (this.callback.set) {
            for (const item of this.enumerateReverse(data)) {
              const newContext = pushNewContext(field, model, item, true);
              yield this.callback.set(model, item, newContext);
            }
          }
          break;
        case "update":
          for (const item of this.enumerateReverse(data)) {
            const newContext = pushNewContext(field, model, item.where);
            let callbackResult;
            if (this.callback.update) {
              callbackResult = yield this.callback.update(model, item, newContext);
            }
            if (callbackResult !== false) {
              const subPayload = typeof callbackResult === "object" ? callbackResult : typeof item.data === "object" ? item.data : item;
              yield this.visitSubPayload(model, action, subPayload, newContext.nestingPath);
            }
          }
          break;
        case "updateMany":
        case "updateManyAndReturn":
          for (const item of this.enumerateReverse(data)) {
            const newContext = pushNewContext(field, model, item.where);
            let callbackResult;
            if (this.callback.updateMany) {
              callbackResult = yield this.callback.updateMany(model, item, newContext);
            }
            if (callbackResult !== false) {
              const subPayload = typeof callbackResult === "object" ? callbackResult : item;
              yield this.visitSubPayload(model, action, subPayload, newContext.nestingPath);
            }
          }
          break;
        case "upsert": {
          for (const item of this.enumerateReverse(data)) {
            const newContext = pushNewContext(field, model, item.where);
            let callbackResult;
            if (this.callback.upsert) {
              callbackResult = yield this.callback.upsert(model, item, newContext);
            }
            if (callbackResult !== false) {
              if (typeof callbackResult === "object") {
                yield this.visitSubPayload(model, action, callbackResult, newContext.nestingPath);
              } else {
                yield this.visitSubPayload(model, action, item.create, newContext.nestingPath);
                yield this.visitSubPayload(model, action, item.update, newContext.nestingPath);
              }
            }
          }
          break;
        }
        case "delete": {
          if (this.callback.delete) {
            for (const item of this.enumerateReverse(data)) {
              const newContext = pushNewContext(field, model, toplevel ? item.where : item);
              yield this.callback.delete(model, item, newContext);
            }
          }
          break;
        }
        case "deleteMany":
          if (this.callback.deleteMany) {
            for (const item of this.enumerateReverse(data)) {
              const newContext = pushNewContext(field, model, toplevel ? item.where : item);
              yield this.callback.deleteMany(model, item, newContext);
            }
          }
          break;
        default: {
          throw new Error(`unhandled action type ${action}`);
        }
      }
    });
  }
  visitSubPayload(model, action, payload, nestingPath) {
    return __async(this, null, function* () {
      for (const item of enumerate(payload)) {
        for (const field of getModelFields(item)) {
          const fieldInfo = resolveField(this.modelMeta, model, field);
          if (!fieldInfo) {
            continue;
          }
          if (fieldInfo.isDataModel) {
            if (item[field]) {
              for (const [subAction, subData] of Object.entries(item[field])) {
                if (this.isPrismaWriteAction(subAction) && subData) {
                  yield this.doVisit(fieldInfo.type, subAction, subData, item[field], fieldInfo, [
                    ...nestingPath
                  ]);
                }
              }
            }
          } else {
            if (this.callback.field) {
              yield this.callback.field(fieldInfo, action, item[field], {
                parent: item,
                nestingPath,
                field: fieldInfo
              });
            }
          }
        }
      }
    });
  }
  // enumerate a (possible) array in reverse order, so that the enumeration
  // callback can safely delete the current item
  *enumerateReverse(data) {
    if (Array.isArray(data)) {
      for (let i = data.length - 1; i >= 0; i--) {
        yield data[i];
      }
    } else {
      yield data;
    }
  }
};

// src/cross/query-analyzer.ts
function getReadModels(model, modelMeta, args) {
  const result = /* @__PURE__ */ new Set();
  result.add(model);
  const visitor = new NestedReadVisitor(modelMeta, {
    field: (model2) => {
      result.add(model2);
      return true;
    }
  });
  visitor.visit(model, args);
  return [...result];
}
function getMutatedModels(model, operation, mutationArgs, modelMeta) {
  return __async(this, null, function* () {
    const result = /* @__PURE__ */ new Set();
    result.add(model);
    if (mutationArgs) {
      const addModel = (model2) => void result.add(model2);
      const addCascades = (model2) => {
        const cascades = /* @__PURE__ */ new Set();
        const visited = /* @__PURE__ */ new Set();
        collectDeleteCascades(model2, modelMeta, cascades, visited);
        cascades.forEach((m) => addModel(m));
      };
      const visitor = new NestedWriteVisitor(modelMeta, {
        create: addModel,
        createMany: addModel,
        connectOrCreate: addModel,
        connect: addModel,
        disconnect: addModel,
        set: addModel,
        update: addModel,
        updateMany: addModel,
        upsert: addModel,
        delete: (model2) => {
          addModel(model2);
          addCascades(model2);
        },
        deleteMany: (model2) => {
          addModel(model2);
          addCascades(model2);
        }
      });
      yield visitor.visit(model, operation, mutationArgs);
    }
    result.forEach((m) => {
      getBaseRecursively(m, modelMeta, result);
    });
    return [...result];
  });
}
function collectDeleteCascades(model, modelMeta, result, visited) {
  var _a;
  if (visited.has(model)) {
    return;
  }
  visited.add(model);
  const cascades = (_a = modelMeta.deleteCascade) == null ? void 0 : _a[lowerCaseFirst(model)];
  if (!cascades) {
    return;
  }
  cascades.forEach((m) => {
    result.add(m);
    collectDeleteCascades(m, modelMeta, result, visited);
  });
}
function getBaseRecursively(model, modelMeta, result) {
  var _a;
  const bases = (_a = getModelInfo(modelMeta, model)) == null ? void 0 : _a.baseTypes;
  if (bases) {
    bases.forEach((base) => {
      result.add(base);
      getBaseRecursively(base, modelMeta, result);
    });
  }
}

const cross = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
	__proto__: null,
	ModelDataVisitor,
	NestedReadVisitor,
	NestedWriteVisitor,
	PrismaWriteActions,
	applyMutation,
	clone,
	enumerate,
	getFields,
	getIdFields,
	getModelFields,
	getModelInfo,
	getMutatedModels,
	getReadModels,
	getTypeDefInfo,
	getUniqueConstraints,
	isDelegateModel,
	requireField,
	resolveField,
	zip
}, Symbol.toStringTag, { value: 'Module' }));

const require$$1 = /*@__PURE__*/getAugmentedNamespace(cross);

var enhance = {};

var hasRequiredEnhance;

function requireEnhance () {
	if (hasRequiredEnhance) return enhance;
	hasRequiredEnhance = 1;
	Object.defineProperty(enhance, '__esModule', { value: true });

	try {
	    enhance.enhance = require('.zenstack/enhance').enhance;
	} catch {
	    enhance.enhance = function () {
	        throw new Error('Generated "enhance" function not found. Please run `zenstack generate` first.');
	    };
	}
	return enhance;
}

var error = {};

var hasRequiredError;

function requireError () {
	if (hasRequiredError) return error;
	hasRequiredError = 1;
	Object.defineProperty(error, "__esModule", { value: true });
	error.isPrismaClientKnownRequestError = isPrismaClientKnownRequestError;
	error.isPrismaClientUnknownRequestError = isPrismaClientUnknownRequestError;
	error.isPrismaClientValidationError = isPrismaClientValidationError;
	/* eslint-disable @typescript-eslint/no-explicit-any */
	function isPrismaClientKnownRequestError(err) {
	    return findConstructorName(err.__proto__, 'PrismaClientKnownRequestError');
	}
	function isPrismaClientUnknownRequestError(err) {
	    return findConstructorName(err.__proto__, 'PrismaClientUnknownRequestError');
	}
	function isPrismaClientValidationError(err) {
	    return findConstructorName(err.__proto__, 'PrismaClientValidationError');
	}
	function findConstructorName(proto, name) {
	    if (!proto) {
	        return false;
	    }
	    return proto.constructor.name === name || findConstructorName(proto.__proto__, name);
	}
	
	return error;
}

var types$1 = {};

var hasRequiredTypes;

function requireTypes () {
	if (hasRequiredTypes) return types$1;
	hasRequiredTypes = 1;
	/* eslint-disable @typescript-eslint/no-explicit-any */
	Object.defineProperty(types$1, "__esModule", { value: true });
	
	return types$1;
}

var validation = {};

var v3;
var hasRequiredV3;

function requireV3 () {
	if (hasRequiredV3) return v3;
	hasRequiredV3 = 1;
	var __create = Object.create;
	var __defProp = Object.defineProperty;
	var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
	var __getOwnPropNames = Object.getOwnPropertyNames;
	var __getProtoOf = Object.getPrototypeOf;
	var __hasOwnProp = Object.prototype.hasOwnProperty;
	var __export = (target, all) => {
	  for (var name in all)
	    __defProp(target, name, { get: all[name], enumerable: true });
	};
	var __copyProps = (to, from, except, desc) => {
	  if (from && typeof from === "object" || typeof from === "function") {
	    for (let key of __getOwnPropNames(from))
	      if (!__hasOwnProp.call(to, key) && key !== except)
	        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
	  }
	  return to;
	};
	var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
	  // If the importer is in node compatibility mode or this is not an ESM
	  // file that has been converted to a CommonJS file using a Babel-
	  // compatible transform (i.e. "__esModule" has not been set), then set
	  // "default" to the CommonJS "module.exports" for node compatibility.
	  !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
	  mod
	));
	var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

	// lib/v3/index.ts
	var index_exports = {};
	__export(index_exports, {
	  ValidationError: () => ValidationError,
	  createMessageBuilder: () => createMessageBuilder,
	  errorMap: () => errorMap,
	  fromError: () => fromError,
	  fromZodError: () => fromZodError,
	  fromZodIssue: () => fromZodIssue,
	  isValidationError: () => isValidationError,
	  isValidationErrorLike: () => isValidationErrorLike,
	  isZodErrorLike: () => isZodErrorLike,
	  toValidationError: () => toValidationError
	});
	v3 = __toCommonJS(index_exports);

	// lib/v3/isZodErrorLike.ts
	function isZodErrorLike(err) {
	  return err instanceof Error && err.name === "ZodError" && "issues" in err && Array.isArray(err.issues);
	}

	// lib/v3/ValidationError.ts
	var ValidationError = class extends Error {
	  name;
	  details;
	  constructor(message, options) {
	    super(message, options);
	    this.name = "ZodValidationError";
	    this.details = getIssuesFromErrorOptions(options);
	  }
	  toString() {
	    return this.message;
	  }
	};
	function getIssuesFromErrorOptions(options) {
	  if (options) {
	    const cause = options.cause;
	    if (isZodErrorLike(cause)) {
	      return cause.issues;
	    }
	  }
	  return [];
	}

	// lib/v3/isValidationError.ts
	function isValidationError(err) {
	  return err instanceof ValidationError;
	}

	// lib/v3/isValidationErrorLike.ts
	function isValidationErrorLike(err) {
	  return err instanceof Error && err.name === "ZodValidationError";
	}

	// lib/v3/fromZodIssue.ts
	var zod2 = __toESM(require$$0$2);

	// lib/v3/MessageBuilder.ts
	var zod = __toESM(require$$0$2);

	// lib/utils/NonEmptyArray.ts
	function isNonEmptyArray(value) {
	  return value.length !== 0;
	}

	// lib/utils/stringify.ts
	function stringifySymbol(symbol) {
	  return symbol.description ?? "";
	}

	// lib/utils/joinPath.ts
	var identifierRegex = /[$_\p{ID_Start}][$\u200c\u200d\p{ID_Continue}]*/u;
	function joinPath(path) {
	  if (path.length === 1) {
	    let propertyKey = path[0];
	    if (typeof propertyKey === "symbol") {
	      propertyKey = stringifySymbol(propertyKey);
	    }
	    return propertyKey.toString() || '""';
	  }
	  return path.reduce((acc, propertyKey) => {
	    if (typeof propertyKey === "number") {
	      return acc + "[" + propertyKey.toString() + "]";
	    }
	    if (typeof propertyKey === "symbol") {
	      propertyKey = stringifySymbol(propertyKey);
	    }
	    if (propertyKey.includes('"')) {
	      return acc + '["' + escapeQuotes(propertyKey) + '"]';
	    }
	    if (!identifierRegex.test(propertyKey)) {
	      return acc + '["' + propertyKey + '"]';
	    }
	    const separator = acc.length === 0 ? "" : ".";
	    return acc + separator + propertyKey;
	  }, "");
	}
	function escapeQuotes(str) {
	  return str.replace(/"/g, '\\"');
	}

	// lib/v3/config.ts
	var ISSUE_SEPARATOR = "; ";
	var MAX_ISSUES_IN_MESSAGE = 99;
	var PREFIX = "Validation error";
	var PREFIX_SEPARATOR = ": ";
	var UNION_SEPARATOR = ", or ";

	// lib/v3/MessageBuilder.ts
	function createMessageBuilder(props = {}) {
	  const {
	    issueSeparator = ISSUE_SEPARATOR,
	    unionSeparator = UNION_SEPARATOR,
	    prefixSeparator = PREFIX_SEPARATOR,
	    prefix = PREFIX,
	    includePath = true,
	    maxIssuesInMessage = MAX_ISSUES_IN_MESSAGE
	  } = props;
	  return (issues) => {
	    const message = issues.slice(0, maxIssuesInMessage).map(
	      (issue) => getMessageFromZodIssue({
	        issue,
	        issueSeparator,
	        unionSeparator,
	        includePath
	      })
	    ).join(issueSeparator);
	    return prefixMessage(message, prefix, prefixSeparator);
	  };
	}
	function getMessageFromZodIssue(props) {
	  const { issue, issueSeparator, unionSeparator, includePath } = props;
	  if (issue.code === zod.ZodIssueCode.invalid_union) {
	    return issue.unionErrors.reduce((acc, zodError) => {
	      const newIssues = zodError.issues.map(
	        (issue2) => getMessageFromZodIssue({
	          issue: issue2,
	          issueSeparator,
	          unionSeparator,
	          includePath
	        })
	      ).join(issueSeparator);
	      if (!acc.includes(newIssues)) {
	        acc.push(newIssues);
	      }
	      return acc;
	    }, []).join(unionSeparator);
	  }
	  if (issue.code === zod.ZodIssueCode.invalid_arguments) {
	    return [
	      issue.message,
	      ...issue.argumentsError.issues.map(
	        (issue2) => getMessageFromZodIssue({
	          issue: issue2,
	          issueSeparator,
	          unionSeparator,
	          includePath
	        })
	      )
	    ].join(issueSeparator);
	  }
	  if (issue.code === zod.ZodIssueCode.invalid_return_type) {
	    return [
	      issue.message,
	      ...issue.returnTypeError.issues.map(
	        (issue2) => getMessageFromZodIssue({
	          issue: issue2,
	          issueSeparator,
	          unionSeparator,
	          includePath
	        })
	      )
	    ].join(issueSeparator);
	  }
	  if (includePath && isNonEmptyArray(issue.path)) {
	    if (issue.path.length === 1) {
	      const identifier = issue.path[0];
	      if (typeof identifier === "number") {
	        return `${issue.message} at index ${identifier}`;
	      }
	    }
	    return `${issue.message} at "${joinPath(issue.path)}"`;
	  }
	  return issue.message;
	}
	function prefixMessage(message, prefix, prefixSeparator) {
	  if (prefix !== null) {
	    if (message.length > 0) {
	      return [prefix, message].join(prefixSeparator);
	    }
	    return prefix;
	  }
	  if (message.length > 0) {
	    return message;
	  }
	  return PREFIX;
	}

	// lib/v3/fromZodIssue.ts
	function fromZodIssue(issue, options = {}) {
	  const messageBuilder = createMessageBuilderFromOptions(options);
	  const message = messageBuilder([issue]);
	  return new ValidationError(message, { cause: new zod2.ZodError([issue]) });
	}
	function createMessageBuilderFromOptions(options) {
	  if ("messageBuilder" in options) {
	    return options.messageBuilder;
	  }
	  return createMessageBuilder(options);
	}

	// lib/v3/errorMap.ts
	var errorMap = (issue, ctx) => {
	  const error = fromZodIssue({
	    ...issue,
	    // fallback to the default error message
	    // when issue does not have a message
	    message: issue.message ?? ctx.defaultError
	  });
	  return {
	    message: error.message
	  };
	};

	// lib/v3/fromZodError.ts
	function fromZodError(zodError, options = {}) {
	  if (!isZodErrorLike(zodError)) {
	    throw new TypeError(
	      `Invalid zodError param; expected instance of ZodError. Did you mean to use the "${fromError.name}" method instead?`
	    );
	  }
	  return fromZodErrorWithoutRuntimeCheck(zodError, options);
	}
	function fromZodErrorWithoutRuntimeCheck(zodError, options = {}) {
	  const zodIssues = zodError.errors;
	  let message;
	  if (isNonEmptyArray(zodIssues)) {
	    const messageBuilder = createMessageBuilderFromOptions2(options);
	    message = messageBuilder(zodIssues);
	  } else {
	    message = zodError.message;
	  }
	  return new ValidationError(message, { cause: zodError });
	}
	function createMessageBuilderFromOptions2(options) {
	  if ("messageBuilder" in options) {
	    return options.messageBuilder;
	  }
	  return createMessageBuilder(options);
	}

	// lib/v3/toValidationError.ts
	var toValidationError = (options = {}) => (err) => {
	  if (isZodErrorLike(err)) {
	    return fromZodErrorWithoutRuntimeCheck(err, options);
	  }
	  if (err instanceof Error) {
	    return new ValidationError(err.message, { cause: err });
	  }
	  return new ValidationError("Unknown error");
	};

	// lib/v3/fromError.ts
	function fromError(err, options = {}) {
	  return toValidationError(options)(err);
	}
	
	return v3;
}

var hasRequiredValidation;

function requireValidation () {
	if (hasRequiredValidation) return validation;
	hasRequiredValidation = 1;
	Object.defineProperty(validation, "__esModule", { value: true });
	validation.ValidationError = void 0;
	validation.validate = validate;
	validation.hasAllFields = hasAllFields;
	validation.allFieldsEqual = allFieldsEqual;
	const v3_1 = /*@__PURE__*/ requireV3();
	/**
	 * Error indicating violations of field-level constraints
	 */
	class ValidationError {
	    constructor(message) {
	        this.message = message;
	    }
	}
	validation.ValidationError = ValidationError;
	/**
	 * Validate the given data with the given zod schema (for field-level constraints)
	 */
	function validate(validator, data) {
	    try {
	        validator.parse(data);
	    }
	    catch (err) {
	        throw new ValidationError((0, v3_1.fromZodError)(err).message);
	    }
	}
	/**
	 * Check if the given object has all the given fields, not null or undefined
	 * @param obj
	 * @param fields
	 * @returns
	 */
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	function hasAllFields(obj, fields) {
	    if (typeof obj !== 'object' || !obj) {
	        return false;
	    }
	    return fields.every((f) => obj[f] !== undefined && obj[f] !== null);
	}
	/**
	 * Check if the given objects have equal values for the given fields. Returns
	 * false if either object is nullish or is not an object.
	 */
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	function allFieldsEqual(obj1, obj2, fields) {
	    if (!obj1 || !obj2 || typeof obj1 !== 'object' || typeof obj2 !== 'object') {
	        return false;
	    }
	    return fields.every((f) => obj1[f] === obj2[f]);
	}
	
	return validation;
}

var version$1 = {};

const name = "@zenstackhq/runtime";
const displayName = "ZenStack Runtime Library";
const version = "2.18.0";
const description = "Runtime of ZenStack for both client-side and server-side environments.";
const repository = {"type":"git","url":"https://github.com/zenstackhq/zenstack"};
const main = "index.js";
const types = "index.d.ts";
const exports = {".":{"types":"./index.d.ts","default":"./index.js"},"./local-helpers":{"types":"./local-helpers/index.d.ts","default":"./local-helpers/index.js"},"./edge":{"types":"./edge.d.ts","default":"./edge.js"},"./enhancements/node":{"types":"./enhancements/node/index.d.ts","default":"./enhancements/node/index.js"},"./enhancements/edge":{"types":"./enhancements/edge/index.d.ts","default":"./enhancements/edge/index.js"},"./validation":{"types":"./validation.d.ts","default":"./validation.js"},"./constraint-solver":{"types":"./constraint-solver.d.ts","default":"./constraint-solver.js"},"./zod":{"types":"./zod/index.d.ts","default":"./zod/index.js"},"./zod/input":{"types":"./zod/input.d.ts","default":"./zod/input.js"},"./zod/models":{"types":"./zod/models.d.ts","default":"./zod/models.js"},"./zod/objects":{"types":"./zod/objects.d.ts","default":"./zod/objects.js"},"./browser":{"types":"./browser/index.d.ts","import":"./browser/index.mjs","require":"./browser/index.js","default":"./browser/index.js"},"./cross":{"types":"./cross/index.d.ts","import":"./cross/index.mjs","require":"./cross/index.js","default":"./cross/index.js"},"./model-meta":{"types":"./model-meta.d.ts","default":"./model-meta.js"},"./models":{"types":"./models.d.ts","default":"./models.js"},"./zod-utils":{"types":"./zod-utils.d.ts","default":"./zod-utils.js"},"./encryption":{"types":"./encryption/index.d.ts","default":"./encryption/index.js"},"./package.json":{"default":"./package.json"}};
const publishConfig = {"directory":"dist","linkDirectory":true};
const dependencies = {"bcryptjs":"^2.4.3","buffer":"^6.0.3","decimal.js-light":"^2.5.1","deepmerge":"^4.3.1","logic-solver":"^2.0.1","pluralize":"^8.0.0","safe-json-stringify":"^1.2.0","semver":"^7.5.2","superjson":"^1.13.0","ts-pattern":"^4.3.0","tslib":"^2.4.1","uuid":"^9.0.0","zod-validation-error":"^4.0.0"};
const peerDependencies = {"@prisma/client":"5.0.0 - 6.13.x","zod":"^3.25.0"};
const author = {"name":"ZenStack Team"};
const homepage = "https://zenstack.dev";
const license = "MIT";
const devDependencies = {"@types/bcryptjs":"^2.4.2","@types/pluralize":"^0.0.29","@types/safe-json-stringify":"^1.1.5","@types/semver":"^7.3.13","@types/uuid":"^8.3.4","decimal.js-light":"^2.5.1","superjson":"^1.13.0","uuid":"^9.0.0"};
const scripts = {"clean":"rimraf dist","build":"pnpm lint --max-warnings=0 && pnpm clean && tsc && tsup-node --config ./tsup-browser.config.ts && tsup-node --config ./tsup-cross.config.ts && copyfiles ./package.json ./README.md ../../LICENSE dist && copyfiles -u1 \"res/**/*\" dist && pnpm pack dist --pack-destination ../../../.build","watch":"concurrently \"tsc --watch\" \"tsup-node --config ./tsup-browser.config.ts --watch\" \"tsup-node --config ./tsup-cross.config.ts --watch\"","lint":"eslint src --ext ts"};
const require$$0 = {
  name,
  displayName,
  version,
  description,
  repository,
  main,
  types,
  exports,
  publishConfig,
  dependencies,
  peerDependencies,
  author,
  homepage,
  license,
  devDependencies,
  scripts,
};

var hasRequiredVersion;

function requireVersion () {
	if (hasRequiredVersion) return version$1;
	hasRequiredVersion = 1;
	var __createBinding = (version$1 && version$1.__createBinding) || (Object.create ? (function(o, m, k, k2) {
	    if (k2 === undefined) k2 = k;
	    var desc = Object.getOwnPropertyDescriptor(m, k);
	    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
	      desc = { enumerable: true, get: function() { return m[k]; } };
	    }
	    Object.defineProperty(o, k2, desc);
	}) : (function(o, m, k, k2) {
	    if (k2 === undefined) k2 = k;
	    o[k2] = m[k];
	}));
	var __setModuleDefault = (version$1 && version$1.__setModuleDefault) || (Object.create ? (function(o, v) {
	    Object.defineProperty(o, "default", { enumerable: true, value: v });
	}) : function(o, v) {
	    o["default"] = v;
	});
	var __importStar = (version$1 && version$1.__importStar) || (function () {
	    var ownKeys = function(o) {
	        ownKeys = Object.getOwnPropertyNames || function (o) {
	            var ar = [];
	            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
	            return ar;
	        };
	        return ownKeys(o);
	    };
	    return function (mod) {
	        if (mod && mod.__esModule) return mod;
	        var result = {};
	        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
	        __setModuleDefault(result, mod);
	        return result;
	    };
	})();
	Object.defineProperty(version$1, "__esModule", { value: true });
	version$1.getVersion = getVersion;
	const pkgJson = __importStar(require$$0);
	/**
	 * Gets this package's version.
	 * @returns
	 */
	function getVersion() {
	    return pkgJson.version;
	}
	
	return version$1;
}

var hasRequiredRuntime;

function requireRuntime () {
	if (hasRequiredRuntime) return runtime;
	hasRequiredRuntime = 1;
	(function (exports) {
		var __createBinding = (runtime && runtime.__createBinding) || (Object.create ? (function(o, m, k, k2) {
		    if (k2 === undefined) k2 = k;
		    var desc = Object.getOwnPropertyDescriptor(m, k);
		    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
		      desc = { enumerable: true, get: function() { return m[k]; } };
		    }
		    Object.defineProperty(o, k2, desc);
		}) : (function(o, m, k, k2) {
		    if (k2 === undefined) k2 = k;
		    o[k2] = m[k];
		}));
		var __exportStar = (runtime && runtime.__exportStar) || function(m, exports) {
		    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
		};
		Object.defineProperty(exports, "__esModule", { value: true });
		__exportStar(requireConstants(), exports);
		__exportStar(require$$1, exports);
		__exportStar(requireEnhance(), exports);
		__exportStar(requireError(), exports);
		__exportStar(requireTypes(), exports);
		__exportStar(requireValidation(), exports);
		__exportStar(requireVersion(), exports);
		
	} (runtime));
	return runtime;
}

requireRuntime();

function isNumericString(value) {
  if (!value || typeof value !== "string") {
    return false;
  }
  const trimmed = value.trim();
  if (trimmed === "") {
    return false;
  }
  const num = Number(trimmed);
  return !isNaN(num) && isFinite(num);
}
function parseNumericValue(value) {
  if (!isNumericString(value)) {
    return null;
  }
  const num = Number(value.trim());
  if (isNaN(num) || !isFinite(num)) {
    return null;
  }
  return num;
}
function syncAttributeValues(data) {
  const value = data.value || "";
  const numericValue = parseNumericValue(value);
  return {
    value,
    numericValue
  };
}
function createAttributeMiddleware(modelName) {
  return async ({ args, query }) => {
    if (args.data?.value !== void 0) {
      const synced = syncAttributeValues({ value: args.data.value });
      args.data.numericValue = synced.numericValue;
    }
    if (args.data?.createMany?.data) {
      args.data.createMany.data = args.data.createMany.data.map((item) => {
        if (item.value !== void 0) {
          const synced = syncAttributeValues({ value: item.value });
          return { ...item, numericValue: synced.numericValue };
        }
        return item;
      });
    }
    return query(args);
  };
}
function updateAttributeMiddleware(modelName) {
  return async ({ args, query }) => {
    if (args.data?.value !== void 0) {
      const synced = syncAttributeValues({ value: args.data.value });
      args.data.numericValue = synced.numericValue;
    }
    if (args.data?.value?.set !== void 0) {
      const synced = syncAttributeValues({ value: args.data.value.set });
      args.data.numericValue = { set: synced.numericValue };
    }
    return query(args);
  };
}
function upsertAttributeMiddleware(modelName) {
  return async ({ args, query }) => {
    if (args.create?.value !== void 0) {
      const synced = syncAttributeValues({ value: args.create.value });
      args.create.numericValue = synced.numericValue;
    }
    if (args.update?.value !== void 0) {
      const synced = syncAttributeValues({ value: args.update.value });
      args.update.numericValue = synced.numericValue;
    }
    return query(args);
  };
}

const UPLOADS_ROOT = join(process.cwd(), "uploads");
const ALLOWED_IMAGE_MIME = /* @__PURE__ */ new Set([
  "image/jpeg",
  "image/png",
  "image/webp",
  "image/gif",
  "image/svg+xml"
]);
/* @__PURE__ */ new Set([...ALLOWED_IMAGE_MIME, "application/pdf"]);
function resolveUrlToFilePath(url) {
  const prefix = "/api/uploads/";
  if (!url.startsWith(prefix)) {
    throw new Error("Invalid media URL");
  }
  const relative = url.slice(prefix.length);
  return join(UPLOADS_ROOT, relative);
}
async function deleteFileIfExists(filePath) {
  try {
    await unlink(filePath);
  } catch (e) {
  }
}

const basePrisma = new _defaultExports.PrismaClient({
  log: ["query", "info", "warn", "error"]
});
const prisma = basePrisma.$extends({
  query: {
    partAttribute: {
      create: createAttributeMiddleware(),
      update: updateAttributeMiddleware(),
      upsert: upsertAttributeMiddleware(),
      createMany: createAttributeMiddleware(),
      updateMany: updateAttributeMiddleware()
    },
    catalogItemAttribute: {
      create: createAttributeMiddleware(),
      update: updateAttributeMiddleware(),
      upsert: upsertAttributeMiddleware(),
      createMany: createAttributeMiddleware(),
      updateMany: updateAttributeMiddleware()
    },
    equipmentModelAttribute: {
      create: createAttributeMiddleware(),
      update: updateAttributeMiddleware(),
      upsert: upsertAttributeMiddleware(),
      createMany: createAttributeMiddleware(),
      updateMany: updateAttributeMiddleware()
    },
    // Автоматическое удаление связанных изображений при удалении Part
    part: {
      async delete({ args, query }) {
        const toDelete = await basePrisma.part.findUnique({
          where: args.where,
          include: { image: true }
        });
        const result = await query(args);
        if (toDelete?.imageId && toDelete.image?.url) {
          try {
            await basePrisma.mediaAsset.delete({ where: { id: toDelete.imageId } });
          } catch {
          }
          const path = resolveUrlToFilePath(toDelete.image.url);
          await deleteFileIfExists(path);
        }
        return result;
      },
      async deleteMany({ args, query }) {
        const toDelete = await basePrisma.part.findMany({
          where: args.where,
          select: { imageId: true, image: { select: { url: true } } }
        });
        const result = await query(args);
        for (const item of toDelete) {
          if (item.imageId && item.image?.url) {
            try {
              await basePrisma.mediaAsset.delete({ where: { id: item.imageId } });
            } catch {
            }
            const path = resolveUrlToFilePath(item.image.url);
            await deleteFileIfExists(path);
          }
        }
        return result;
      }
    },
    // Автоматическое удаление связанных изображений при удалении PartCategory
    partCategory: {
      async delete({ args, query }) {
        const toDelete = await basePrisma.partCategory.findUnique({
          where: args.where,
          include: { image: true }
        });
        const result = await query(args);
        if (toDelete?.imageId && toDelete.image?.url) {
          try {
            await basePrisma.mediaAsset.delete({ where: { id: toDelete.imageId } });
          } catch {
          }
          const path = resolveUrlToFilePath(toDelete.image.url);
          await deleteFileIfExists(path);
        }
        return result;
      },
      async deleteMany({ args, query }) {
        const toDelete = await basePrisma.partCategory.findMany({
          where: args.where,
          select: { imageId: true, image: { select: { url: true } } }
        });
        const result = await query(args);
        for (const item of toDelete) {
          if (item.imageId && item.image?.url) {
            try {
              await basePrisma.mediaAsset.delete({ where: { id: item.imageId } });
            } catch {
            }
            const path = resolveUrlToFilePath(item.image.url);
            await deleteFileIfExists(path);
          }
        }
        return result;
      }
    },
    // Если удаляют MediaAsset напрямую — чистим файл на диске
    mediaAsset: {
      async delete({ args, query }) {
        const toDelete = await basePrisma.mediaAsset.findUnique({ where: args.where });
        const result = await query(args);
        if (toDelete?.url) {
          const path = resolveUrlToFilePath(toDelete.url);
          await deleteFileIfExists(path);
        }
        return result;
      },
      async deleteMany({ args, query }) {
        const toDelete = await basePrisma.mediaAsset.findMany({ where: args.where });
        const result = await query(args);
        for (const item of toDelete) {
          if (item.url) {
            const path = resolveUrlToFilePath(item.url);
            await deleteFileIfExists(path);
          }
        }
        return result;
      }
    }
  }
});
const $prisma = prisma;

const statement = {
  ...defaultStatements,
  user: ["ban", "delete", "update", "read"],
  session: ["revoke"],
  project: ["create", "update", "delete", "share"]
};
const ac = createAccessControl(statement);
const user = ac.newRole({
  project: ["create"]
});
const admin = ac.newRole({
  ...adminAc.statements,
  user: ["ban", "delete", "update", "read"],
  session: ["revoke"],
  project: ["create", "update", "delete", "share"]
});

const auth = betterAuth({
  // Используем переменные окружения для URL
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3000",
  // Расширенный список доверенных источников
  trustedOrigins: [
    "http://localhost:4321",
    // Astro dev server
    "http://localhost:4322",
    "http://localhost:4323",
    "http://localhost:4444",
    "http://localhost:3000",
    // API server
    // В продакшене добавить реальные домены
    ...process.env.NODE_ENV === "production" ? [
      process.env.FRONTEND_URL,
      process.env.PRODUCTION_URL
    ].filter((url) => Boolean(url)) : []
  ],
  database: prismaAdapter($prisma, {
    provider: "postgresql"
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
    // Пока отключаем для разработки
    minPasswordLength: 8,
    maxPasswordLength: 128,
    autoSignIn: true
    // Автоматический вход после регистрации
  },
  emailVerification: {
    sendOnSignUp: false,
    // Не отправляем автоматически при регистрации
    sendOnSignIn: false,
    // Не отправляем при входе
    autoSignInAfterVerification: true,
    expiresIn: 60 * 60 * 24,
    // 24 часа
    sendVerificationEmail: async ({ user, url, token }) => {
      console.log("📧 ОТПРАВКА EMAIL ВЕРИФИКАЦИИ:");
      console.log("👤 Пользователь:", user.email);
      console.log("🔗 URL верификации:", url);
      console.log("🎫 Токен:", token);
      console.log('✅ Email верификация "отправлена" (в режиме разработки)');
    }
  },
  user: {
    additionalFields: {
      role: {
        type: "string",
        defaultValue: "USER",
        required: false,
        returned: true
        // Явно указываем, что поле должно возвращаться
      }
    }
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7,
    // 7 дней
    updateAge: 60 * 60 * 24,
    // Обновлять каждый день
    cookieCache: {
      enabled: true,
      maxAge: 60 * 5
      // 5 минут кеш
    }
  },
  // Конфигурация аккаунтов
  account: {
    accountLinking: {
      enabled: true,
      trustedProviders: ["email-password"]
      // Пока только email/password
    }
  },
  plugins: [
    phoneNumber({
      sendOTP: ({ phoneNumber: phoneNumber2, code }) => {
        console.log("📱 ОТПРАВКА SMS OTP:");
        console.log("📞 Номер телефона:", phoneNumber2);
        console.log("🔢 Код:", code);
      }
    }),
    anonymous(),
    username(),
    admin$1({
      adminRoles: ["ADMIN"],
      defaultRole: "USER",
      defaultBanReason: "Нарушение правил использования",
      defaultBanExpiresIn: 60 * 60 * 24 * 30,
      impersonationSessionDuration: 60 * 60,
      bannedUserMessage: "Ваш аккаунт заблокирован. Обратитесь в службу поддержки для получения дополнительной информации.",
      // Access Control
      ac,
      roles: {
        admin: admin,
        user: user
      }
    })
  ],
  // Настройки безопасности и производительности
  rateLimit: {
    enabled: process.env.NODE_ENV === "production",
    // Включаем в продакшене
    window: 10,
    // 10 секунд
    max: 100,
    // 100 запросов
    customRules: {
      "/sign-in/email": { window: 60, max: 5 },
      // Ограничиваем попытки входа
      "/sign-up/email": { window: 60, max: 3 }
      // Ограничиваем регистрации
    }
  },
  advanced: {
    // IP адрес для rate limiting
    ipAddress: {
      ipAddressHeaders: ["x-forwarded-for", "x-real-ip"],
      disableIpTracking: false
    },
    // Настройки cookies
    crossSubDomainCookies: {
      enabled: false
      // Отключаем для localhost
    },
    defaultCookieAttributes: {
      sameSite: "lax",
      // Более безопасно для localhost
      secure: process.env.NODE_ENV === "production",
      // Secure только в продакшене
      httpOnly: true,
      // Защита от XSS
      path: "/"
      // Доступно для всего сайта
    },
    // Настройки базы данных
    database: {
      generateId: () => {
        return crypto.randomUUID();
      }
    },
    // Отключаем CSRF проверку только для разработки
    disableCSRFCheck: process.env.NODE_ENV === "development"
  },
  // Логирование
  logger: {
    level: process.env.NODE_ENV === "production" ? "error" : "info",
    disabled: false
  }
});

const PROTECTED_ROUTES = [
  "/account"
];
const GUEST_ONLY_ROUTES = [
  "/login",
  "/register"
];
function isProtectedRoute(pathname) {
  return PROTECTED_ROUTES.some((route) => pathname === route || pathname.startsWith(route + "/"));
}
function isGuestOnlyRoute(pathname) {
  return GUEST_ONLY_ROUTES.some((route) => pathname === route || pathname.startsWith(route + "/"));
}
const onRequest$1 = defineMiddleware(async (context, next) => {
  const { url, request, redirect } = context;
  const pathname = url.pathname;
  try {
    const session = await auth.api.getSession({ headers: request.headers });
    const user = session?.user;
    const isAuthenticated = !!user;
    if (false) ;
    context.locals.user = user ?? null;
    context.locals.session = session?.session ?? null;
    if (isGuestOnlyRoute(pathname)) {
      if (isAuthenticated) return redirect("/account");
      return next();
    }
    if (isProtectedRoute(pathname)) {
      if (!isAuthenticated) return redirect("/login");
      return next();
    }
    return next();
  } catch (e) {
    console.error("Auth middleware error (/site):", e);
    if (isProtectedRoute(pathname)) return redirect("/login");
    return next();
  }
});

const onRequest = sequence(
	
	onRequest$1
	
);

export { onRequest };
