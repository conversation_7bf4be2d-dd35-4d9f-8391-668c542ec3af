import { e as createComponent, k as renderComponent, r as renderTemplate } from '../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { c as cn, B as Button, $ as $$MainLayout } from '../chunks/MainLayout_B4WbK6nK.mjs';
import { jsx, jsxs, Fragment } from 'react/jsx-runtime';
import * as React from 'react';
import { useState, useRef, useEffect, useMemo } from 'react';
import { XIcon, Bolt, Search, Bell, Save, Calculator, Download, Bot, ChevronUp, ChevronDown, Filter, X, Sliders, Maximize2, Minimize2, Lightbulb, ChevronRight, Zap, Send, MessageCircle, Sparkles, TrendingUp, ImageIcon, FileText, ChevronLeft, ZoomIn, Package, List, Grid3X3, BarChart3, ArrowLeftRight, Building, Clock, Eye } from 'lucide-react';
import { Slot } from '@radix-ui/react-slot';
import { cva } from 'class-variance-authority';
import { B as Badge } from '../chunks/badge_CaSK_yxb.mjs';
import { L as Label, C as Checkbox, S as Separator, a as StatusBadge } from '../chunks/label_5LZUw9kF.mjs';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import * as TabsPrimitive from '@radix-ui/react-tabs';
import * as CollapsiblePrimitive from '@radix-ui/react-collapsible';
import * as SliderPrimitive from '@radix-ui/react-slider';
import { a as trpc, T as TrpcProvider } from '../chunks/TrpcProvider_JR8NncQp.mjs';
export { renderers } from '../renderers.mjs';

const modernButtonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow-sm hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline: "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        gradient: "gradient-primary text-primary-foreground shadow-sm hover:opacity-90"
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default"
    }
  }
);
const ModernButton = React.forwardRef(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return /* @__PURE__ */ jsx(Comp, { className: cn(modernButtonVariants({ variant, size, className })), ref, ...props });
  }
);
ModernButton.displayName = "ModernButton";

const ModernCard = React.forwardRef(({ className, variant = "default", ...props }, ref) => /* @__PURE__ */ jsx(
  "div",
  {
    ref,
    className: cn(
      "rounded-md border transition-all duration-200 animate-theme-transition",
      {
        "bg-card text-card-foreground border-border shadow-professional hover:shadow-elevated hover:border-border-strong": variant === "default",
        "bg-card text-card-foreground border-border-strong shadow-elevated hover:shadow-strong": variant === "elevated",
        "glass-effect text-card-foreground border-border": variant === "glass"
      },
      className
    ),
    ...props
  }
));
ModernCard.displayName = "ModernCard";
const ModernCardHeader = React.forwardRef(
  ({ className, ...props }, ref) => /* @__PURE__ */ jsx("div", { ref, className: cn("flex flex-col space-y-1 p-4 pb-2", className), ...props })
);
ModernCardHeader.displayName = "ModernCardHeader";
const ModernCardTitle = React.forwardRef(
  ({ className, ...props }, ref) => /* @__PURE__ */ jsx("h3", { ref, className: cn("font-semibold leading-none tracking-tight", className), ...props })
);
ModernCardTitle.displayName = "ModernCardTitle";
const ModernCardDescription = React.forwardRef(
  ({ className, ...props }, ref) => /* @__PURE__ */ jsx("p", { ref, className: cn("text-sm text-muted-foreground leading-relaxed", className), ...props })
);
ModernCardDescription.displayName = "ModernCardDescription";
const ModernCardContent = React.forwardRef(
  ({ className, ...props }, ref) => /* @__PURE__ */ jsx("div", { ref, className: cn("p-4 pt-0", className), ...props })
);
ModernCardContent.displayName = "ModernCardContent";
const ModernCardFooter = React.forwardRef(
  ({ className, ...props }, ref) => /* @__PURE__ */ jsx("div", { ref, className: cn("flex items-center p-4 pt-0", className), ...props })
);
ModernCardFooter.displayName = "ModernCardFooter";

function Dialog({
  ...props
}) {
  return /* @__PURE__ */ jsx(DialogPrimitive.Root, { "data-slot": "dialog", ...props });
}
function DialogTrigger({
  ...props
}) {
  return /* @__PURE__ */ jsx(DialogPrimitive.Trigger, { "data-slot": "dialog-trigger", ...props });
}
function DialogPortal({
  ...props
}) {
  return /* @__PURE__ */ jsx(DialogPrimitive.Portal, { "data-slot": "dialog-portal", ...props });
}
function DialogOverlay({
  className,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    DialogPrimitive.Overlay,
    {
      "data-slot": "dialog-overlay",
      className: cn(
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",
        className
      ),
      ...props
    }
  );
}
function DialogContent({
  className,
  children,
  showCloseButton = true,
  ...props
}) {
  return /* @__PURE__ */ jsxs(DialogPortal, { "data-slot": "dialog-portal", children: [
    /* @__PURE__ */ jsx(DialogOverlay, {}),
    /* @__PURE__ */ jsxs(
      DialogPrimitive.Content,
      {
        "data-slot": "dialog-content",
        className: cn(
          "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",
          className
        ),
        ...props,
        children: [
          children,
          showCloseButton && /* @__PURE__ */ jsxs(
            DialogPrimitive.Close,
            {
              "data-slot": "dialog-close",
              className: "ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
              children: [
                /* @__PURE__ */ jsx(XIcon, {}),
                /* @__PURE__ */ jsx("span", { className: "sr-only", children: "Close" })
              ]
            }
          )
        ]
      }
    )
  ] });
}
function DialogHeader({ className, ...props }) {
  return /* @__PURE__ */ jsx(
    "div",
    {
      "data-slot": "dialog-header",
      className: cn("flex flex-col gap-2 text-center sm:text-left", className),
      ...props
    }
  );
}
function DialogTitle({
  className,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    DialogPrimitive.Title,
    {
      "data-slot": "dialog-title",
      className: cn("text-lg leading-none font-semibold", className),
      ...props
    }
  );
}

function Tabs({
  className,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    TabsPrimitive.Root,
    {
      "data-slot": "tabs",
      className: cn("flex flex-col gap-2", className),
      ...props
    }
  );
}
function TabsList({
  className,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    TabsPrimitive.List,
    {
      "data-slot": "tabs-list",
      className: cn(
        "bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",
        className
      ),
      ...props
    }
  );
}
function TabsTrigger({
  className,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    TabsPrimitive.Trigger,
    {
      "data-slot": "tabs-trigger",
      className: cn(
        "data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      ),
      ...props
    }
  );
}
function TabsContent({
  className,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    TabsPrimitive.Content,
    {
      "data-slot": "tabs-content",
      className: cn("flex-1 outline-none", className),
      ...props
    }
  );
}

const ModernInput = React.forwardRef(
  ({ className, type, variant = "default", ...props }, ref) => {
    return /* @__PURE__ */ jsx(
      "input",
      {
        type,
        className: cn(
          "flex h-11 w-full rounded-lg border border-input bg-input px-4 py-2 text-sm transition-all duration-200",
          "file:border-0 file:bg-transparent file:text-sm file:font-medium",
          "placeholder:text-muted-foreground",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-0",
          "focus-visible:border-primary/50 focus-visible:bg-background/50",
          "disabled:cursor-not-allowed disabled:opacity-50",
          "hover:border-primary/30 hover:bg-background/30",
          {
            "border-transparent bg-muted/50": variant === "ghost",
            "bg-muted border-muted": variant === "filled"
          },
          className
        ),
        ref,
        ...props
      }
    );
  }
);
ModernInput.displayName = "ModernInput";

function ProfessionalHeader({ totalCount, filteredCount, searchQuery, onSearchChange, onOpenAI }) {
  return /* @__PURE__ */ jsx("header", { className: "sticky top-0 z-50 w-full border-b border-border/40 bg-background/80 backdrop-blur-xl", children: /* @__PURE__ */ jsxs("div", { className: "container flex h-16 max-w-screen-2xl items-center justify-between px-6", children: [
    /* @__PURE__ */ jsx("div", { className: "flex items-center gap-4", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-3", children: [
      /* @__PURE__ */ jsx("div", { className: "flex bg-zinc-900 h-10 w-10 items-center justify-center rounded-xl shadow-lg", children: /* @__PURE__ */ jsx(Bolt, { className: "h-5 w-5 text-primary-foreground" }) }),
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("h1", { className: "text-xl font-bold tracking-tight", children: "Каталог" }),
        /* @__PURE__ */ jsx("p", { className: "text-sm text-muted-foreground", children: "Профессиональная система каталогизации" })
      ] })
    ] }) }),
    /* @__PURE__ */ jsx("div", { className: "flex flex-1 items-center justify-center px-8", children: /* @__PURE__ */ jsxs("div", { className: "relative w-full max-w-lg", children: [
      /* @__PURE__ */ jsx(Search, { className: "absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" }),
      /* @__PURE__ */ jsx(
        ModernInput,
        {
          placeholder: "Поиск по артикулу, описанию, бренду...",
          value: searchQuery,
          onChange: (e) => onSearchChange(e.target.value),
          className: "pl-10 pr-4",
          variant: "ghost"
        }
      )
    ] }) }),
    /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-3", children: [
      /* @__PURE__ */ jsxs(Badge, { variant: "outline", className: "hidden sm:flex", children: [
        filteredCount,
        " / ",
        totalCount
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
        /* @__PURE__ */ jsxs(ModernButton, { variant: "ghost", size: "icon", className: "relative", children: [
          /* @__PURE__ */ jsx(Bell, { className: "h-4 w-4" }),
          /* @__PURE__ */ jsx("span", { className: "absolute -top-1 -right-1 h-2 w-2 rounded-full bg-destructive" })
        ] }),
        /* @__PURE__ */ jsxs(ModernButton, { variant: "outline", size: "sm", children: [
          /* @__PURE__ */ jsx(Save, { className: "h-4 w-4" }),
          /* @__PURE__ */ jsx("span", { className: "hidden sm:inline", children: "Сохранить" })
        ] }),
        /* @__PURE__ */ jsxs(ModernButton, { variant: "outline", size: "sm", children: [
          /* @__PURE__ */ jsx(Calculator, { className: "h-4 w-4" }),
          /* @__PURE__ */ jsx("span", { className: "hidden sm:inline", children: "Калькулятор" })
        ] }),
        /* @__PURE__ */ jsxs(ModernButton, { variant: "outline", size: "sm", children: [
          /* @__PURE__ */ jsx(Download, { className: "h-4 w-4" }),
          /* @__PURE__ */ jsx("span", { className: "hidden sm:inline", children: "Экспорт" })
        ] }),
        /* @__PURE__ */ jsxs(ModernButton, { variant: "gradient", size: "sm", onClick: onOpenAI, className: "gap-2", children: [
          /* @__PURE__ */ jsx(Bot, { className: "h-4 w-4" }),
          /* @__PURE__ */ jsx("span", { className: "hidden sm:inline", children: "AI Помощник" })
        ] })
      ] })
    ] })
  ] }) });
}

function Collapsible({
  ...props
}) {
  return /* @__PURE__ */ jsx(CollapsiblePrimitive.Root, { "data-slot": "collapsible", ...props });
}
function CollapsibleTrigger({
  ...props
}) {
  return /* @__PURE__ */ jsx(
    CollapsiblePrimitive.CollapsibleTrigger,
    {
      "data-slot": "collapsible-trigger",
      ...props
    }
  );
}
function CollapsibleContent({
  ...props
}) {
  return /* @__PURE__ */ jsx(
    CollapsiblePrimitive.CollapsibleContent,
    {
      "data-slot": "collapsible-content",
      ...props
    }
  );
}

function Slider({
  className,
  defaultValue,
  value,
  min = 0,
  max = 100,
  ...props
}) {
  const _values = React.useMemo(
    () => Array.isArray(value) ? value : Array.isArray(defaultValue) ? defaultValue : [min, max],
    [value, defaultValue, min, max]
  );
  return /* @__PURE__ */ jsxs(
    SliderPrimitive.Root,
    {
      "data-slot": "slider",
      defaultValue,
      value,
      min,
      max,
      className: cn(
        "relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",
        className
      ),
      ...props,
      children: [
        /* @__PURE__ */ jsx(
          SliderPrimitive.Track,
          {
            "data-slot": "slider-track",
            className: cn(
              "bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"
            ),
            children: /* @__PURE__ */ jsx(
              SliderPrimitive.Range,
              {
                "data-slot": "slider-range",
                className: cn(
                  "bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full"
                )
              }
            )
          }
        ),
        Array.from({ length: _values.length }, (_, index) => /* @__PURE__ */ jsx(
          SliderPrimitive.Thumb,
          {
            "data-slot": "slider-thumb",
            className: "border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"
          },
          index
        ))
      ]
    }
  );
}

function AttributeFilter({ template, selectedValues, numericRange, onValuesChange, onRangeChange, availableValues, numericStats }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const handleValueToggle = (value) => {
    if (selectedValues.includes(value)) onValuesChange(selectedValues.filter((v) => v !== value));
    else onValuesChange([...selectedValues, value]);
  };
  const isNumeric = template.dataType === "NUMBER";
  const hasActiveFilters = selectedValues.length > 0 || numericRange && numericStats && (numericRange[0] > numericStats.min || numericRange[1] < numericStats.max);
  const filteredValues = availableValues.filter((value) => value.toLowerCase().includes(searchQuery.toLowerCase()));
  return /* @__PURE__ */ jsxs("div", { className: "border-2 border-border-strong rounded-lg bg-card animate-theme-transition", children: [
    /* @__PURE__ */ jsxs(Button, { variant: "ghost", onClick: () => setIsExpanded(!isExpanded), className: "w-full justify-between p-4 text-foreground hover:bg-accent/50 transition-colors", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
        /* @__PURE__ */ jsx("span", { className: "font-medium", children: template.title }),
        template.unit && /* @__PURE__ */ jsx(Badge, { variant: "outline", className: "text-xs border-border text-muted-foreground", children: template.unit }),
        hasActiveFilters && /* @__PURE__ */ jsx(Badge, { variant: "default", className: "text-xs bg-primary text-primary-foreground", children: selectedValues.length || "range" })
      ] }),
      isExpanded ? /* @__PURE__ */ jsx(ChevronUp, { className: "w-4 h-4" }) : /* @__PURE__ */ jsx(ChevronDown, { className: "w-4 h-4" })
    ] }),
    isExpanded && /* @__PURE__ */ jsxs("div", { className: "p-4 pt-0 space-y-3 border-t border-border", children: [
      template.description && /* @__PURE__ */ jsx("p", { className: "text-xs text-muted-foreground", children: template.description }),
      isNumeric && numericStats ? /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex justify-between text-sm text-muted-foreground", children: [
          /* @__PURE__ */ jsxs("span", { children: [
            "Диапазон: ",
            numericStats.min,
            " - ",
            numericStats.max
          ] }),
          /* @__PURE__ */ jsxs("span", { children: [
            "Среднее: ",
            numericStats.avg.toFixed(1)
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxs(Label, { className: "text-sm text-foreground", children: [
            "Выбранный диапазон: ",
            numericRange?.[0] || numericStats.min,
            " - ",
            numericRange?.[1] || numericStats.max
          ] }),
          /* @__PURE__ */ jsx(Slider, { value: numericRange || [numericStats.min, numericStats.max], onValueChange: (value) => onRangeChange([value[0], value[1]]), min: numericStats.min, max: numericStats.max, step: template.tolerance || 0.1, className: "w-full" })
        ] }),
        template.tolerance && /* @__PURE__ */ jsxs("p", { className: "text-xs text-muted-foreground", children: [
          "Допуск: ±",
          template.tolerance,
          " ",
          template.unit
        ] })
      ] }) : /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
        availableValues.length > 5 && /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx(Search, { className: "absolute left-3 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground" }),
          /* @__PURE__ */ jsx(ModernInput, { placeholder: "Поиск значений...", value: searchQuery, onChange: (e) => setSearchQuery(e.target.value), className: "pl-9 h-8 text-sm", variant: "ghost" })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "space-y-2 max-h-48 overflow-y-auto", children: [
          filteredValues.map((value) => /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2 group", children: [
            /* @__PURE__ */ jsx(Checkbox, { id: `${template.id}-${value}`, checked: selectedValues.includes(value), onCheckedChange: () => handleValueToggle(value), className: "border-border data-[state=checked]:bg-primary data-[state=checked]:border-primary" }),
            /* @__PURE__ */ jsx(Label, { htmlFor: `${template.id}-${value}`, className: "text-sm text-foreground cursor-pointer flex-1 group-hover:text-primary transition-colors", children: value })
          ] }, value)),
          filteredValues.length === 0 && searchQuery && /* @__PURE__ */ jsxs("p", { className: "text-sm text-muted-foreground text-center py-2", children: [
            'Ничего не найдено по запросу "',
            searchQuery,
            '"'
          ] }),
          availableValues.length === 0 && /* @__PURE__ */ jsx("p", { className: "text-sm text-muted-foreground text-center py-2", children: "Нет доступных значений" })
        ] })
      ] })
    ] })
  ] });
}

function ModernFiltersPanel({ filters, setFilters, activeFiltersCount, onClearAll, mockCategories, mockBrands, mockAttributeTemplates, availableAttributeValues }) {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [expandedSections, setExpandedSections] = useState({ categories: true, brands: true, accuracy: true });
  const [categorySearch, setCategorySearch] = useState("");
  const [brandSearch, setBrandSearch] = useState("");
  const [accuracySearch, setAccuracySearch] = useState("");
  const handleMultiSelect = (value, selected, setter) => {
    if (selected.includes(value)) setter(selected.filter((v) => v !== value));
    else setter([...selected, value]);
  };
  const toggleSection = (section) => setExpandedSections((prev) => ({ ...prev, [section]: !prev[section] }));
  const accuracyOptions = [
    { value: "EXACT_MATCH", label: "Точное совпадение" },
    { value: "MATCH_WITH_NOTES", label: "С примечаниями" },
    { value: "REQUIRES_MODIFICATION", label: "Требует доработки" },
    { value: "PARTIAL_MATCH", label: "Частичное совпадение" }
  ];
  const filteredCategories = mockCategories.filter((category) => category.name.toLowerCase().includes(categorySearch.toLowerCase()));
  const filteredBrands = mockBrands.filter((brand) => brand.name.toLowerCase().includes(brandSearch.toLowerCase()));
  const filteredAccuracyOptions = accuracyOptions.filter((option) => option.label.toLowerCase().includes(accuracySearch.toLowerCase()));
  return /* @__PURE__ */ jsx("div", { className: "w-72 border-r border-border-strong bg-surface/50", children: /* @__PURE__ */ jsx("div", { className: "sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto", children: /* @__PURE__ */ jsxs("div", { className: "p-4 space-y-4", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
        /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx(Filter, { className: "h-4 w-4 text-primary" }),
          activeFiltersCount > 0 && /* @__PURE__ */ jsx("div", { className: "absolute -top-1 -right-1 h-3 w-3 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold", children: activeFiltersCount })
        ] }),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("h2", { className: "font-semibold text-sm", children: "Фильтры" }),
          activeFiltersCount > 0 && /* @__PURE__ */ jsxs("p", { className: "text-xs text-muted-foreground", children: [
            activeFiltersCount,
            " активных"
          ] })
        ] })
      ] }),
      activeFiltersCount > 0 && /* @__PURE__ */ jsxs(ModernButton, { variant: "outline", size: "sm", onClick: onClearAll, className: "h-7 px-2 text-xs gap-1 hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30", children: [
        /* @__PURE__ */ jsx(X, { className: "h-3 w-3" }),
        "Очистить"
      ] })
    ] }),
    activeFiltersCount > 0 && /* @__PURE__ */ jsx(ModernCard, { variant: "glass", className: "border-primary/20 bg-primary/5", children: /* @__PURE__ */ jsxs(ModernCardContent, { className: "p-3", children: [
      /* @__PURE__ */ jsx("div", { className: "flex items-center gap-2 mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-xs font-medium", children: "Активные фильтры" }) }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-wrap gap-1", children: [
        filters.categoryIds.length > 0 && /* @__PURE__ */ jsxs(Badge, { variant: "secondary", className: "text-xs h-5", children: [
          "Категории: ",
          filters.categoryIds.length
        ] }),
        filters.brandIds.length > 0 && /* @__PURE__ */ jsxs(Badge, { variant: "secondary", className: "text-xs h-5", children: [
          "Бренды: ",
          filters.brandIds.length
        ] }),
        filters.accuracyLevels && filters.accuracyLevels.length > 0 && /* @__PURE__ */ jsxs(Badge, { variant: "secondary", className: "text-xs h-5", children: [
          "Точность: ",
          filters.accuracyLevels.length
        ] }),
        filters.attributeFilters && Object.keys(filters.attributeFilters).length > 0 && /* @__PURE__ */ jsxs(Badge, { variant: "secondary", className: "text-xs h-5", children: [
          "Параметры: ",
          Object.keys(filters.attributeFilters).length
        ] }),
        filters.isOemOnly && /* @__PURE__ */ jsx(Badge, { variant: "secondary", className: "text-xs h-5", children: "Только OEM" })
      ] })
    ] }) }),
    /* @__PURE__ */ jsx(ModernCard, { variant: "default", children: /* @__PURE__ */ jsxs(Collapsible, { open: expandedSections.categories, onOpenChange: () => toggleSection("categories"), children: [
      /* @__PURE__ */ jsx(CollapsibleTrigger, { asChild: true, children: /* @__PURE__ */ jsx(ModernCardHeader, { className: "cursor-pointer p-3 hover:bg-accent/5 transition-colors", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
          /* @__PURE__ */ jsx(ModernCardTitle, { className: "text-sm", children: "Категории" }),
          filters.categoryIds.length > 0 && /* @__PURE__ */ jsx(Badge, { variant: "default", className: "h-4 w-4 rounded-full p-0 text-xs", children: filters.categoryIds.length })
        ] }),
        expandedSections.categories ? /* @__PURE__ */ jsx(ChevronUp, { className: "h-3 w-3 text-muted-foreground" }) : /* @__PURE__ */ jsx(ChevronDown, { className: "h-3 w-3 text-muted-foreground" })
      ] }) }) }),
      /* @__PURE__ */ jsx(CollapsibleContent, { children: /* @__PURE__ */ jsxs(ModernCardContent, { className: "p-3 pt-0 space-y-2", children: [
        mockCategories.length > 5 && /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx(Search, { className: "absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground" }),
          /* @__PURE__ */ jsx(ModernInput, { placeholder: "Поиск категорий...", value: categorySearch, onChange: (e) => setCategorySearch(e.target.value), className: "pl-7 h-7 text-xs", variant: "ghost" })
        ] }),
        filteredCategories.map((category) => /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2 group", children: [
          /* @__PURE__ */ jsx(Checkbox, { id: `category-${category.id}`, checked: filters.categoryIds.includes(category.id), onCheckedChange: () => handleMultiSelect(category.id, filters.categoryIds, (values) => setFilters({ ...filters, categoryIds: values })), className: "data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3" }),
          /* @__PURE__ */ jsx(Label, { htmlFor: `category-${category.id}`, className: "text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors", children: category.name })
        ] }, category.id))
      ] }) })
    ] }) }),
    /* @__PURE__ */ jsx(ModernCard, { variant: "default", children: /* @__PURE__ */ jsxs(Collapsible, { open: expandedSections.brands, onOpenChange: () => toggleSection("brands"), children: [
      /* @__PURE__ */ jsx(CollapsibleTrigger, { asChild: true, children: /* @__PURE__ */ jsx(ModernCardHeader, { className: "cursor-pointer p-3 hover:bg-accent/5 transition-colors", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
          /* @__PURE__ */ jsx(ModernCardTitle, { className: "text-sm", children: "Производители" }),
          (filters.brandIds.length > 0 || filters.isOemOnly) && /* @__PURE__ */ jsx(Badge, { variant: "default", className: "h-4 w-4 rounded-full p-0 text-xs", children: filters.brandIds.length + (filters.isOemOnly ? 1 : 0) })
        ] }),
        expandedSections.brands ? /* @__PURE__ */ jsx(ChevronUp, { className: "h-3 w-3 text-muted-foreground" }) : /* @__PURE__ */ jsx(ChevronDown, { className: "h-3 w-3 text-muted-foreground" })
      ] }) }) }),
      /* @__PURE__ */ jsx(CollapsibleContent, { children: /* @__PURE__ */ jsxs(ModernCardContent, { className: "p-3 pt-0 space-y-2", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2 p-2 rounded bg-accent/5 border border-border/30", children: [
          /* @__PURE__ */ jsx(Checkbox, { id: "oem-only", checked: filters.isOemOnly, onCheckedChange: (checked) => setFilters({ ...filters, isOemOnly: !!checked }), className: "data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3" }),
          /* @__PURE__ */ jsx(Label, { htmlFor: "oem-only", className: "text-xs cursor-pointer font-medium", children: "Только OEM производители" })
        ] }),
        /* @__PURE__ */ jsx(Separator, { className: "bg-border/30" }),
        mockBrands.length > 5 && /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx(Search, { className: "absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground" }),
          /* @__PURE__ */ jsx(ModernInput, { placeholder: "Поиск брендов...", value: brandSearch, onChange: (e) => setBrandSearch(e.target.value), className: "pl-7 h-7 text-xs", variant: "ghost" })
        ] }),
        filteredBrands.map((brand) => /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2 group", children: [
          /* @__PURE__ */ jsx(Checkbox, { id: `brand-${brand.id}`, checked: filters.brandIds.includes(brand.id), onCheckedChange: () => handleMultiSelect(brand.id, filters.brandIds, (values) => setFilters({ ...filters, brandIds: values })), className: "data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3" }),
          /* @__PURE__ */ jsxs(Label, { htmlFor: `brand-${brand.id}`, className: "text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors flex items-center gap-1", children: [
            brand.name,
            brand.isOem && /* @__PURE__ */ jsx(Badge, { variant: "outline", className: "text-xs h-4", children: "OEM" })
          ] })
        ] }, brand.id))
      ] }) })
    ] }) }),
    /* @__PURE__ */ jsx(ModernCard, { variant: "default", children: /* @__PURE__ */ jsxs(Collapsible, { open: expandedSections.accuracy, onOpenChange: () => toggleSection("accuracy"), children: [
      /* @__PURE__ */ jsx(CollapsibleTrigger, { asChild: true, children: /* @__PURE__ */ jsx(ModernCardHeader, { className: "cursor-pointer p-3 hover:bg-accent/5 transition-colors", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
          /* @__PURE__ */ jsx(ModernCardTitle, { className: "text-sm", children: "Точность применимости" }),
          filters.accuracyLevels && filters.accuracyLevels.length > 0 && /* @__PURE__ */ jsx(Badge, { variant: "default", className: "h-4 w-4 rounded-full p-0 text-xs", children: filters.accuracyLevels.length })
        ] }),
        expandedSections.accuracy ? /* @__PURE__ */ jsx(ChevronUp, { className: "h-3 w-3 text-muted-foreground" }) : /* @__PURE__ */ jsx(ChevronDown, { className: "h-3 w-3 text-muted-foreground" })
      ] }) }) }),
      /* @__PURE__ */ jsx(CollapsibleContent, { children: /* @__PURE__ */ jsxs(ModernCardContent, { className: "p-3 pt-0 space-y-2", children: [
        /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx(Search, { className: "absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground" }),
          /* @__PURE__ */ jsx(ModernInput, { placeholder: "Поиск по точности...", value: accuracySearch, onChange: (e) => setAccuracySearch(e.target.value), className: "pl-7 h-7 text-xs", variant: "ghost" })
        ] }),
        filteredAccuracyOptions.map((option) => /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2 group", children: [
          /* @__PURE__ */ jsx(Checkbox, { id: `accuracy-${option.value}`, checked: !!filters.accuracyLevels?.includes(option.value), onCheckedChange: () => handleMultiSelect(option.value, filters.accuracyLevels || [], (values) => setFilters({ ...filters, accuracyLevels: values })), className: "data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3" }),
          /* @__PURE__ */ jsx(Label, { htmlFor: `accuracy-${option.value}`, className: "text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors", children: /* @__PURE__ */ jsx(StatusBadge, { status: option.value, size: "sm", className: "border-0 bg-transparent p-0 text-xs font-normal" }) })
        ] }, option.value))
      ] }) })
    ] }) }),
    /* @__PURE__ */ jsx(ModernCard, { variant: "default", children: /* @__PURE__ */ jsxs(Collapsible, { open: showAdvancedFilters, onOpenChange: setShowAdvancedFilters, children: [
      /* @__PURE__ */ jsx(CollapsibleTrigger, { asChild: true, children: /* @__PURE__ */ jsx(ModernCardHeader, { className: "cursor-pointer p-3 hover:bg-accent/5 transition-colors", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
          /* @__PURE__ */ jsx(Sliders, { className: "h-3 w-3 text-primary" }),
          /* @__PURE__ */ jsx(ModernCardTitle, { className: "text-sm", children: "Технические параметры" }),
          filters.attributeFilters && Object.keys(filters.attributeFilters).length > 0 && /* @__PURE__ */ jsx(Badge, { variant: "default", className: "h-4 w-4 rounded-full p-0 text-xs", children: Object.keys(filters.attributeFilters).length })
        ] }),
        showAdvancedFilters ? /* @__PURE__ */ jsx(ChevronUp, { className: "h-3 w-3 text-muted-foreground" }) : /* @__PURE__ */ jsx(ChevronDown, { className: "h-3 w-3 text-muted-foreground" })
      ] }) }) }),
      /* @__PURE__ */ jsx(CollapsibleContent, { children: /* @__PURE__ */ jsx(ModernCardContent, { className: "p-3 pt-0 space-y-3", children: mockAttributeTemplates.map((template) => /* @__PURE__ */ jsx(
        AttributeFilter,
        {
          template,
          selectedValues: filters.attributeFilters?.[template.id]?.values || [],
          numericRange: filters.attributeFilters?.[template.id]?.numericRange,
          onValuesChange: (values) => {
            setFilters({ ...filters, attributeFilters: { ...filters.attributeFilters || {}, [template.id]: { ...filters.attributeFilters?.[template.id] || {}, values } } });
          },
          onRangeChange: (range) => {
            setFilters({ ...filters, attributeFilters: { ...filters.attributeFilters || {}, [template.id]: { ...filters.attributeFilters?.[template.id] || {}, numericRange: range } } });
          },
          availableValues: availableAttributeValues.values[template.id] || [],
          numericStats: availableAttributeValues.numericStats[template.id]
        },
        template.id
      )) }) })
    ] }) })
  ] }) }) });
}

const mockMessages = [
  { id: "1", type: "assistant", content: "Привет! Я ваш AI помощник по поиску уплотнений. Опишите что ищете или задайте вопрос о технических характеристиках.", timestamp: /* @__PURE__ */ new Date(), suggestions: ["Найти сальник для двигателя", "Подобрать уплотнение по размерам", "Показать все NBR материалы", "Что такое точность применимости?"] }
];
const quickActions = [
  { icon: Search, label: "Поиск по описанию", description: "Опишите деталь своими словами", prompt: "Помогите найти уплотнение по описанию" },
  { icon: Filter, label: "Умные фильтры", description: "Подберу фильтры автоматически", prompt: "Настройте фильтры для моего поиска" },
  { icon: Lightbulb, label: "Техническая помощь", description: "Объясню параметры и характеристики", prompt: "Объясните технические характеристики" }
];
function AIAssistantDrawer({ isOpen, onToggle, onApplyFilters, currentFilters, resultsCount }) {
  const [messages, setMessages] = useState(mockMessages);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const scrollToBottom = () => messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  useEffect(() => {
    if (isOpen && !isMinimized) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen, isMinimized]);
  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;
    const userMessage = { id: Date.now().toString(), type: "user", content: inputValue, timestamp: /* @__PURE__ */ new Date() };
    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setIsTyping(true);
    setTimeout(() => {
      const aiResponse = generateAIResponse(inputValue, currentFilters, resultsCount);
      setMessages((prev) => [...prev, aiResponse]);
      setIsTyping(false);
    }, 800);
  };
  const handleSuggestionClick = (suggestion) => {
    setInputValue(suggestion);
    setTimeout(() => handleSendMessage(), 100);
  };
  const handleQuickAction = (prompt) => {
    setInputValue(prompt);
    setTimeout(() => handleSendMessage(), 100);
  };
  const handleApplyFilterSuggestion = (filterSuggestion) => onApplyFilters(filterSuggestion);
  const generateAIResponse = (userInput, filters, count) => {
    const input = userInput.toLowerCase();
    if (input.includes("сальник") && input.includes("двигател")) {
      return { id: Date.now().toString(), type: "assistant", content: "Отлично! Ищете сальники для двигателя. Я настрою фильтры для более точного поиска:", timestamp: /* @__PURE__ */ new Date(), suggestions: ["Какая марка техники?", "Нужны размеры сальника?", "Какой тип двигателя?"], filterSuggestion: { categoryIds: [1], query: "сальник двигатель" } };
    }
    if (input.includes("размер") || input.includes("диаметр")) {
      return { id: Date.now().toString(), type: "assistant", content: "Для точного поиска по размерам укажите внутренний и наружный диаметр. Например: 'внутренний 25мм, наружный 35мм'", timestamp: /* @__PURE__ */ new Date(), suggestions: ["25x35x7 мм", "30x42x8 мм", "Показать популярные размеры"] };
    }
    if (input.includes("материал") || input.includes("nbr") || input.includes("fkm")) {
      return { id: Date.now().toString(), type: "assistant", content: "Материал критически важен! NBR подходит для стандартных применений (-40°C до +100°C), FKM - для высоких температур (до +200°C).", timestamp: /* @__PURE__ */ new Date(), filterSuggestion: { attributeFilters: { 3: { values: input.includes("nbr") ? ["NBR"] : input.includes("fkm") ? ["FKM"] : [] } } } };
    }
    return { id: Date.now().toString(), type: "assistant", content: `Понял ваш запрос "${userInput}". Сейчас найдено ${count} позиций. Могу помочь уточнить поиск или объяснить характеристики.`, timestamp: /* @__PURE__ */ new Date(), suggestions: ["Уточнить поиск", "Объяснить результаты", "Показать аналоги"] };
  };
  return /* @__PURE__ */ jsx(Dialog, { open: isOpen, onOpenChange: onToggle, children: /* @__PURE__ */ jsx(DialogContent, { className: "w-[480px] sm:w-[540px] p-0 border-l-2 border-border-strong right-0 top-0 fixed h-full m-0 rounded-none", children: /* @__PURE__ */ jsxs("div", { className: "flex flex-col h-full", children: [
    /* @__PURE__ */ jsx(DialogHeader, { className: "px-6 py-4 border-b border-border-strong bg-surface/50", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-3", children: [
        /* @__PURE__ */ jsx("div", { className: "h-10 w-10 rounded-xl gradient-ai flex items-center justify-center shadow-lg", children: /* @__PURE__ */ jsx(Bot, { className: "h-5 w-5 text-white" }) }),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx(DialogTitle, { className: "text-lg font-bold", children: "AI Ассистент" }),
          /* @__PURE__ */ jsx("p", { className: "text-sm text-muted-foreground", children: "Умный помощник по поиску" })
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
        /* @__PURE__ */ jsxs(Badge, { variant: "outline", className: "text-xs", children: [
          resultsCount,
          " результатов"
        ] }),
        /* @__PURE__ */ jsx(ModernButton, { variant: "ghost", size: "icon", onClick: () => setIsMinimized(!isMinimized), className: "h-8 w-8", children: isMinimized ? /* @__PURE__ */ jsx(Maximize2, { className: "h-4 w-4" }) : /* @__PURE__ */ jsx(Minimize2, { className: "h-4 w-4" }) })
      ] })
    ] }) }),
    !isMinimized ? /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsxs("div", { className: "px-6 py-4 bg-surface/30 border-b border-border", children: [
        /* @__PURE__ */ jsx("h3", { className: "text-sm font-semibold mb-3 text-muted-foreground uppercase tracking-wide", children: "Быстрые действия" }),
        /* @__PURE__ */ jsx("div", { className: "grid grid-cols-1 gap-2", children: quickActions.map((action, index) => /* @__PURE__ */ jsxs(ModernButton, { variant: "ghost", className: "h-auto p-3 justify-start text-left hover:bg-surface-hover transition-colors", onClick: () => handleQuickAction(action.prompt), children: [
          /* @__PURE__ */ jsx("div", { className: "h-8 w-8 rounded-lg bg-ai-primary/10 flex items-center justify-center mr-3", children: /* @__PURE__ */ jsx(action.icon, { className: "h-4 w-4 text-ai-primary" }) }),
          /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
            /* @__PURE__ */ jsx("div", { className: "font-medium text-sm", children: action.label }),
            /* @__PURE__ */ jsx("div", { className: "text-xs text-muted-foreground", children: action.description })
          ] }),
          /* @__PURE__ */ jsx(ChevronRight, { className: "h-4 w-4 text-muted-foreground" })
        ] }, index)) })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "flex-1 px-6 py-4 overflow-y-auto", children: /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
        messages.map((message) => /* @__PURE__ */ jsxs("div", { className: "space-y-3 animate-slide-in-right", children: [
          /* @__PURE__ */ jsxs(
            "div",
            {
              className: cn("flex gap-3", message.type === "user" ? "justify-end" : "justify-start"),
              children: [
                message.type === "assistant" && /* @__PURE__ */ jsx("div", { className: "h-8 w-8 rounded-full gradient-ai flex items-center justify-center flex-shrink-0 shadow-md", children: /* @__PURE__ */ jsx(Bot, { className: "h-4 w-4 text-white" }) }),
                /* @__PURE__ */ jsx("div", { className: cn("max-w-[85%] rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm", message.type === "user" ? "ai-message-user ml-auto" : "ai-message-assistant"), children: message.content })
              ]
            }
          ),
          message.suggestions && /* @__PURE__ */ jsx("div", { className: "flex flex-wrap gap-2 ml-11", children: message.suggestions.map((suggestion, index) => /* @__PURE__ */ jsx(ModernButton, { variant: "outline", size: "sm", className: "h-8 text-xs ai-suggestion", onClick: () => handleSuggestionClick(suggestion), children: suggestion }, index)) }),
          message.filterSuggestion && /* @__PURE__ */ jsx("div", { className: "ml-11", children: /* @__PURE__ */ jsx(ModernCard, { variant: "default", className: "p-4 border-ai-primary/20 bg-ai-primary/5", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-3", children: [
              /* @__PURE__ */ jsx("div", { className: "h-8 w-8 rounded-lg bg-ai-primary/20 flex items-center justify-center", children: /* @__PURE__ */ jsx(Zap, { className: "h-4 w-4 text-ai-primary" }) }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("span", { className: "text-sm font-medium", children: "Применить умные фильтры" }),
                /* @__PURE__ */ jsx("p", { className: "text-xs text-muted-foreground", children: "Настроено на основе вашего запроса" })
              ] })
            ] }),
            /* @__PURE__ */ jsx(ModernButton, { size: "sm", className: "gradient-ai text-white shadow-md", onClick: () => handleApplyFilterSuggestion(message.filterSuggestion), children: "Применить" })
          ] }) }) })
        ] }, message.id)),
        isTyping && /* @__PURE__ */ jsxs("div", { className: "flex gap-3 animate-slide-in-right", children: [
          /* @__PURE__ */ jsx("div", { className: "h-8 w-8 rounded-full gradient-ai flex items-center justify-center flex-shrink-0 shadow-md", children: /* @__PURE__ */ jsx(Bot, { className: "h-4 w-4 text-white" }) }),
          /* @__PURE__ */ jsx("div", { className: "ai-message-assistant rounded-2xl px-4 py-3 text-sm", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex gap-1", children: [
              /* @__PURE__ */ jsx("div", { className: "w-2 h-2 bg-ai-primary rounded-full animate-bounce", style: { animationDelay: "0ms" } }),
              /* @__PURE__ */ jsx("div", { className: "w-2 h-2 bg-ai-primary rounded-full animate-bounce", style: { animationDelay: "150ms" } }),
              /* @__PURE__ */ jsx("div", { className: "w-2 h-2 bg-ai-primary rounded-full animate-bounce", style: { animationDelay: "300ms" } })
            ] }),
            /* @__PURE__ */ jsx("span", { className: "text-muted-foreground", children: "Анализирую запрос..." })
          ] }) })
        ] }),
        /* @__PURE__ */ jsx("div", { ref: messagesEndRef })
      ] }) }),
      /* @__PURE__ */ jsxs("div", { className: "p-6 border-t border-border-strong bg-surface/30", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex gap-3", children: [
          /* @__PURE__ */ jsx(ModernInput, { ref: inputRef, placeholder: "Опишите что ищете или задайте вопрос...", value: inputValue, onChange: (e) => setInputValue(e.target.value), onKeyDown: (e) => {
            if (e.key === "Enter" && !e.shiftKey) {
              e.preventDefault();
              handleSendMessage();
            }
          }, className: "flex-1 h-12", variant: "filled" }),
          /* @__PURE__ */ jsx(ModernButton, { onClick: handleSendMessage, disabled: !inputValue.trim() || isTyping, size: "icon", className: "h-12 w-12 gradient-ai text-white shadow-md", children: /* @__PURE__ */ jsx(Send, { className: "h-5 w-5" }) })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2 mt-3 text-xs text-muted-foreground", children: [
          /* @__PURE__ */ jsx("span", { className: "text-ai-primary", children: "AI" }),
          /* @__PURE__ */ jsx("span", { children: "поможет найти нужные детали и настроить фильтры автоматически" })
        ] })
      ] })
    ] }) : /* @__PURE__ */ jsx("div", { className: "p-6 flex items-center justify-center h-full", children: /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
      /* @__PURE__ */ jsx("div", { className: "h-16 w-16 rounded-full gradient-ai flex items-center justify-center mx-auto mb-4 shadow-lg", children: /* @__PURE__ */ jsx(MessageCircle, { className: "h-8 w-8 text-white" }) }),
      /* @__PURE__ */ jsx("h3", { className: "font-semibold mb-2", children: "Чат свернут" }),
      /* @__PURE__ */ jsxs("p", { className: "text-sm text-muted-foreground mb-4", children: [
        messages.length - 1,
        " сообщений в истории"
      ] }),
      /* @__PURE__ */ jsx(ModernButton, { variant: "outline", onClick: () => setIsMinimized(false), children: "Развернуть чат" })
    ] }) })
  ] }) }) });
}

function AISearchSuggestions({ query, resultsCount, onApplyFilters, onUpdateQuery }) {
  const [suggestions, setSuggestions] = useState([]);
  const [isVisible, setIsVisible] = useState(false);
  useEffect(() => {
    const generateSuggestions = () => {
      const newSuggestions = [];
      if (resultsCount === 0 && query) {
        newSuggestions.push({ id: "no-results-1", type: "search", title: "Попробуйте упростить запрос", description: "Используйте более общие термины", action: "Упростить" });
        newSuggestions.push({ id: "no-results-2", type: "filter", title: "Расширить поиск по категориям", description: "Включить все категории уплотнений", action: "Расширить", filters: { categoryIds: [] } });
      }
      if (resultsCount > 0 && resultsCount < 5 && query) {
        newSuggestions.push({ id: "few-results-1", type: "search", title: "Найти похожие детали", description: "Поиск по синонимам и аналогам", action: "Найти похожие" });
      }
      if (resultsCount > 50) {
        newSuggestions.push({ id: "many-results-1", type: "filter", title: "Уточнить по бренду", description: "Выберите предпочтительного производителя", action: "Показать бренды" });
        newSuggestions.push({ id: "many-results-2", type: "tip", title: "Добавьте технические параметры", description: "Укажите размеры или материал для точного поиска", action: "Добавить параметры" });
      }
      if (query.toLowerCase().includes("сальник") && !query.toLowerCase().includes("двигател")) {
        newSuggestions.push({ id: "smart-1", type: "filter", title: "Сальники двигателя", description: "Самая популярная категория", action: "Показать", filters: { categoryIds: [1] } });
      }
      setSuggestions(newSuggestions);
      setIsVisible(newSuggestions.length > 0);
    };
    const timer = setTimeout(generateSuggestions, 300);
    return () => clearTimeout(timer);
  }, [query, resultsCount]);
  const handleSuggestionClick = (suggestion) => {
    if (suggestion.filters) onApplyFilters(suggestion.filters);
    else if (suggestion.type === "search") {
      if (suggestion.id === "no-results-1") {
        const simplifiedQuery = query.split(" ").slice(0, 2).join(" ");
        onUpdateQuery(simplifiedQuery);
      }
    }
  };
  if (!isVisible) return null;
  return /* @__PURE__ */ jsx(ModernCard, { variant: "glass", className: "mb-4 border-primary/20 bg-primary/5", children: /* @__PURE__ */ jsx(ModernCardContent, { className: "p-4", children: /* @__PURE__ */ jsxs("div", { className: "flex items-start gap-3", children: [
    /* @__PURE__ */ jsx("div", { className: "h-8 w-8 rounded-full bg-gradient-to-br from-primary to-accent flex items-center justify-center flex-shrink-0", children: /* @__PURE__ */ jsx(Sparkles, { className: "h-4 w-4 text-primary-foreground" }) }),
    /* @__PURE__ */ jsxs("div", { className: "flex-1 space-y-3", children: [
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("h3", { className: "font-semibold text-sm mb-1", children: "AI предложения" }),
        /* @__PURE__ */ jsx("p", { className: "text-xs text-muted-foreground", children: "Основано на вашем запросе и результатах поиска" })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "space-y-2", children: suggestions.map((suggestion) => /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between p-3 rounded-lg bg-background/50 border border-border/30 hover:border-primary/30 transition-colors", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-3", children: [
          /* @__PURE__ */ jsxs("div", { className: "h-6 w-6 rounded-full bg-muted flex items-center justify-center", children: [
            suggestion.type === "search" && /* @__PURE__ */ jsx(TrendingUp, { className: "h-3 w-3" }),
            suggestion.type === "filter" && /* @__PURE__ */ jsx(Zap, { className: "h-3 w-3" }),
            suggestion.type === "tip" && /* @__PURE__ */ jsx(Sparkles, { className: "h-3 w-3" })
          ] }),
          /* @__PURE__ */ jsxs("div", { children: [
            /* @__PURE__ */ jsx("div", { className: "font-medium text-sm", children: suggestion.title }),
            /* @__PURE__ */ jsx("div", { className: "text-xs text-muted-foreground", children: suggestion.description })
          ] })
        ] }),
        /* @__PURE__ */ jsx(ModernButton, { variant: "outline", size: "sm", onClick: () => handleSuggestionClick(suggestion), className: "h-7 text-xs", children: suggestion.action })
      ] }, suggestion.id)) })
    ] })
  ] }) }) });
}

function MediaGallery({ mediaAssets, primaryImage, title, className }) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [isPDFViewerOpen, setIsPDFViewerOpen] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState(null);
  const allAssets = [...primaryImage ? [primaryImage] : [], ...mediaAssets.filter((asset) => asset.id !== primaryImage?.id)];
  if (allAssets.length === 0) {
    return /* @__PURE__ */ jsx("div", { className: cn("flex items-center justify-center h-48 bg-muted/30 rounded-lg border-2 border-dashed border-border", className), children: /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
      /* @__PURE__ */ jsx(ImageIcon, { className: "h-12 w-12 text-muted-foreground mx-auto mb-2" }),
      /* @__PURE__ */ jsx("p", { className: "text-sm text-muted-foreground", children: "Изображения отсутствуют" })
    ] }) });
  }
  const currentAsset = allAssets[selectedIndex];
  const isImage = currentAsset?.mimeType.startsWith("image/");
  const isPDF = currentAsset?.mimeType === "application/pdf";
  const formatFileSize = (bytes) => {
    if (!bytes) return "";
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  };
  const handleAssetClick = (asset, index) => {
    setSelectedIndex(index);
    setSelectedAsset(asset);
    if (asset.mimeType.startsWith("image/")) setIsLightboxOpen(true);
    else if (asset.mimeType === "application/pdf") setIsPDFViewerOpen(true);
  };
  const nextImage = () => setSelectedIndex((prev) => (prev + 1) % allAssets.length);
  const prevImage = () => setSelectedIndex((prev) => (prev - 1 + allAssets.length) % allAssets.length);
  return /* @__PURE__ */ jsxs("div", { className: cn("space-y-4", className), children: [
    /* @__PURE__ */ jsxs("div", { className: "relative group", children: [
      /* @__PURE__ */ jsx("div", { className: "aspect-video bg-muted/30 rounded-lg overflow-hidden border-2 border-border-strong", children: isImage ? /* @__PURE__ */ jsx("img", { src: currentAsset.url || "/placeholder.svg", alt: currentAsset.fileName, className: "w-full h-full object-contain cursor-zoom-in hover:scale-105 transition-transform duration-300", onClick: () => handleAssetClick(currentAsset, selectedIndex), loading: "lazy" }) : isPDF ? /* @__PURE__ */ jsx("div", { className: "w-full h-full flex items-center justify-center cursor-pointer hover:bg-muted/50 transition-colors", onClick: () => handleAssetClick(currentAsset, selectedIndex), children: /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
        /* @__PURE__ */ jsx(FileText, { className: "h-16 w-16 text-primary mx-auto mb-4" }),
        /* @__PURE__ */ jsx("h3", { className: "font-semibold mb-2", children: currentAsset.fileName }),
        /* @__PURE__ */ jsx(Badge, { variant: "outline", className: "mb-2", children: "PDF" }),
        currentAsset.fileSize && /* @__PURE__ */ jsx("p", { className: "text-sm text-muted-foreground", children: formatFileSize(currentAsset.fileSize) })
      ] }) }) : /* @__PURE__ */ jsx("div", { className: "w-full h-full flex items-center justify-center", children: /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
        /* @__PURE__ */ jsx(FileText, { className: "h-16 w-16 text-muted-foreground mx-auto mb-4" }),
        /* @__PURE__ */ jsx("p", { className: "text-sm text-muted-foreground", children: "Неподдерживаемый формат" })
      ] }) }) }),
      allAssets.length > 1 && /* @__PURE__ */ jsxs(Fragment, { children: [
        /* @__PURE__ */ jsx(ModernButton, { variant: "ghost", size: "icon", className: "absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-background/80 backdrop-blur-sm", onClick: prevImage, children: /* @__PURE__ */ jsx(ChevronLeft, { className: "h-4 w-4" }) }),
        /* @__PURE__ */ jsx(ModernButton, { variant: "ghost", size: "icon", className: "absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-background/80 backdrop-blur-sm", onClick: nextImage, children: /* @__PURE__ */ jsx(ChevronRight, { className: "h-4 w-4" }) })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity", children: [
        isImage && /* @__PURE__ */ jsx(ModernButton, { variant: "ghost", size: "icon", className: "bg-background/80 backdrop-blur-sm", onClick: () => handleAssetClick(currentAsset, selectedIndex), children: /* @__PURE__ */ jsx(ZoomIn, { className: "h-4 w-4" }) }),
        /* @__PURE__ */ jsx(ModernButton, { variant: "ghost", size: "icon", className: "bg-background/80 backdrop-blur-sm", onClick: () => window.open(currentAsset.url, "_blank"), children: /* @__PURE__ */ jsx(Download, { className: "h-4 w-4" }) })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "absolute bottom-2 left-2 flex gap-2", children: [
        /* @__PURE__ */ jsxs(Badge, { variant: "secondary", className: "bg-background/80 backdrop-blur-sm", children: [
          selectedIndex + 1,
          " / ",
          allAssets.length
        ] }),
        /* @__PURE__ */ jsx(Badge, { variant: "outline", className: "bg-background/80 backdrop-blur-sm", children: currentAsset.mimeType.split("/")[1].toUpperCase() })
      ] })
    ] }),
    allAssets.length > 1 && /* @__PURE__ */ jsx("div", { className: "flex gap-2 overflow-x-auto pb-2", children: allAssets.map((asset, index) => /* @__PURE__ */ jsx("button", { onClick: () => setSelectedIndex(index), className: cn("flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all", selectedIndex === index ? "border-primary shadow-md" : "border-border hover:border-border-strong"), children: asset.mimeType.startsWith("image/") ? /* @__PURE__ */ jsx("img", { src: asset.url || "/placeholder.svg", alt: asset.fileName, className: "w-full h-full object-cover", loading: "lazy" }) : /* @__PURE__ */ jsx("div", { className: "w-full h-full flex items-center justify-center bg-muted/50", children: /* @__PURE__ */ jsx(FileText, { className: "h-6 w-6 text-muted-foreground" }) }) }, asset.id)) }),
    /* @__PURE__ */ jsx(Dialog, { open: isLightboxOpen, onOpenChange: setIsLightboxOpen, children: /* @__PURE__ */ jsx(DialogContent, { className: "max-w-7xl max-h-[95vh] p-0 bg-black/95", children: /* @__PURE__ */ jsxs("div", { className: "relative w-full h-[90vh] flex items-center justify-center", children: [
      /* @__PURE__ */ jsx("img", { src: selectedAsset?.url || "/placeholder.svg", alt: selectedAsset?.fileName || "image", className: "max-w-full max-h-full object-contain" }),
      /* @__PURE__ */ jsx(ModernButton, { variant: "ghost", size: "icon", className: "absolute top-4 right-4 text-white hover:bg-white/20", onClick: () => setIsLightboxOpen(false), children: /* @__PURE__ */ jsx(X, { className: "h-6 w-6" }) }),
      allAssets.length > 1 && /* @__PURE__ */ jsxs(Fragment, { children: [
        /* @__PURE__ */ jsx(ModernButton, { variant: "ghost", size: "icon", className: "absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20", onClick: prevImage, children: /* @__PURE__ */ jsx(ChevronLeft, { className: "h-6 w-6" }) }),
        /* @__PURE__ */ jsx(ModernButton, { variant: "ghost", size: "icon", className: "absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20", onClick: nextImage, children: /* @__PURE__ */ jsx(ChevronRight, { className: "h-6 w-6" }) })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "absolute bottom-4 left-1/2 -translate-x-1/2 flex items-center gap-4 bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2 text-white", children: [
        /* @__PURE__ */ jsx("span", { className: "text-sm", children: selectedAsset?.fileName }),
        /* @__PURE__ */ jsxs(Badge, { variant: "outline", className: "border-white/30 text-white", children: [
          selectedIndex + 1,
          " / ",
          allAssets.length
        ] }),
        selectedAsset?.fileSize && /* @__PURE__ */ jsx("span", { className: "text-sm opacity-70", children: formatFileSize(selectedAsset.fileSize) })
      ] })
    ] }) }) }),
    /* @__PURE__ */ jsx(Dialog, { open: isPDFViewerOpen, onOpenChange: setIsPDFViewerOpen, children: /* @__PURE__ */ jsxs(DialogContent, { className: "max-w-6xl max-h-[95vh]", children: [
      /* @__PURE__ */ jsx(DialogHeader, { children: /* @__PURE__ */ jsxs(DialogTitle, { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ jsx("span", { children: selectedAsset?.fileName }),
        /* @__PURE__ */ jsxs("div", { className: "flex gap-2", children: [
          /* @__PURE__ */ jsxs(ModernButton, { variant: "outline", size: "sm", onClick: () => window.open(selectedAsset?.url || "", "_blank"), children: [
            " ",
            /* @__PURE__ */ jsx(Download, { className: "h-4 w-4 mr-2" }),
            " Скачать "
          ] }),
          /* @__PURE__ */ jsxs(ModernButton, { variant: "outline", size: "sm", onClick: () => window.print(), children: [
            " ",
            /* @__PURE__ */ jsx(FileText, { className: "h-4 w-4 mr-2" }),
            " Печать "
          ] })
        ] })
      ] }) }),
      /* @__PURE__ */ jsx("div", { className: "w-full h-[70vh] border rounded-lg overflow-hidden", children: /* @__PURE__ */ jsx("iframe", { src: selectedAsset?.url, className: "w-full h-full", title: selectedAsset?.fileName || "pdf" }) })
    ] }) })
  ] });
}

function MediaThumbnail({ mediaAsset, className, size = "md", showBadge = true }) {
  const sizeClasses = { sm: "w-12 h-12", md: "w-16 h-16", lg: "w-24 h-24" };
  if (!mediaAsset) {
    return /* @__PURE__ */ jsx("div", { className: cn("flex items-center justify-center bg-muted/30 rounded-lg border border-border", sizeClasses[size], className), children: /* @__PURE__ */ jsx(ImageIcon, { className: "h-1/2 w-1/2 text-muted-foreground" }) });
  }
  const isImage = mediaAsset.mimeType.startsWith("image/");
  const isPDF = mediaAsset.mimeType === "application/pdf";
  return /* @__PURE__ */ jsxs("div", { className: cn("relative", className), children: [
    /* @__PURE__ */ jsx("div", { className: cn("rounded-lg overflow-hidden border border-border-strong", sizeClasses[size]), children: isImage ? /* @__PURE__ */ jsx("img", { src: mediaAsset.url || "/placeholder.svg", alt: mediaAsset.fileName, className: "w-full h-full object-cover", loading: "lazy" }) : /* @__PURE__ */ jsx("div", { className: "w-full h-full flex items-center justify-center bg-muted/50", children: /* @__PURE__ */ jsx(FileText, { className: "h-1/2 w-1/2 text-muted-foreground" }) }) }),
    showBadge && /* @__PURE__ */ jsx(Badge, { variant: "secondary", className: "absolute -top-1 -right-1 text-xs h-5 px-1", children: isPDF ? "PDF" : mediaAsset.mimeType.split("/")[1].toUpperCase() })
  ] });
}

function buildCatalogItemWhere(filters) {
  const where = {};
  if (filters.query && filters.query.trim()) {
    const q = filters.query.trim();
    where.OR = [
      { sku: { contains: q, mode: "insensitive" } },
      { description: { contains: q, mode: "insensitive" } }
    ];
  }
  if (filters.brandIds && filters.brandIds.length > 0) {
    where.brandId = { in: filters.brandIds };
  }
  if (filters.isOemOnly) {
    where.brand = { isOem: true };
  }
  if (filters.categoryIds && filters.categoryIds.length > 0) {
    where.applicabilities = {
      some: { part: { partCategoryId: { in: filters.categoryIds } } }
    };
  }
  if (filters.accuracyLevels && filters.accuracyLevels.length > 0) {
    where.applicabilities = {
      ...where.applicabilities || {},
      some: {
        ...where.applicabilities?.some || {},
        accuracy: { in: filters.accuracyLevels }
      }
    };
  }
  if (filters.attributeFilters && Object.keys(filters.attributeFilters).length > 0) {
    const andConditions = [];
    for (const [templateIdStr, filter] of Object.entries(filters.attributeFilters)) {
      const templateId = Number.parseInt(templateIdStr);
      const cond = { templateId };
      if (filter.values && filter.values.length > 0) {
        cond.value = { in: filter.values };
      }
      if (filter.numericRange && filter.numericRange.length === 2) {
        const [min, max] = filter.numericRange;
        cond.numericValue = { gte: min, lte: max };
      }
      andConditions.push(cond);
    }
    if (andConditions.length > 0) {
      where.attributes = { some: { AND: andConditions } };
    }
  }
  return where;
}
function useCatalogSearch() {
  const [filters, setFilters] = useState({
    query: "",
    categoryIds: [],
    brandIds: [],
    attributeFilters: {},
    accuracyLevels: [],
    isOemOnly: false
  });
  useMemo(() => buildCatalogItemWhere(filters), [filters]);
  const { data: searchResult, isLoading } = trpc.site.search.catalogItems.useQuery({
    search: filters.query || void 0,
    brandIds: filters.brandIds,
    categoryIds: filters.categoryIds,
    isOemOnly: filters.isOemOnly,
    accuracy: filters.accuracyLevels || void 0,
    attributeFilters: Object.entries(filters.attributeFilters || {}).map(([templateId, f]) => ({
      templateId: Number(templateId),
      values: f.values?.length ? f.values : void 0,
      minValue: f.numericRange?.[0],
      maxValue: f.numericRange?.[1]
    })),
    limit: 50,
    offset: 0,
    sortBy: "updatedAt",
    sortDir: "desc"
  });
  const results = useMemo(() => {
    if (!searchResult?.items) return [];
    const list = [];
    for (const ci of searchResult.items) {
      if (!ci.applicabilities) continue;
      for (const ap of ci.applicabilities) {
        list.push({
          id: ap.id,
          partId: ap.partId,
          catalogItemId: ap.catalogItemId,
          accuracy: ap.accuracy,
          notes: ap.notes,
          part: ap.part,
          catalogItem: {
            id: ci.id,
            sku: ci.sku,
            description: ci.description,
            brandId: ci.brandId,
            brand: ci.brand,
            isPublic: true,
            attributes: ci.attributes,
            image: ci.image ?? null,
            mediaAssets: ci.mediaAssets ?? []
          }
        });
      }
    }
    return list;
  }, [searchResult]);
  return {
    results,
    filters,
    setFilters,
    totalCount: searchResult?.total || 0,
    filteredCount: results.length,
    isLoading
  };
}

const mockCategories = [
  { id: 1, name: "Сальники двигателя", slug: "engine-seals", level: 1, path: "01/01" },
  { id: 2, name: "Сальники трансмиссии", slug: "transmission-seals", level: 1, path: "01/02" },
  { id: 3, name: "Уплотнения гидравлики", slug: "hydraulic-seals", level: 1, path: "01/03" }
];
const mockBrands = [
  { id: 1, name: "SKF", slug: "skf", isOem: false },
  { id: 2, name: "Freudenberg", slug: "freudenberg", isOem: false },
  { id: 3, name: "Caterpillar", slug: "caterpillar", isOem: true },
  { id: 4, name: "Komatsu", slug: "komatsu", isOem: true }
];
const mockAttributeTemplates = [
  { id: 1, name: "inner_diameter", title: "Внутренний диаметр", dataType: "NUMBER", unit: "MM", isRequired: true, allowedValues: [], tolerance: 0.1 },
  { id: 2, name: "outer_diameter", title: "Наружный диаметр", dataType: "NUMBER", unit: "MM", isRequired: true, allowedValues: [], tolerance: 0.1 },
  { id: 3, name: "material", title: "Материал", dataType: "STRING", isRequired: true, allowedValues: ["NBR", "FKM", "PTFE", "EPDM"] }
];
function ModernCatalogSearch() {
  const { results, filters, setFilters, totalCount, filteredCount } = useCatalogSearch();
  const [viewMode, setViewMode] = useState("detailed");
  const [selectedItems, setSelectedItems] = useState([]);
  const [selectedApplicability, setSelectedApplicability] = useState(null);
  const [isAIAssistantOpen, setIsAIAssistantOpen] = useState(false);
  const availableAttributeValues = useMemo(() => {
    const values = {};
    const numericStats = {};
    results.forEach((applicability) => {
      applicability.catalogItem.attributes.forEach((attr) => {
        if (attr.template.dataType === "NUMBER" && attr.numericValue !== void 0 && attr.numericValue !== null) {
          if (!numericStats[attr.templateId]) {
            numericStats[attr.templateId] = { min: attr.numericValue, max: attr.numericValue, avg: 0 };
          } else {
            numericStats[attr.templateId].min = Math.min(numericStats[attr.templateId].min, attr.numericValue);
            numericStats[attr.templateId].max = Math.max(numericStats[attr.templateId].max, attr.numericValue);
          }
        } else {
          if (!values[attr.templateId]) values[attr.templateId] = [];
          if (!values[attr.templateId].includes(attr.value)) values[attr.templateId].push(attr.value);
        }
      });
    });
    Object.keys(numericStats).forEach((templateId) => {
      const templateIdNum = Number.parseInt(templateId);
      const allValues = results.flatMap((r) => r.catalogItem.attributes).filter((attr) => attr.templateId === templateIdNum && attr.numericValue !== void 0 && attr.numericValue !== null).map((attr) => attr.numericValue);
      if (allValues.length > 0) {
        numericStats[templateIdNum].avg = allValues.reduce((sum, val) => sum + val, 0) / allValues.length;
      }
    });
    return { values, numericStats };
  }, [results]);
  const clearAllFilters = () => {
    setFilters({
      query: "",
      categoryIds: [],
      brandIds: [],
      attributeFilters: {},
      accuracyLevels: [],
      isOemOnly: false
    });
  };
  const activeFiltersCount = filters.categoryIds.length + filters.brandIds.length + Object.keys(filters.attributeFilters).length + filters.accuracyLevels.length + (filters.isOemOnly ? 1 : 0);
  const handleAIApplyFilters = (aiFilters) => {
    setFilters({ ...filters, ...aiFilters });
  };
  const handleAIUpdateQuery = (query) => setFilters({ ...filters, query });
  return /* @__PURE__ */ jsxs("div", { className: "min-h-screen bg-background", children: [
    /* @__PURE__ */ jsx(
      ProfessionalHeader,
      {
        totalCount,
        filteredCount,
        searchQuery: filters.query,
        onSearchChange: (query) => setFilters({ ...filters, query }),
        onOpenAI: () => setIsAIAssistantOpen(true)
      }
    ),
    /* @__PURE__ */ jsxs("div", { className: "flex", children: [
      /* @__PURE__ */ jsx(
        ModernFiltersPanel,
        {
          filters,
          setFilters,
          activeFiltersCount,
          onClearAll: clearAllFilters,
          mockCategories,
          mockBrands,
          mockAttributeTemplates,
          availableAttributeValues
        }
      ),
      /* @__PURE__ */ jsx("div", { className: "flex-1 overflow-y-auto", children: /* @__PURE__ */ jsxs("div", { className: "container max-w-none p-4", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between mb-4", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-3", children: [
            /* @__PURE__ */ jsx("h2", { className: "text-xl font-bold tracking-tight", children: "Результаты поиска" }),
            /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
              /* @__PURE__ */ jsxs(Badge, { variant: "secondary", className: "px-2 py-1 font-medium text-xs", children: [
                /* @__PURE__ */ jsx(Package, { className: "h-3 w-3 mr-1" }),
                filteredCount,
                " позиций"
              ] }),
              filteredCount !== totalCount && /* @__PURE__ */ jsxs(Badge, { variant: "outline", className: "px-2 py-1 text-xs", children: [
                "из ",
                totalCount,
                " общих"
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-1 p-1 bg-muted/50 rounded border border-border/40", children: [
              /* @__PURE__ */ jsx(ModernButton, { variant: viewMode === "detailed" ? "default" : "ghost", size: "sm", onClick: () => setViewMode("detailed"), className: "h-7 w-7 p-0", children: /* @__PURE__ */ jsx(List, { className: "h-3 w-3" }) }),
              /* @__PURE__ */ jsx(ModernButton, { variant: viewMode === "grid" ? "default" : "ghost", size: "sm", onClick: () => setViewMode("grid"), className: "h-7 w-7 p-0", children: /* @__PURE__ */ jsx(Grid3X3, { className: "h-3 w-3" }) }),
              /* @__PURE__ */ jsx(ModernButton, { variant: viewMode === "table" ? "default" : "ghost", size: "sm", onClick: () => setViewMode("table"), className: "h-7 w-7 p-0", children: /* @__PURE__ */ jsx(BarChart3, { className: "h-3 w-3" }) })
            ] }),
            /* @__PURE__ */ jsxs(ModernButton, { variant: "outline", size: "sm", className: "gap-1 h-7 px-2 text-xs", children: [
              /* @__PURE__ */ jsx(ArrowLeftRight, { className: "h-3 w-3" }),
              "Сравнить",
              selectedItems.length > 0 && /* @__PURE__ */ jsx(Badge, { variant: "secondary", className: "ml-1 h-4 w-4 rounded-full p-0 text-xs", children: selectedItems.length })
            ] })
          ] })
        ] }),
        /* @__PURE__ */ jsx(AISearchSuggestions, { query: filters.query, resultsCount: filteredCount, onApplyFilters: handleAIApplyFilters, onUpdateQuery: handleAIUpdateQuery }),
        results.length === 0 ? /* @__PURE__ */ jsx(ModernCard, { variant: "elevated", className: "text-center py-12 border-2 border-dashed border-border-strong", children: /* @__PURE__ */ jsx(ModernCardContent, { children: /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center gap-3", children: [
          /* @__PURE__ */ jsx("div", { className: "h-12 w-12 rounded-full bg-muted/50 flex items-center justify-center", children: /* @__PURE__ */ jsx(Package, { className: "h-6 w-6 text-muted-foreground" }) }),
          /* @__PURE__ */ jsxs("div", { children: [
            /* @__PURE__ */ jsx("h3", { className: "text-lg font-semibold mb-1", children: "Ничего не найдено" }),
            /* @__PURE__ */ jsx("p", { className: "text-muted-foreground text-sm max-w-md", children: "Попробуйте изменить критерии поиска или воспользуйтесь AI ассистентом для помощи" })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "flex gap-2", children: [
            /* @__PURE__ */ jsx(ModernButton, { variant: "outline", onClick: clearAllFilters, size: "sm", children: "Очистить фильтры" }),
            /* @__PURE__ */ jsx(ModernButton, { variant: "gradient", onClick: () => setIsAIAssistantOpen(true), size: "sm", children: "Спросить AI" })
          ] })
        ] }) }) }) : /* @__PURE__ */ jsx("div", { className: "space-y-3 animate-fade-in", children: results.map((applicability, index) => /* @__PURE__ */ jsx(ModernCard, { variant: "elevated", className: "group hover:shadow-strong transition-all duration-200 animate-slide-up border hover:border-primary/20", style: { animationDelay: `${index * 30}ms` }, children: /* @__PURE__ */ jsx(ModernCardContent, { className: "p-4", children: /* @__PURE__ */ jsxs("div", { className: "flex gap-4", children: [
          /* @__PURE__ */ jsx("div", { className: "flex-shrink-0", children: /* @__PURE__ */ jsx(MediaThumbnail, { mediaAsset: applicability.catalogItem.image ?? void 0, size: "md", className: "cursor-pointer hover:scale-105 transition-transform" }) }),
          /* @__PURE__ */ jsxs("div", { className: "flex-1 min-w-0", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex items-start justify-between mb-3", children: [
              /* @__PURE__ */ jsxs("div", { className: "flex-1 min-w-0", children: [
                /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-3 mb-2", children: [
                  /* @__PURE__ */ jsx(
                    Checkbox,
                    {
                      checked: selectedItems.includes(applicability.id),
                      onCheckedChange: (checked) => {
                        if (checked) setSelectedItems([...selectedItems, applicability.id]);
                        else setSelectedItems(selectedItems.filter((id) => id !== applicability.id));
                      },
                      className: "data-[state=checked]:bg-primary data-[state=checked]:border-primary h-4 w-4"
                    }
                  ),
                  /* @__PURE__ */ jsxs("div", { className: "min-w-0 flex-1", children: [
                    /* @__PURE__ */ jsx("h3", { className: "text-lg font-bold text-primary font-mono tracking-tight truncate", children: applicability.catalogItem.sku }),
                    /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2 mt-1", children: [
                      /* @__PURE__ */ jsxs(Badge, { variant: "outline", className: "font-medium text-xs h-5", children: [
                        /* @__PURE__ */ jsx(Building, { className: "h-3 w-3 mr-1" }),
                        applicability.catalogItem.brand.name
                      ] }),
                      applicability.catalogItem.brand.isOem && /* @__PURE__ */ jsx(Badge, { variant: "secondary", className: "text-xs h-5", children: "OEM" }),
                      applicability.catalogItem.mediaAssets.length > 0 && /* @__PURE__ */ jsxs(Badge, { variant: "outline", className: "text-xs h-5", children: [
                        /* @__PURE__ */ jsx(ImageIcon, { className: "h-3 w-3 mr-1" }),
                        applicability.catalogItem.mediaAssets.length
                      ] })
                    ] })
                  ] })
                ] }),
                /* @__PURE__ */ jsxs("div", { className: "space-y-1 mb-3", children: [
                  /* @__PURE__ */ jsx("h4", { className: "font-semibold text-sm", children: applicability.part.name }),
                  /* @__PURE__ */ jsx("p", { className: "text-muted-foreground text-sm leading-relaxed line-clamp-2", children: applicability.catalogItem.description })
                ] }),
                applicability.notes && /* @__PURE__ */ jsx("div", { className: "p-2 rounded bg-info/10 border border-info/20 mb-3", children: /* @__PURE__ */ jsxs("div", { className: "flex items-start gap-2", children: [
                  /* @__PURE__ */ jsx("div", { className: "h-4 w-4 rounded-full bg-info/20 flex items-center justify-center flex-shrink-0 mt-0.5", children: /* @__PURE__ */ jsx("div", { className: "h-1.5 w-1.5 rounded-full bg-info" }) }),
                  /* @__PURE__ */ jsx("p", { className: "text-xs text-info-foreground leading-relaxed", children: applicability.notes })
                ] }) })
              ] }),
              /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-end gap-2 ml-4", children: [
                /* @__PURE__ */ jsx(StatusBadge, { status: applicability.accuracy, size: "sm" }),
                /* @__PURE__ */ jsx(Badge, { variant: "outline", className: "text-xs", children: applicability.part.partCategory.name })
              ] })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "flex flex-wrap gap-1 mb-3", children: [
              applicability.catalogItem.attributes.slice(0, 6).map((attr) => /* @__PURE__ */ jsxs("div", { className: "inline-flex items-center gap-1 px-2 py-1 rounded-full bg-card border border-border-strong hover:border-primary/40 transition-colors text-xs", children: [
                /* @__PURE__ */ jsxs("span", { className: "text-muted-foreground font-medium truncate max-w-[80px]", title: attr.template.title, children: [
                  attr.template.title.split(" ")[0],
                  ":"
                ] }),
                /* @__PURE__ */ jsx("span", { className: "font-mono font-semibold", children: attr.value }),
                attr.template.unit && /* @__PURE__ */ jsx("span", { className: "text-muted-foreground", children: attr.template.unit })
              ] }, attr.id)),
              applicability.catalogItem.attributes.length > 6 && /* @__PURE__ */ jsxs("div", { className: "inline-flex items-center px-2 py-1 rounded-full bg-muted/50 border border-border text-xs text-muted-foreground", children: [
                "+",
                applicability.catalogItem.attributes.length - 6,
                " еще"
              ] })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between pt-2 border-t border-border", children: [
              /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
                /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-1 text-xs text-muted-foreground", children: [
                  /* @__PURE__ */ jsx(Clock, { className: "h-3 w-3" }),
                  /* @__PURE__ */ jsx("span", { children: new Date(applicability.part.updatedAt).toLocaleDateString("ru-RU") })
                ] }),
                /* @__PURE__ */ jsx(Separator, { orientation: "vertical", className: "h-3" }),
                /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-1", children: [
                  /* @__PURE__ */ jsxs(Badge, { variant: "outline", className: "text-xs font-mono h-4 px-1", children: [
                    "#",
                    applicability.partId
                  ] }),
                  /* @__PURE__ */ jsxs(Badge, { variant: "outline", className: "text-xs font-mono h-4 px-1", children: [
                    "#",
                    applicability.catalogItemId
                  ] })
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-1", children: [
                /* @__PURE__ */ jsx(Dialog, { children: /* @__PURE__ */ jsx(DialogTrigger, { asChild: true, children: /* @__PURE__ */ jsxs(ModernButton, { variant: "outline", size: "sm", onClick: () => setSelectedApplicibility(applicability), className: "gap-1 h-7 px-2 text-xs", children: [
                  /* @__PURE__ */ jsx(Eye, { className: "h-3 w-3" }),
                  "Подробности"
                ] }) }) }),
                /* @__PURE__ */ jsxs(ModernButton, { variant: "outline", size: "sm", className: "gap-1 h-7 px-2 text-xs", children: [
                  /* @__PURE__ */ jsx(FileText, { className: "h-3 w-3" }),
                  "Техданные"
                ] }),
                /* @__PURE__ */ jsxs(ModernButton, { variant: "gradient", size: "sm", className: "gap-1 h-7 px-2 text-xs", children: [
                  /* @__PURE__ */ jsx(TrendingUp, { className: "h-3 w-3" }),
                  "В корзину"
                ] })
              ] })
            ] })
          ] })
        ] }) }) }, applicability.id)) })
      ] }) })
    ] }),
    /* @__PURE__ */ jsx(AIAssistantDrawer, { isOpen: isAIAssistantOpen, onToggle: () => setIsAIAssistantOpen(!isAIAssistantOpen), onApplyFilters: handleAIApplyFilters, currentFilters: filters, resultsCount: filteredCount }),
    selectedApplicability && /* @__PURE__ */ jsx(Dialog, { open: !!selectedApplicability, onOpenChange: () => setSelectedApplicability(null), children: /* @__PURE__ */ jsxs(DialogContent, { className: "max-w-6xl max-h-[90vh] overflow-y-auto bg-card border-border/40", children: [
      /* @__PURE__ */ jsxs(DialogHeader, { children: [
        /* @__PURE__ */ jsxs(DialogTitle, { className: "text-xl font-bold flex items-center justify-between", children: [
          /* @__PURE__ */ jsx("span", { className: "font-mono", children: selectedApplicability.catalogItem.sku }),
          /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
            /* @__PURE__ */ jsx(Badge, { variant: "outline", children: selectedApplicability.catalogItem.brand.name }),
            /* @__PURE__ */ jsx(StatusBadge, { status: selectedApplicability.accuracy })
          ] })
        ] }),
        /* @__PURE__ */ jsx("p", { className: "text-muted-foreground leading-relaxed", children: selectedApplicability.catalogItem.description })
      ] }),
      /* @__PURE__ */ jsxs(Tabs, { defaultValue: "overview", className: "mt-4", children: [
        /* @__PURE__ */ jsxs(TabsList, { className: "grid w-full grid-cols-5 bg-muted/50", children: [
          /* @__PURE__ */ jsx(TabsTrigger, { value: "overview", children: "Обзор" }),
          /* @__PURE__ */ jsx(TabsTrigger, { value: "media", children: "Медиа" }),
          /* @__PURE__ */ jsx(TabsTrigger, { value: "part", children: "Эталонная группа" }),
          /* @__PURE__ */ jsx(TabsTrigger, { value: "attributes", children: "Атрибуты" }),
          /* @__PURE__ */ jsx(TabsTrigger, { value: "applicability", children: "Применимость" })
        ] }),
        /* @__PURE__ */ jsxs(TabsContent, { value: "overview", className: "space-y-4 mt-4", children: [
          /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-4", children: [
            /* @__PURE__ */ jsxs("div", { children: [
              /* @__PURE__ */ jsx("h3", { className: "font-semibold mb-3", children: "Изображения и документы" }),
              /* @__PURE__ */ jsx(MediaGallery, { mediaAssets: selectedApplicability.catalogItem.mediaAssets, primaryImage: selectedApplicability.catalogItem.image ?? null, title: selectedApplicability.catalogItem.sku })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
              /* @__PURE__ */ jsxs(ModernCard, { variant: "glass", children: [
                /* @__PURE__ */ jsx(ModernCardHeader, { children: /* @__PURE__ */ jsx(ModernCardTitle, { className: "text-sm", children: "Информация о товаре" }) }),
                /* @__PURE__ */ jsx(ModernCardContent, { className: "space-y-3", children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 gap-3", children: [
                  /* @__PURE__ */ jsxs("div", { children: [
                    /* @__PURE__ */ jsx("span", { className: "text-xs text-muted-foreground", children: "Артикул" }),
                    /* @__PURE__ */ jsx("div", { className: "font-mono font-semibold text-sm", children: selectedApplicability.catalogItem.sku })
                  ] }),
                  /* @__PURE__ */ jsxs("div", { children: [
                    /* @__PURE__ */ jsx("span", { className: "text-xs text-muted-foreground", children: "Бренд" }),
                    /* @__PURE__ */ jsx("div", { className: "font-semibold text-sm", children: selectedApplicability.catalogItem.brand.name })
                  ] }),
                  /* @__PURE__ */ jsxs("div", { children: [
                    /* @__PURE__ */ jsx("span", { className: "text-xs text-muted-foreground", children: "Страна" }),
                    /* @__PURE__ */ jsx("div", { className: "font-semibold text-sm", children: selectedApplicability.catalogItem.brand.country || "Не указана" })
                  ] }),
                  /* @__PURE__ */ jsxs("div", { children: [
                    /* @__PURE__ */ jsx("span", { className: "text-xs text-muted-foreground", children: "Тип" }),
                    /* @__PURE__ */ jsx("div", { className: "font-semibold text-sm", children: selectedApplicability.catalogItem.brand.isOem ? "OEM" : "Aftermarket" })
                  ] })
                ] }) })
              ] }),
              /* @__PURE__ */ jsxs(ModernCard, { variant: "glass", children: [
                /* @__PURE__ */ jsx(ModernCardHeader, { children: /* @__PURE__ */ jsx(ModernCardTitle, { className: "text-sm", children: "Эталонная группа" }) }),
                /* @__PURE__ */ jsxs(ModernCardContent, { className: "space-y-2", children: [
                  /* @__PURE__ */ jsxs("div", { children: [
                    /* @__PURE__ */ jsx("span", { className: "text-xs text-muted-foreground", children: "Название группы" }),
                    /* @__PURE__ */ jsx("div", { className: "font-semibold text-sm", children: selectedApplicability.part.name || "Без названия" })
                  ] }),
                  /* @__PURE__ */ jsxs("div", { children: [
                    /* @__PURE__ */ jsx("span", { className: "text-xs text-muted-foreground", children: "Категория" }),
                    /* @__PURE__ */ jsx("div", { className: "font-semibold text-sm", children: selectedApplicability.part.partCategory.name })
                  ] }),
                  /* @__PURE__ */ jsxs("div", { children: [
                    /* @__PURE__ */ jsx("span", { className: "text-xs text-muted-foreground", children: "Путь в иерархии" }),
                    /* @__PURE__ */ jsx("div", { className: "font-mono text-xs", children: selectedApplicability.part.path })
                  ] })
                ] })
              ] })
            ] })
          ] }),
          selectedApplicability.notes && /* @__PURE__ */ jsxs(ModernCard, { variant: "glass", className: "border-info/20 bg-info/5", children: [
            /* @__PURE__ */ jsx(ModernCardHeader, { children: /* @__PURE__ */ jsxs(ModernCardTitle, { className: "text-info flex items-center gap-2 text-sm", children: [
              /* @__PURE__ */ jsx("div", { className: "h-2 w-2 rounded-full bg-info" }),
              "Примечания по применимости"
            ] }) }),
            /* @__PURE__ */ jsx(ModernCardContent, { children: /* @__PURE__ */ jsx("p", { className: "leading-relaxed text-sm", children: selectedApplicability.notes }) })
          ] })
        ] }),
        /* @__PURE__ */ jsx(TabsContent, { value: "media", className: "space-y-4 mt-4", children: /* @__PURE__ */ jsx(MediaGallery, { mediaAssets: selectedApplicability.catalogItem.mediaAssets, primaryImage: selectedApplicability.catalogItem.image ?? null, title: selectedApplicability.catalogItem.sku }) }),
        /* @__PURE__ */ jsx(TabsContent, { value: "part", className: "space-y-4 mt-4", children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-4", children: [
          /* @__PURE__ */ jsxs("div", { children: [
            /* @__PURE__ */ jsx("h3", { className: "font-semibold mb-3 text-sm", children: "Медиа эталонной группы" }),
            /* @__PURE__ */ jsx(MediaGallery, { mediaAssets: selectedApplicability.part.mediaAssets, primaryImage: selectedApplicability.part.image ?? null, title: selectedApplicability.part.name || "Эталонная группа" })
          ] }),
          /* @__PURE__ */ jsxs(ModernCard, { variant: "glass", children: [
            /* @__PURE__ */ jsx(ModernCardHeader, { children: /* @__PURE__ */ jsx(ModernCardTitle, { className: "text-sm", children: "Эталонные атрибуты группы" }) }),
            /* @__PURE__ */ jsx(ModernCardContent, { children: /* @__PURE__ */ jsx("div", { className: "space-y-2", children: selectedApplicability.part.attributes.map((attr) => /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between p-2 rounded bg-card border border-border-strong hover:border-primary/40 transition-colors", children: [
              /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
                /* @__PURE__ */ jsx("span", { className: "font-medium text-sm", children: attr.template.title }),
                attr.template.unit && /* @__PURE__ */ jsx(Badge, { variant: "outline", className: "text-xs h-4", children: attr.template.unit })
              ] }),
              /* @__PURE__ */ jsx("div", { className: "font-mono font-bold text-sm", children: attr.value })
            ] }, attr.id)) }) })
          ] })
        ] }) }),
        /* @__PURE__ */ jsx(TabsContent, { value: "attributes", className: "space-y-4 mt-4", children: /* @__PURE__ */ jsxs(ModernCard, { variant: "glass", children: [
          /* @__PURE__ */ jsx(ModernCardHeader, { children: /* @__PURE__ */ jsx(ModernCardTitle, { className: "text-sm", children: "Атрибуты каталожной позиции" }) }),
          /* @__PURE__ */ jsx(ModernCardContent, { children: /* @__PURE__ */ jsx("div", { className: "space-y-2", children: selectedApplicability.catalogItem.attributes.map((attr) => /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between p-2 rounded bg-card border border-border-strong hover:border-primary/40 transition-colors", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
              /* @__PURE__ */ jsx("span", { className: "font-medium text-sm", children: attr.template.title }),
              attr.template.unit && /* @__PURE__ */ jsx(Badge, { variant: "outline", className: "text-xs h-4", children: attr.template.unit })
            ] }),
            /* @__PURE__ */ jsx("div", { className: "font-mono font-bold text-sm", children: attr.value })
          ] }, attr.id)) }) })
        ] }) }),
        /* @__PURE__ */ jsx(TabsContent, { value: "applicability", className: "space-y-4 mt-4", children: /* @__PURE__ */ jsxs(ModernCard, { variant: "glass", children: [
          /* @__PURE__ */ jsx(ModernCardHeader, { children: /* @__PURE__ */ jsx(ModernCardTitle, { className: "text-sm", children: "Детали применимости" }) }),
          /* @__PURE__ */ jsxs(ModernCardContent, { className: "space-y-3", children: [
            /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 gap-4", children: [
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("span", { className: "text-xs text-muted-foreground", children: "Точность совпадения" }),
                /* @__PURE__ */ jsx("div", { className: "mt-1", children: /* @__PURE__ */ jsx(StatusBadge, { status: selectedApplicability.accuracy }) })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("span", { className: "text-xs text-muted-foreground", children: "ID применимости" }),
                /* @__PURE__ */ jsxs("div", { className: "font-mono font-semibold text-sm", children: [
                  "#",
                  selectedApplicability.id
                ] })
              ] })
            ] }),
            selectedApplicability.notes && /* @__PURE__ */ jsxs("div", { className: "p-3 rounded bg-info/10 border border-info/20", children: [
              /* @__PURE__ */ jsx("h4", { className: "font-medium text-sm mb-2", children: "Примечания:" }),
              /* @__PURE__ */ jsx("p", { className: "text-sm leading-relaxed", children: selectedApplicability.notes })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 gap-4 pt-3 border-t border-border", children: [
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("span", { className: "text-xs text-muted-foreground", children: "Создано" }),
                /* @__PURE__ */ jsx("div", { className: "text-sm", children: new Date(selectedApplicability.part.createdAt).toLocaleString("ru-RU") })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("span", { className: "text-xs text-muted-foreground", children: "Обновлено" }),
                /* @__PURE__ */ jsx("div", { className: "text-sm", children: new Date(selectedApplicability.part.updatedAt).toLocaleString("ru-RU") })
              ] })
            ] })
          ] })
        ] }) })
      ] })
    ] }) })
  ] });
}

function ModernCatalogSearchIsland() {
  return /* @__PURE__ */ jsx(TrpcProvider, { children: /* @__PURE__ */ jsx(ModernCatalogSearch, {}) });
}

const $$CatalogPro = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "\u041A\u0430\u0442\u0430\u043B\u043E\u0433 (\u043F\u0440\u043E\u0444\u0435\u0441\u0441\u0438\u043E\u043D\u0430\u043B\u044C\u043D\u044B\u0439 \u043F\u0440\u043E\u0442\u043E\u0442\u0438\u043F)" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "ModernCatalogSearchIsland", ModernCatalogSearchIsland, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/catalog/ModernCatalogSearchIsland", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/parttec/site/src/pages/catalog-pro.astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/catalog-pro.astro";
const $$url = "/catalog-pro";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$CatalogPro,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
