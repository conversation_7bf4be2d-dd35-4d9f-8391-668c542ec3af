import{j as e}from"./jsx-runtime.D_zvdyIk.js";import{r as a}from"./index.GzzYzsTh.js";import{a as j}from"./auth-client.D86yjSFk.js";import{I as t}from"./input.BvZ2Z-in.js";import{B as f}from"./button.CYGhVy7y.js";import"./utils.CBfrqCZ4.js";import"./index.3rXK4OIH.js";function E(){const[r,u]=a.useState(""),[l,d]=a.useState(""),[o,p]=a.useState(""),[n,i]=a.useState(!1),[m,c]=a.useState(null),x=async s=>{s.preventDefault(),c(null),i(!0);try{await j.signUp.email({email:l,password:o,name:r},{onError:h=>c(h.error.message??"Ошибка регистрации"),onSuccess:()=>{window.location.href="/account"}})}finally{i(!1)}};return e.jsxs("form",{className:"space-y-4",onSubmit:x,children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm",children:"Имя"}),e.jsx(t,{value:r,onChange:s=>u(s.target.value)})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm",children:"Email"}),e.jsx(t,{type:"email",value:l,onChange:s=>d(s.target.value),required:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm",children:"Пароль"}),e.jsx(t,{type:"password",value:o,onChange:s=>p(s.target.value),required:!0})]}),m&&e.jsx("div",{className:"text-sm text-red-500",children:m}),e.jsx(f,{type:"submit",disabled:n,children:n?"Регистрируем...":"Зарегистрироваться"})]})}export{E as default};
