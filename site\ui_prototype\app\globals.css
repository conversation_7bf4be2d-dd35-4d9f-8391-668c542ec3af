@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light Theme - Professional & Strict */
    --background: 0 0% 100%; /* Pure white */
    --foreground: 240 10% 3.9%; /* Rich dark text */

    --card: 0 0% 99%; /* Slightly off-white cards */
    --card-foreground: 240 10% 3.9%; /* Dark text on cards */

    --popover: 0 0% 100%; /* White popover */
    --popover-foreground: 240 10% 3.9%; /* Dark popover text */

    --primary: 220 90% 45%; /* Professional dark blue */
    --primary-foreground: 0 0% 98%; /* White on primary */

    --secondary: 210 20% 95%; /* Light gray secondary */
    --secondary-foreground: 222 84% 4.9%; /* Dark text on secondary */

    --muted: 210 20% 96%; /* Light muted background */
    --muted-foreground: 215 16% 46.9%; /* Medium gray text */

    --accent: 210 20% 94%; /* Light accent */
    --accent-foreground: 222 84% 4.9%; /* Dark accent text */

    --destructive: 0 84% 60%; /* Error red */
    --destructive-foreground: 210 40% 98%; /* White on error */

    --border: 214 32% 88%; /* Professional borders */
    --input: 214 32% 94%; /* Light input background */
    --ring: 220 90% 45%; /* Focus ring */

    /* Enhanced semantic colors */
    --success: 142 76% 36%; /* Professional green */
    --success-foreground: 355 100% 97%; /* White on success */
    --warning: 38 92% 50%; /* Vibrant amber */
    --warning-foreground: 48 96% 89%; /* Light on warning */
    --info: 199 89% 48%; /* Bright cyan */
    --info-foreground: 210 40% 98%; /* White on info */

    /* AI specific colors */
    --ai-primary: 220 90% 45%; /* Same as primary for consistency */
    --ai-secondary: 220 90% 50%; /* Slightly lighter */
    --ai-accent: 220 90% 55%; /* Accent variant */

    --radius: 0.375rem; /* Reduced radius for more professional look */

    /* Professional surface colors */
    --surface: 0 0% 98%; /* Slightly off-white */
    --surface-hover: 210 20% 95%; /* Hover state */
    --border-strong: 214 32% 82%; /* Stronger borders */
  }

  .dark {
    /* Dark Theme - Professional & Strict */
    --background: 240 10% 3.9%; /* Rich dark background */
    --foreground: 0 0% 98%; /* Pure white text */

    --card: 240 10% 5%; /* Dark cards */
    --card-foreground: 0 0% 98%; /* White text on cards */

    --popover: 240 10% 5%; /* Dark popover */
    --popover-foreground: 0 0% 98%; /* White popover text */

    --primary: 217 91% 60%; /* Bright blue primary */
    --primary-foreground: 210 40% 98%; /* White on primary */

    --secondary: 240 3.7% 12%; /* Dark secondary */
    --secondary-foreground: 0 0% 98%; /* White on secondary */

    --muted: 240 3.7% 12%; /* Dark muted */
    --muted-foreground: 240 5% 64.9%; /* Light gray text */

    --accent: 240 3.7% 12%; /* Dark accent */
    --accent-foreground: 0 0% 98%; /* White accent text */

    --destructive: 0 62.8% 30.6%; /* Darker red for dark theme */
    --destructive-foreground: 0 0% 98%; /* White on error */

    --border: 240 3.7% 12%; /* Dark borders */
    --input: 240 3.7% 12%; /* Dark input background */
    --ring: 217 91% 60%; /* Bright focus ring */

    /* Enhanced semantic colors for dark theme */
    --success: 142 76% 36%; /* Professional green */
    --success-foreground: 0 0% 98%; /* White on success */
    --warning: 38 92% 50%; /* Vibrant amber */
    --warning-foreground: 48 96% 89%; /* Light on warning */
    --info: 199 89% 48%; /* Bright cyan */
    --info-foreground: 0 0% 98%; /* White on info */

    /* AI specific colors for dark theme */
    --ai-primary: 217 91% 60%; /* Same as primary */
    --ai-secondary: 217 91% 65%; /* Lighter variant */
    --ai-accent: 217 91% 70%; /* Accent variant */

    /* Professional surface colors */
    --surface: 240 10% 6%; /* Slightly lighter surface */
    --surface-hover: 240 3.7% 12%; /* Hover state */
    --border-strong: 240 3.7% 18%; /* Stronger borders */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color 0.2s ease, color 0.2s ease;
  }

  /* Professional scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/40 rounded-sm;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/60;
  }
}

@layer utilities {
  /* Surface utilities */
  .bg-surface {
    background-color: hsl(var(--surface));
  }

  .bg-surface-hover {
    background-color: hsl(var(--surface-hover));
  }

  .border-border-strong {
    border-color: hsl(var(--border-strong));
  }

  /* AI specific utilities */
  .bg-ai-primary {
    background-color: hsl(var(--ai-primary));
  }

  .bg-ai-secondary {
    background-color: hsl(var(--ai-secondary));
  }

  .text-ai-primary {
    color: hsl(var(--ai-primary));
  }

  .border-ai-primary {
    border-color: hsl(var(--ai-primary));
  }
}

@layer components {
  /* Professional glass effect */
  .glass-effect {
    @apply bg-card/95 backdrop-blur-sm border border-border-strong shadow-sm;
  }

  /* Professional gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
  }

  .gradient-ai {
    background: linear-gradient(135deg, hsl(var(--ai-primary)) 0%, hsl(var(--ai-secondary)) 100%);
  }

  .gradient-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--surface)) 100%);
  }

  /* Professional shadows - more subtle */
  .shadow-professional {
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05), 0 1px 3px 0 rgb(0 0 0 / 0.1);
  }

  .shadow-elevated {
    box-shadow: 0 2px 4px 0 rgb(0 0 0 / 0.06), 0 2px 8px 0 rgb(0 0 0 / 0.12);
  }

  .shadow-strong {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  /* Enhanced status indicators with professional colors */
  .status-exact {
    @apply bg-green-50 text-green-800 border-green-200 
           dark:bg-green-950/50 dark:text-green-200 dark:border-green-800/50;
  }

  .status-notes {
    @apply bg-blue-50 text-blue-800 border-blue-200 
           dark:bg-blue-950/50 dark:text-blue-200 dark:border-blue-800/50;
  }

  .status-modification {
    @apply bg-amber-50 text-amber-800 border-amber-200 
           dark:bg-amber-950/50 dark:text-amber-200 dark:border-amber-800/50;
  }

  .status-partial {
    @apply bg-orange-50 text-orange-800 border-orange-200 
           dark:bg-orange-950/50 dark:text-orange-200 dark:border-orange-800/50;
  }

  /* Subtle professional animations */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.2s ease-out;
  }

  .animate-theme-transition {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(10px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Professional focus states */
  .focus-enhanced:focus-visible {
    @apply outline-none ring-1 ring-ring ring-offset-1 ring-offset-background;
  }

  /* AI specific styles - more professional */
  .ai-message-user {
    @apply bg-primary text-primary-foreground;
  }

  .ai-message-assistant {
    @apply bg-surface border border-border text-foreground;
  }

  .ai-suggestion {
    @apply bg-primary/5 border-primary/20 text-primary hover:bg-primary/10 transition-colors;
  }
}
