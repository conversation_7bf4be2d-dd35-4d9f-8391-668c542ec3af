import 'kleur/colors';
import { p as decodeKey } from './chunks/astro/server_D7mwM5eH.mjs';
import 'clsx';
import 'cookie';
import './chunks/astro-designed-error-pages_NtkQcIoZ.mjs';
import 'es-module-lexer';
import { N as NOOP_MIDDLEWARE_FN } from './chunks/noop-middleware_CzuyjZdh.mjs';

function sanitizeParams(params) {
  return Object.fromEntries(
    Object.entries(params).map(([key, value]) => {
      if (typeof value === "string") {
        return [key, value.normalize().replace(/#/g, "%23").replace(/\?/g, "%3F")];
      }
      return [key, value];
    })
  );
}
function getParameter(part, params) {
  if (part.spread) {
    return params[part.content.slice(3)] || "";
  }
  if (part.dynamic) {
    if (!params[part.content]) {
      throw new TypeError(`Missing parameter: ${part.content}`);
    }
    return params[part.content];
  }
  return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]");
}
function getSegment(segment, params) {
  const segmentPath = segment.map((part) => getParameter(part, params)).join("");
  return segmentPath ? "/" + segmentPath : "";
}
function getRouteGenerator(segments, addTrailingSlash) {
  return (params) => {
    const sanitizedParams = sanitizeParams(params);
    let trailing = "";
    if (addTrailingSlash === "always" && segments.length) {
      trailing = "/";
    }
    const path = segments.map((segment) => getSegment(segment, sanitizedParams)).join("") + trailing;
    return path || "/";
  };
}

function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0,
    fallbackRoutes: rawRouteData.fallbackRoutes.map((fallback) => {
      return deserializeRouteData(fallback);
    }),
    isIndex: rawRouteData.isIndex,
    origin: rawRouteData.origin
  };
}

function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const inlinedScripts = new Map(serializedManifest.inlinedScripts);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  const serverIslandNameMap = new Map(serializedManifest.serverIslandNameMap);
  const key = decodeKey(serializedManifest.key);
  return {
    // in case user middleware exists, this no-op middleware will be reassigned (see plugin-ssr.ts)
    middleware() {
      return { onRequest: NOOP_MIDDLEWARE_FN };
    },
    ...serializedManifest,
    assets,
    componentMetadata,
    inlinedScripts,
    clientDirectives,
    routes,
    serverIslandNameMap,
    key
  };
}

const manifest = deserializeManifest({"hrefRoot":"file:///D:/Dev/parttec/site/","cacheDir":"file:///D:/Dev/parttec/site/node_modules/.astro/","outDir":"file:///D:/Dev/parttec/site/dist/","srcDir":"file:///D:/Dev/parttec/site/src/","publicDir":"file:///D:/Dev/parttec/site/public/","buildClientDir":"file:///D:/Dev/parttec/site/dist/client/","buildServerDir":"file:///D:/Dev/parttec/site/dist/server/","adapterName":"@astrojs/node","routes":[{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"page","component":"_server-islands.astro","params":["name"],"segments":[[{"content":"_server-islands","dynamic":false,"spread":false}],[{"content":"name","dynamic":true,"spread":false}]],"pattern":"^\\/_server-islands\\/([^/]+?)\\/?$","prerender":false,"isIndex":false,"fallbackRoutes":[],"route":"/_server-islands/[name]","origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"endpoint","isIndex":false,"route":"/_image","pattern":"^\\/_image\\/?$","segments":[[{"content":"_image","dynamic":false,"spread":false}]],"params":[],"component":"node_modules/astro/dist/assets/endpoint/node.js","pathname":"/_image","prerender":false,"fallbackRoutes":[],"origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Ci8tGRhh.css"}],"routeData":{"route":"/account","isIndex":false,"type":"page","pattern":"^\\/account\\/?$","segments":[[{"content":"account","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/account.astro","pathname":"/account","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Ci8tGRhh.css"}],"routeData":{"route":"/brands","isIndex":false,"type":"page","pattern":"^\\/brands\\/?$","segments":[[{"content":"brands","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/brands.astro","pathname":"/brands","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Ci8tGRhh.css"}],"routeData":{"route":"/catalog/brands/[slug]","isIndex":false,"type":"page","pattern":"^\\/catalog\\/brands\\/([^/]+?)\\/?$","segments":[[{"content":"catalog","dynamic":false,"spread":false}],[{"content":"brands","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/catalog/brands/[slug].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Ci8tGRhh.css"}],"routeData":{"route":"/catalog/categories/[slug]","isIndex":false,"type":"page","pattern":"^\\/catalog\\/categories\\/([^/]+?)\\/?$","segments":[[{"content":"catalog","dynamic":false,"spread":false}],[{"content":"categories","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/catalog/categories/[slug].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Ci8tGRhh.css"}],"routeData":{"route":"/catalog/parts/[id]","isIndex":false,"type":"page","pattern":"^\\/catalog\\/parts\\/([^/]+?)\\/?$","segments":[[{"content":"catalog","dynamic":false,"spread":false}],[{"content":"parts","dynamic":false,"spread":false}],[{"content":"id","dynamic":true,"spread":false}]],"params":["id"],"component":"src/pages/catalog/parts/[id].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Ci8tGRhh.css"}],"routeData":{"route":"/catalog","isIndex":false,"type":"page","pattern":"^\\/catalog\\/?$","segments":[[{"content":"catalog","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/catalog.astro","pathname":"/catalog","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Ci8tGRhh.css"}],"routeData":{"route":"/catalog-pro","isIndex":false,"type":"page","pattern":"^\\/catalog-pro\\/?$","segments":[[{"content":"catalog-pro","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/catalog-pro.astro","pathname":"/catalog-pro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Ci8tGRhh.css"}],"routeData":{"route":"/catalog-search","isIndex":false,"type":"page","pattern":"^\\/catalog-search\\/?$","segments":[[{"content":"catalog-search","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/catalog-search.astro","pathname":"/catalog-search","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Ci8tGRhh.css"}],"routeData":{"route":"/categories","isIndex":false,"type":"page","pattern":"^\\/categories\\/?$","segments":[[{"content":"categories","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/categories.astro","pathname":"/categories","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Ci8tGRhh.css"}],"routeData":{"route":"/login","isIndex":false,"type":"page","pattern":"^\\/login\\/?$","segments":[[{"content":"login","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/login.astro","pathname":"/login","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Ci8tGRhh.css"}],"routeData":{"route":"/register","isIndex":false,"type":"page","pattern":"^\\/register\\/?$","segments":[[{"content":"register","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/register.astro","pathname":"/register","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Ci8tGRhh.css"}],"routeData":{"route":"/search","isIndex":false,"type":"page","pattern":"^\\/search\\/?$","segments":[[{"content":"search","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/search.astro","pathname":"/search","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[{"type":"external","src":"/_astro/account.Ci8tGRhh.css"}],"routeData":{"route":"/","isIndex":true,"type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}}],"base":"/","trailingSlash":"ignore","compressHTML":true,"componentMetadata":[["D:/Dev/parttec/site/src/pages/account.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/brands.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/catalog-pro.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/catalog-search.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/catalog.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/catalog/brands/[slug].astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/catalog/categories/[slug].astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/catalog/parts/[id].astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/categories.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/index.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/login.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/register.astro",{"propagation":"none","containsHead":true}],["D:/Dev/parttec/site/src/pages/search.astro",{"propagation":"none","containsHead":true}]],"renderers":[],"clientDirectives":[["idle","(()=>{var l=(n,t)=>{let i=async()=>{await(await n())()},e=typeof t.value==\"object\"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};\"requestIdleCallback\"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var n=(a,t)=>{let i=async()=>{await(await a())()};if(t.value){let e=matchMedia(t.value);e.matches?i():e.addEventListener(\"change\",i,{once:!0})}};(self.Astro||(self.Astro={})).media=n;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var a=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value==\"object\"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let l of e)if(l.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=a;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000astro-internal:middleware":"_astro-internal_middleware.mjs","\u0000noop-actions":"_noop-actions.mjs","\u0000@astro-page:src/pages/account@_@astro":"pages/account.astro.mjs","\u0000@astro-page:src/pages/brands@_@astro":"pages/brands.astro.mjs","\u0000@astro-page:src/pages/catalog/brands/[slug]@_@astro":"pages/catalog/brands/_slug_.astro.mjs","\u0000@astro-page:src/pages/catalog/categories/[slug]@_@astro":"pages/catalog/categories/_slug_.astro.mjs","\u0000@astro-page:src/pages/catalog/parts/[id]@_@astro":"pages/catalog/parts/_id_.astro.mjs","\u0000@astro-page:src/pages/catalog@_@astro":"pages/catalog.astro.mjs","\u0000@astro-page:src/pages/catalog-pro@_@astro":"pages/catalog-pro.astro.mjs","\u0000@astro-page:src/pages/catalog-search@_@astro":"pages/catalog-search.astro.mjs","\u0000@astro-page:src/pages/categories@_@astro":"pages/categories.astro.mjs","\u0000@astro-page:src/pages/login@_@astro":"pages/login.astro.mjs","\u0000@astro-page:src/pages/register@_@astro":"pages/register.astro.mjs","\u0000@astro-page:src/pages/search@_@astro":"pages/search.astro.mjs","\u0000@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","\u0000@astrojs-ssr-virtual-entry":"entry.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000@astro-page:node_modules/astro/dist/assets/endpoint/node@_@js":"pages/_image.astro.mjs","\u0000@astrojs-ssr-adapter":"<EMAIL>","\u0000@astrojs-manifest":"manifest_Dsb_8Q_0.mjs","D:/Dev/parttec/site/node_modules/unstorage/drivers/fs-lite.mjs":"chunks/fs-lite_COtHaKzy.mjs","D:/Dev/parttec/site/node_modules/astro/dist/assets/services/sharp.js":"chunks/sharp_BE11tTb-.mjs","@/components/auth/AccountCard":"_astro/AccountCard.CTo_EFqw.js","@/components/catalog/CategoryCard":"_astro/CategoryCard.DqjroJIS.js","@/components/catalog/PartCard":"_astro/PartCard.BVPHiHYs.js","@/components/catalog/BrandCard":"_astro/BrandCard.DZJFg6-p.js","@/components/catalog/SearchForm":"_astro/SearchForm.BJU1NFIA.js","@/components/catalog/SimpleCatalogSearch":"_astro/SimpleCatalogSearch.BTV7LSaE.js","@/components/auth/LoginForm":"_astro/LoginForm.DWpG7XsB.js","@/components/auth/RegisterForm":"_astro/RegisterForm.BFEcz9E_.js","@/components/navigation/AuthNav":"_astro/AuthNav.CVmTXJx7.js","D:/Dev/parttec/site/node_modules/astro/components/ClientRouter.astro?astro&type=script&index=0&lang.ts":"_astro/ClientRouter.astro_astro_type_script_index_0_lang.B0JFCTGp.js","@astrojs/react/client.js":"_astro/client.B7_wsQ6z.js","@/components/catalog/ModernCatalogSearchIsland":"_astro/ModernCatalogSearchIsland.Dn2pJtLf.js","@/components/providers/TrpcProvider":"_astro/TrpcProvider.RarJVWDx.js","astro:scripts/before-hydration.js":""},"inlinedScripts":[],"assets":["/_astro/account.Ci8tGRhh.css","/favicon.svg","/placeholder.svg","/_astro/AccountCard.CTo_EFqw.js","/_astro/auth-client.D86yjSFk.js","/_astro/AuthNav.CVmTXJx7.js","/_astro/badge.BTnnEvrQ.js","/_astro/BrandCard.DZJFg6-p.js","/_astro/button.CYGhVy7y.js","/_astro/card.DUNQ4BBf.js","/_astro/CategoryCard.DqjroJIS.js","/_astro/client.B7_wsQ6z.js","/_astro/ClientRouter.astro_astro_type_script_index_0_lang.B0JFCTGp.js","/_astro/index.3rXK4OIH.js","/_astro/index.BSy9ry4M.js","/_astro/index.GzzYzsTh.js","/_astro/input.BvZ2Z-in.js","/_astro/jsx-runtime.D_zvdyIk.js","/_astro/label.T9h-zJiu.js","/_astro/LoginForm.DWpG7XsB.js","/_astro/ModernCatalogSearchIsland.Dn2pJtLf.js","/_astro/PartCard.BVPHiHYs.js","/_astro/RegisterForm.BFEcz9E_.js","/_astro/router.WLLD8StG.js","/_astro/search.C9RwVH3H.js","/_astro/SearchForm.BJU1NFIA.js","/_astro/SimpleCatalogSearch.BTV7LSaE.js","/_astro/TrpcProvider.RarJVWDx.js","/_astro/TrpcProvider._sdBuAbb.js","/_astro/utils.CBfrqCZ4.js"],"buildFormat":"directory","checkOrigin":true,"serverIslandNameMap":[],"key":"LJtqhT4e1618G4xMOGorl/rR1rZOcPE+muaB86gfMP8=","sessionConfig":{"driver":"fs-lite","options":{"base":"D:\\Dev\\parttec\\site\\node_modules\\.astro\\sessions"}}});
if (manifest.sessionConfig) manifest.sessionConfig.driverModule = () => import('./chunks/fs-lite_COtHaKzy.mjs');

export { manifest };
