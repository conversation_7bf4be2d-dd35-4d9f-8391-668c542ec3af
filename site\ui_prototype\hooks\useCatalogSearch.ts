"use client"

import { useState, useMemo } from "react"
import type { PartApplicability, SearchFilters, MediaAsset } from "../types/catalog"

// Моковые медиа-ресурсы
const mockMediaAssets: MediaAsset[] = [
  {
    id: 1,
    fileName: "seal-diagram.jpg",
    mimeType: "image/jpeg",
    fileSize: 245760,
    url: "/technical-seal-diagram.png",
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z",
  },
  {
    id: 2,
    fileName: "installation-guide.pdf",
    mimeType: "application/pdf",
    fileSize: 1048576,
    url: "/pdf-installation-guide.png",
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z",
  },
  {
    id: 3,
    fileName: "seal-cross-section.png",
    mimeType: "image/png",
    fileSize: 156789,
    url: "/seal-cross-section-technical.png",
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z",
  },
  {
    id: 4,
    fileName: "specifications.pdf",
    mimeType: "application/pdf",
    fileSize: 2097152,
    url: "/technical-specifications-pdf.png",
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z",
  },
]

// Моковые данные на основе вашей схемы
const mockApplicabilities: PartApplicability[] = [
  {
    id: 1,
    partId: 1,
    catalogItemId: 1,
    accuracy: "EXACT_MATCH",
    part: {
      id: 1,
      name: "Сальник коленвала передний",
      level: 0,
      path: "01",
      partCategoryId: 1,
      partCategory: {
        id: 1,
        name: "Сальники двигателя",
        slug: "engine-seals",
        level: 1,
        path: "01/01",
        image: mockMediaAssets[0],
      },
      attributes: [
        {
          id: 1,
          value: "25.0",
          numericValue: 25.0,
          partId: 1,
          templateId: 1,
          template: {
            id: 1,
            name: "inner_diameter",
            title: "Внутренний диаметр",
            dataType: "NUMBER",
            unit: "MM",
            isRequired: true,
            allowedValues: [],
            tolerance: 0.1,
          },
        },
        {
          id: 2,
          value: "35.0",
          numericValue: 35.0,
          partId: 1,
          templateId: 2,
          template: {
            id: 2,
            name: "outer_diameter",
            title: "Наружный диаметр",
            dataType: "NUMBER",
            unit: "MM",
            isRequired: true,
            allowedValues: [],
            tolerance: 0.1,
          },
        },
        {
          id: 3,
          value: "NBR",
          partId: 1,
          templateId: 3,
          template: {
            id: 3,
            name: "material",
            title: "Материал",
            dataType: "STRING",
            isRequired: true,
            allowedValues: ["NBR", "FKM", "PTFE", "EPDM"],
          },
        },
      ],
      image: mockMediaAssets[0],
      mediaAssets: [mockMediaAssets[0], mockMediaAssets[1], mockMediaAssets[2]],
      createdAt: "2024-01-15T10:00:00Z",
      updatedAt: "2024-01-15T10:00:00Z",
    },
    catalogItem: {
      id: 1,
      sku: "TC-25x35x7",
      description: "Радиальный сальник для коленчатого вала",
      brandId: 1,
      brand: {
        id: 1,
        name: "SKF",
        slug: "skf",
        country: "Sweden",
        isOem: false,
      },
      isPublic: true,
      attributes: [
        {
          id: 1,
          value: "25.0",
          numericValue: 25.0,
          catalogItemId: 1,
          templateId: 1,
          template: {
            id: 1,
            name: "inner_diameter",
            title: "Внутренний диаметр",
            dataType: "NUMBER",
            unit: "MM",
            isRequired: true,
            allowedValues: [],
            tolerance: 0.1,
          },
        },
        {
          id: 2,
          value: "35.0",
          numericValue: 35.0,
          catalogItemId: 1,
          templateId: 2,
          template: {
            id: 2,
            name: "outer_diameter",
            title: "Наружный диаметр",
            dataType: "NUMBER",
            unit: "MM",
            isRequired: true,
            allowedValues: [],
            tolerance: 0.1,
          },
        },
        {
          id: 3,
          value: "NBR",
          catalogItemId: 1,
          templateId: 3,
          template: {
            id: 3,
            name: "material",
            title: "Материал",
            dataType: "STRING",
            isRequired: true,
            allowedValues: ["NBR", "FKM", "PTFE", "EPDM"],
          },
        },
      ],
      image: mockMediaAssets[0],
      mediaAssets: [mockMediaAssets[0], mockMediaAssets[1], mockMediaAssets[3]],
    },
  },
  {
    id: 2,
    partId: 2,
    catalogItemId: 2,
    accuracy: "MATCH_WITH_NOTES",
    notes: "Совпадение по допуску ±0.1мм для внутреннего диаметра",
    part: {
      id: 2,
      name: "Сальник распредвала",
      level: 0,
      path: "02",
      partCategoryId: 1,
      partCategory: {
        id: 1,
        name: "Сальники двигателя",
        slug: "engine-seals",
        level: 1,
        path: "01/01",
        image: mockMediaAssets[0],
      },
      attributes: [
        {
          id: 4,
          value: "30.0",
          numericValue: 30.0,
          partId: 2,
          templateId: 1,
          template: {
            id: 1,
            name: "inner_diameter",
            title: "Внутренний диаметр",
            dataType: "NUMBER",
            unit: "MM",
            isRequired: true,
            allowedValues: [],
            tolerance: 0.1,
          },
        },
        {
          id: 5,
          value: "FKM",
          partId: 2,
          templateId: 3,
          template: {
            id: 3,
            name: "material",
            title: "Материал",
            dataType: "STRING",
            isRequired: true,
            allowedValues: ["NBR", "FKM", "PTFE", "EPDM"],
          },
        },
      ],
      image: mockMediaAssets[2],
      mediaAssets: [mockMediaAssets[2], mockMediaAssets[3]],
      createdAt: "2024-01-15T10:00:00Z",
      updatedAt: "2024-01-15T10:00:00Z",
    },
    catalogItem: {
      id: 2,
      sku: "V-RING-30",
      description: "V-образное уплотнение высокотемпературное",
      brandId: 2,
      brand: {
        id: 2,
        name: "Freudenberg",
        slug: "freudenberg",
        country: "Germany",
        isOem: false,
      },
      isPublic: true,
      attributes: [
        {
          id: 6,
          value: "30.05",
          numericValue: 30.05,
          catalogItemId: 2,
          templateId: 1,
          template: {
            id: 1,
            name: "inner_diameter",
            title: "Внутренний диаметр",
            dataType: "NUMBER",
            unit: "MM",
            isRequired: true,
            allowedValues: [],
            tolerance: 0.1,
          },
        },
        {
          id: 7,
          value: "FKM",
          catalogItemId: 2,
          templateId: 3,
          template: {
            id: 3,
            name: "material",
            title: "Материал",
            dataType: "STRING",
            isRequired: true,
            allowedValues: ["NBR", "FKM", "PTFE", "EPDM"],
          },
        },
      ],
      image: mockMediaAssets[2],
      mediaAssets: [mockMediaAssets[2], mockMediaAssets[1]],
    },
  },
]

export function useCatalogSearch() {
  const [filters, setFilters] = useState<SearchFilters>({
    query: "",
    categoryIds: [],
    brandIds: [],
    attributeFilters: {},
    accuracyLevels: [],
    isOemOnly: false,
  })

  const filteredResults = useMemo(() => {
    return mockApplicabilities.filter((applicability) => {
      // Текстовый поиск
      if (filters.query) {
        const query = filters.query.toLowerCase()
        const matchesQuery =
          applicability.catalogItem.sku.toLowerCase().includes(query) ||
          applicability.catalogItem.description?.toLowerCase().includes(query) ||
          applicability.part.name?.toLowerCase().includes(query) ||
          applicability.catalogItem.brand.name.toLowerCase().includes(query)

        if (!matchesQuery) return false
      }

      // Фильтр по категориям
      if (filters.categoryIds.length > 0) {
        if (!filters.categoryIds.includes(applicability.part.partCategoryId)) {
          return false
        }
      }

      // Фильтр по брендам
      if (filters.brandIds.length > 0) {
        if (!filters.brandIds.includes(applicability.catalogItem.brandId)) {
          return false
        }
      }

      // Фильтр по точности применимости
      if (filters.accuracyLevels.length > 0) {
        if (!filters.accuracyLevels.includes(applicability.accuracy)) {
          return false
        }
      }

      // Фильтр OEM
      if (filters.isOemOnly && !applicability.catalogItem.brand.isOem) {
        return false
      }

      // Фильтры по атрибутам
      for (const [templateId, filter] of Object.entries(filters.attributeFilters)) {
        const templateIdNum = Number.parseInt(templateId)
        const catalogItemAttr = applicability.catalogItem.attributes.find((attr) => attr.templateId === templateIdNum)

        if (!catalogItemAttr) continue

        // Строковые значения
        if (filter.values.length > 0) {
          if (!filter.values.includes(catalogItemAttr.value)) {
            return false
          }
        }

        // Числовые диапазоны
        if (filter.numericRange && catalogItemAttr.numericValue !== undefined) {
          const [min, max] = filter.numericRange
          if (catalogItemAttr.numericValue < min || catalogItemAttr.numericValue > max) {
            return false
          }
        }
      }

      return true
    })
  }, [filters])

  return {
    results: filteredResults,
    filters,
    setFilters,
    totalCount: mockApplicabilities.length,
    filteredCount: filteredResults.length,
  }
}
