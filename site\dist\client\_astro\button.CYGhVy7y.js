import{j as d}from"./jsx-runtime.D_zvdyIk.js";import{r as s}from"./index.GzzYzsTh.js";import{c as m}from"./index.3rXK4OIH.js";import{c as b}from"./utils.CBfrqCZ4.js";function p(e,n){if(typeof e=="function")return e(n);e!=null&&(e.current=n)}function g(...e){return n=>{let r=!1;const o=e.map(t=>{const i=p(t,n);return!r&&typeof i=="function"&&(r=!0),i});if(r)return()=>{for(let t=0;t<o.length;t++){const i=o[t];typeof i=="function"?i():p(e[t],null)}}}}function z(...e){return s.useCallback(g(...e),e)}function h(e){const n=x(e),r=s.forwardRef((o,t)=>{const{children:i,...l}=o,a=s.Children.toArray(i),c=a.find(E);if(c){const u=c.props.children,v=a.map(f=>f===c?s.Children.count(u)>1?s.Children.only(null):s.isValidElement(u)?u.props.children:null:f);return d.jsx(n,{...l,ref:t,children:s.isValidElement(u)?s.cloneElement(u,void 0,v):null})}return d.jsx(n,{...l,ref:t,children:i})});return r.displayName=`${e}.Slot`,r}var y=h("Slot");function x(e){const n=s.forwardRef((r,o)=>{const{children:t,...i}=r;if(s.isValidElement(t)){const l=k(t),a=S(i,t.props);return t.type!==s.Fragment&&(a.ref=o?g(o,l):l),s.cloneElement(t,a)}return s.Children.count(t)>1?s.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}var C=Symbol("radix.slottable");function E(e){return s.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===C}function S(e,n){const r={...n};for(const o in n){const t=e[o],i=n[o];/^on[A-Z]/.test(o)?t&&i?r[o]=(...a)=>{const c=i(...a);return t(...a),c}:t&&(r[o]=t):o==="style"?r[o]={...t,...i}:o==="className"&&(r[o]=[t,i].filter(Boolean).join(" "))}return{...e,...r}}function k(e){let n=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=n&&"isReactWarning"in n&&n.isReactWarning;return r?e.ref:(n=Object.getOwnPropertyDescriptor(e,"ref")?.get,r=n&&"isReactWarning"in n&&n.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}const R=m("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function I({className:e,variant:n,size:r,asChild:o=!1,...t}){const i=o?y:"button";return d.jsx(i,{"data-slot":"button",className:b(R({variant:n,size:r,className:e})),...t})}export{I as B,y as S,h as c,z as u};
