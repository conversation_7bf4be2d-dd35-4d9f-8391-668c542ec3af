"use client"

import { Package } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { ProfessionalHeader } from "../pro/ProfessionalHeader"
import { useCatalogSearch } from "../pro/useCatalogSearch"

export default function SearchHeaderIsland() {
  const { filters, updateFilters, totalCount, filteredCount } = useCatalogSearch()

  const handleSearchChange = (query: string) => {
    updateFilters({ query })
  }

  const handleOpenAI = () => {
    // Будет реализовано в AIAssistantIsland
    const event = new CustomEvent('openAIAssistant')
    window.dispatchEvent(event)
  }

  return (
    <div className="bg-background border-b border-border/40">
      <ProfessionalHeader
        totalCount={totalCount}
        filteredCount={filteredCount}
        searchQuery={filters.query}
        onSearchChange={handleSearchChange}
        onOpenAI={handleOpenAI}
      />
      
      {/* Статистика результатов */}
      <div className="container max-w-none px-4 py-2">
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="px-2 py-1 font-medium text-xs">
            <Package className="h-3 w-3 mr-1" />
            {filteredCount} позиций
          </Badge>
          {filteredCount !== totalCount && (
            <Badge variant="outline" className="px-2 py-1 text-xs">
              из {totalCount} общих
            </Badge>
          )}
        </div>
      </div>
    </div>
  )
}
