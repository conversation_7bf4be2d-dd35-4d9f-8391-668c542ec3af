import type { Context } from '../trpc'
import { router, publicProcedure } from '../trpc'
import { PartInputSchema } from '../generated/zod/input/PartInput.schema'
import { PartCategoryInputSchema } from '../generated/zod/input/PartCategoryInput.schema'
import { BrandInputSchema } from '../generated/zod/input/BrandInput.schema'
import { CatalogService } from '../services/catalog.service'

export const catalogRouter = router({
  listParts: publicProcedure
    .input(PartInputSchema.findMany)
    .query(async ({ ctx, input }: { ctx: Context; input: typeof PartInputSchema['findMany']['_type'] }) =>
      CatalogService.listParts(ctx.db, input as any)
    ),

  countParts: publicProcedure
    .input(PartInputSchema.count.optional())
    .query(async ({ ctx, input }: { ctx: Context; input: typeof PartInputSchema['count']['_type'] | undefined }) =>
      CatalogService.countParts(ctx.db, input as any)
    ),

  listCategories: publicProcedure
    .input(PartCategoryInputSchema.findMany.optional())
    .query(async ({ ctx, input }: { ctx: Context; input: typeof PartCategoryInputSchema['findMany']['_type'] | undefined }) =>
      CatalogService.listCategories(ctx.db, input as any)
    ),

  listBrands: publicProcedure
    .input(BrandInputSchema.findMany.optional())
    .query(async ({ ctx, input }: { ctx: Context; input: typeof BrandInputSchema['findMany']['_type'] | undefined }) =>
      CatalogService.listBrands(ctx.db, input as any)
    ),
})

