import { e as createComponent, k as renderComponent, r as renderTemplate } from '../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { B as Button, $ as $$MainLayout } from '../chunks/MainLayout_B4WbK6nK.mjs';
import { jsxs, jsx } from 'react/jsx-runtime';
import { useState } from 'react';
import { Search, Filter, Package } from 'lucide-react';
import { I as Input } from '../chunks/input_B2Kb__1-.mjs';
import { C as Card, b as CardHeader, c as CardTitle, a as CardContent } from '../chunks/card_Dx3ipgID.mjs';
import { B as Badge } from '../chunks/badge_CaSK_yxb.mjs';
import { C as Checkbox, L as Label, S as Separator, a as StatusBadge } from '../chunks/label_5LZUw9kF.mjs';
export { renderers } from '../renderers.mjs';

const mockResults = [
  {
    id: 1,
    partId: 1,
    catalogItemId: 1,
    accuracy: "EXACT_MATCH",
    part: {
      id: 1,
      name: "Сальник коленвала передний",
      partCategory: {
        id: 1,
        name: "Сальники двигателя",
        slug: "engine-seals"
      }
    },
    catalogItem: {
      id: 1,
      sku: "TC-25x35x7",
      description: "Радиальный сальник для коленчатого вала",
      brand: {
        id: 1,
        name: "SKF",
        slug: "skf",
        isOem: false
      },
      attributes: [
        {
          id: 1,
          value: "25.0",
          template: {
            id: 1,
            title: "Внутренний диаметр",
            unit: "MM"
          }
        },
        {
          id: 2,
          value: "35.0",
          template: {
            id: 2,
            title: "Наружный диаметр",
            unit: "MM"
          }
        }
      ]
    }
  }
];
const mockCategories = [
  { id: 1, name: "Сальники двигателя" },
  { id: 2, name: "Сальники трансмиссии" },
  { id: 3, name: "Уплотнения гидравлики" }
];
const mockBrands = [
  { id: 1, name: "SKF", isOem: false },
  { id: 2, name: "Freudenberg", isOem: false },
  { id: 3, name: "Caterpillar", isOem: true }
];
function SimpleCatalogSearch() {
  const [filters, setFilters] = useState({
    query: "",
    categoryIds: [],
    brandIds: [],
    attributeFilters: {},
    accuracyLevels: [],
    isOemOnly: false
  });
  const [showFilters, setShowFilters] = useState(false);
  const handleCategoryToggle = (categoryId) => {
    setFilters((prev) => ({
      ...prev,
      categoryIds: prev.categoryIds.includes(categoryId) ? prev.categoryIds.filter((id) => id !== categoryId) : [...prev.categoryIds, categoryId]
    }));
  };
  const handleBrandToggle = (brandId) => {
    setFilters((prev) => ({
      ...prev,
      brandIds: prev.brandIds.includes(brandId) ? prev.brandIds.filter((id) => id !== brandId) : [...prev.brandIds, brandId]
    }));
  };
  const activeFiltersCount = filters.categoryIds.length + filters.brandIds.length + (filters.isOemOnly ? 1 : 0);
  return /* @__PURE__ */ jsxs("div", { className: "min-h-screen bg-background", children: [
    /* @__PURE__ */ jsx("header", { className: "sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60", children: /* @__PURE__ */ jsxs("div", { className: "container flex h-14 items-center", children: [
      /* @__PURE__ */ jsx("div", { className: "mr-4 flex", children: /* @__PURE__ */ jsx("h1", { className: "text-lg font-semibold", children: "PartTec3 Каталог" }) }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-1 items-center space-x-2", children: [
        /* @__PURE__ */ jsxs("div", { className: "relative flex-1 max-w-sm", children: [
          /* @__PURE__ */ jsx(Search, { className: "absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" }),
          /* @__PURE__ */ jsx(
            Input,
            {
              placeholder: "Поиск по артикулу, описанию...",
              value: filters.query,
              onChange: (e) => setFilters((prev) => ({ ...prev, query: e.target.value })),
              className: "pl-8"
            }
          )
        ] }),
        /* @__PURE__ */ jsxs(
          Button,
          {
            variant: "outline",
            size: "sm",
            onClick: () => setShowFilters(!showFilters),
            className: "gap-2",
            children: [
              /* @__PURE__ */ jsx(Filter, { className: "h-4 w-4" }),
              "Фильтры",
              activeFiltersCount > 0 && /* @__PURE__ */ jsx(Badge, { variant: "secondary", className: "ml-1 h-5 w-5 rounded-full p-0 text-xs", children: activeFiltersCount })
            ]
          }
        )
      ] })
    ] }) }),
    /* @__PURE__ */ jsx("div", { className: "container py-6", children: /* @__PURE__ */ jsxs("div", { className: "flex gap-6", children: [
      showFilters && /* @__PURE__ */ jsxs("div", { className: "w-64 space-y-4", children: [
        /* @__PURE__ */ jsxs(Card, { children: [
          /* @__PURE__ */ jsx(CardHeader, { className: "pb-3", children: /* @__PURE__ */ jsx(CardTitle, { className: "text-sm", children: "Категории" }) }),
          /* @__PURE__ */ jsx(CardContent, { className: "space-y-2", children: mockCategories.map((category) => /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2", children: [
            /* @__PURE__ */ jsx(
              Checkbox,
              {
                id: `category-${category.id}`,
                checked: filters.categoryIds.includes(category.id),
                onCheckedChange: () => handleCategoryToggle(category.id)
              }
            ),
            /* @__PURE__ */ jsx(
              Label,
              {
                htmlFor: `category-${category.id}`,
                className: "text-sm font-normal cursor-pointer",
                children: category.name
              }
            )
          ] }, category.id)) })
        ] }),
        /* @__PURE__ */ jsxs(Card, { children: [
          /* @__PURE__ */ jsx(CardHeader, { className: "pb-3", children: /* @__PURE__ */ jsx(CardTitle, { className: "text-sm", children: "Производители" }) }),
          /* @__PURE__ */ jsxs(CardContent, { className: "space-y-2", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2 mb-3", children: [
              /* @__PURE__ */ jsx(
                Checkbox,
                {
                  id: "oem-only",
                  checked: filters.isOemOnly,
                  onCheckedChange: (checked) => setFilters((prev) => ({ ...prev, isOemOnly: !!checked }))
                }
              ),
              /* @__PURE__ */ jsx(Label, { htmlFor: "oem-only", className: "text-sm font-medium cursor-pointer", children: "Только OEM" })
            ] }),
            /* @__PURE__ */ jsx(Separator, {}),
            mockBrands.map((brand) => /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2", children: [
              /* @__PURE__ */ jsx(
                Checkbox,
                {
                  id: `brand-${brand.id}`,
                  checked: filters.brandIds.includes(brand.id),
                  onCheckedChange: () => handleBrandToggle(brand.id)
                }
              ),
              /* @__PURE__ */ jsxs(
                Label,
                {
                  htmlFor: `brand-${brand.id}`,
                  className: "text-sm font-normal cursor-pointer flex items-center gap-2",
                  children: [
                    brand.name,
                    brand.isOem && /* @__PURE__ */ jsx(Badge, { variant: "outline", className: "text-xs", children: "OEM" })
                  ]
                }
              )
            ] }, brand.id))
          ] })
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
        /* @__PURE__ */ jsxs("div", { className: "mb-4 flex items-center justify-between", children: [
          /* @__PURE__ */ jsx("h2", { className: "text-xl font-semibold", children: "Результаты поиска" }),
          /* @__PURE__ */ jsxs(Badge, { variant: "secondary", children: [
            /* @__PURE__ */ jsx(Package, { className: "h-3 w-3 mr-1" }),
            mockResults.length,
            " позиций"
          ] })
        ] }),
        /* @__PURE__ */ jsx("div", { className: "space-y-4", children: mockResults.map((result) => /* @__PURE__ */ jsx(Card, { className: "hover:shadow-md transition-shadow", children: /* @__PURE__ */ jsxs(CardContent, { className: "p-4", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex items-start justify-between mb-3", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
              /* @__PURE__ */ jsx("h3", { className: "text-lg font-mono font-bold text-primary mb-1", children: result.catalogItem.sku }),
              /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2 mb-2", children: [
                /* @__PURE__ */ jsx(Badge, { variant: "outline", children: result.catalogItem.brand.name }),
                result.catalogItem.brand.isOem && /* @__PURE__ */ jsx(Badge, { variant: "secondary", children: "OEM" })
              ] }),
              /* @__PURE__ */ jsx("h4", { className: "font-semibold text-sm mb-1", children: result.part.name }),
              /* @__PURE__ */ jsx("p", { className: "text-sm text-muted-foreground", children: result.catalogItem.description })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-end gap-2", children: [
              /* @__PURE__ */ jsx(StatusBadge, { status: result.accuracy, size: "sm" }),
              /* @__PURE__ */ jsx(Badge, { variant: "outline", className: "text-xs", children: result.part.partCategory.name })
            ] })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "flex flex-wrap gap-2 mb-3", children: result.catalogItem.attributes.map((attr) => /* @__PURE__ */ jsxs(
            "div",
            {
              className: "inline-flex items-center gap-1 px-2 py-1 rounded-full bg-muted text-xs",
              children: [
                /* @__PURE__ */ jsxs("span", { className: "text-muted-foreground", children: [
                  attr.template.title,
                  ":"
                ] }),
                /* @__PURE__ */ jsx("span", { className: "font-mono font-semibold", children: attr.value }),
                attr.template.unit && /* @__PURE__ */ jsx("span", { className: "text-muted-foreground", children: attr.template.unit })
              ]
            },
            attr.id
          )) }),
          /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between pt-2 border-t", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
              /* @__PURE__ */ jsxs(Badge, { variant: "outline", className: "text-xs font-mono", children: [
                "#",
                result.partId
              ] }),
              /* @__PURE__ */ jsxs(Badge, { variant: "outline", className: "text-xs font-mono", children: [
                "#",
                result.catalogItemId
              ] })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
              /* @__PURE__ */ jsx(Button, { variant: "outline", size: "sm", children: "Подробности" }),
              /* @__PURE__ */ jsx(Button, { size: "sm", children: "В корзину" })
            ] })
          ] })
        ] }) }, result.id)) })
      ] })
    ] }) })
  ] });
}

const $$CatalogSearch = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "\u041F\u043E\u0438\u0441\u043A \u043F\u043E \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0443 - PartTec3" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "SimpleCatalogSearch", SimpleCatalogSearch, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/catalog/SimpleCatalogSearch", "client:component-export": "default" })} ` })}`;
}, "D:/Dev/parttec/site/src/pages/catalog-search.astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/catalog-search.astro";
const $$url = "/catalog-search";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$CatalogSearch,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
