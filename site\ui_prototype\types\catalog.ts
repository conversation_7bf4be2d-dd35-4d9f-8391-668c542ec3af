// Типы данных на основе вашей Prisma схемы
export interface MediaAsset {
  id: number
  fileName: string
  mimeType: string
  fileSize?: number
  url: string
  createdAt: string
  updatedAt: string
}

export interface AttributeTemplate {
  id: number
  name: string
  title: string
  description?: string
  dataType: "STRING" | "NUMBER" | "BOOLEAN" | "DATE" | "JSON"
  unit?: string
  isRequired: boolean
  minValue?: number
  maxValue?: number
  allowedValues: string[]
  tolerance?: number
  groupId?: number
}

export interface PartAttribute {
  id: number
  value: string
  numericValue?: number
  partId: number
  templateId: number
  template: AttributeTemplate
}

export interface CatalogItemAttribute {
  id: number
  value: string
  numericValue?: number
  catalogItemId: number
  templateId: number
  template: AttributeTemplate
}

export interface Brand {
  id: number
  name: string
  slug: string
  country?: string
  isOem: boolean
}

export interface PartCategory {
  id: number
  name: string
  slug: string
  description?: string
  parentId?: number
  level: number
  path: string
  icon?: string
  image?: MediaAsset
}

export interface Part {
  id: number
  name?: string
  parentId?: number
  level: number
  path: string
  partCategoryId: number
  partCategory: PartCategory
  attributes: PartAttribute[]
  image?: MediaAsset
  mediaAssets: MediaAsset[]
  createdAt: string
  updatedAt: string
}

export interface CatalogItem {
  id: number
  sku: string
  source?: string
  description?: string
  brandId: number
  brand: Brand
  isPublic: boolean
  attributes: CatalogItemAttribute[]
  image?: MediaAsset
  mediaAssets: MediaAsset[]
}

export interface PartApplicability {
  id: number
  partId: number
  catalogItemId: number
  accuracy: "EXACT_MATCH" | "MATCH_WITH_NOTES" | "REQUIRES_MODIFICATION" | "PARTIAL_MATCH"
  notes?: string
  part: Part
  catalogItem: CatalogItem
}

export interface SearchFilters {
  query: string
  categoryIds: number[]
  brandIds: number[]
  attributeFilters: Record<
    number,
    {
      values: string[]
      numericRange?: [number, number]
    }
  >
  accuracyLevels: string[]
  isOemOnly: boolean
}
