import{c as I}from"./search.C9RwVH3H.js";import{j as p}from"./jsx-runtime.D_zvdyIk.js";import{r as a,R as G,g as K}from"./index.GzzYzsTh.js";import{u as T,c as J}from"./button.CYGhVy7y.js";import{c as A}from"./utils.CBfrqCZ4.js";import{c as Y}from"./index.3rXK4OIH.js";import{B as Z}from"./badge.BTnnEvrQ.js";import{r as ee}from"./index.BSy9ry4M.js";/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const te=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],ne=I("check",te);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const re=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],He=I("funnel",re);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oe=[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]],$e=I("package",oe);function Ue(e,t){const n=a.createContext(t),s=r=>{const{children:c,...i}=r,u=a.useMemo(()=>i,Object.values(i));return p.jsx(n.Provider,{value:u,children:c})};s.displayName=e+"Provider";function o(r){const c=a.useContext(n);if(c)return c;if(t!==void 0)return t;throw new Error(`\`${r}\` must be used within \`${e}\``)}return[s,o]}function se(e,t=[]){let n=[];function s(r,c){const i=a.createContext(c),u=n.length;n=[...n,c];const d=l=>{const{scope:m,children:h,...b}=l,g=m?.[e]?.[u]||i,C=a.useMemo(()=>b,Object.values(b));return p.jsx(g.Provider,{value:C,children:h})};d.displayName=r+"Provider";function f(l,m){const h=m?.[e]?.[u]||i,b=a.useContext(h);if(b)return b;if(c!==void 0)return c;throw new Error(`\`${l}\` must be used within \`${r}\``)}return[d,f]}const o=()=>{const r=n.map(c=>a.createContext(c));return function(i){const u=i?.[e]||r;return a.useMemo(()=>({[`__scope${e}`]:{...i,[e]:u}}),[i,u])}};return o.scopeName=e,[s,ae(o,...t)]}function ae(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const s=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(r){const c=s.reduce((i,{useScope:u,scopeName:d})=>{const l=u(r)[`__scope${d}`];return{...i,...l}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}function P(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),n===!1||!o.defaultPrevented)return t?.(o)}}var E=globalThis?.document?a.useLayoutEffect:()=>{},ie=G[" useInsertionEffect ".trim().toString()]||E;function ce({prop:e,defaultProp:t,onChange:n=()=>{},caller:s}){const[o,r,c]=ue({defaultProp:t,onChange:n}),i=e!==void 0,u=i?e:o;{const f=a.useRef(e!==void 0);a.useEffect(()=>{const l=f.current;l!==i&&console.warn(`${s} is changing from ${l?"controlled":"uncontrolled"} to ${i?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),f.current=i},[i,s])}const d=a.useCallback(f=>{if(i){const l=de(f)?f(e):f;l!==e&&c.current?.(l)}else r(f)},[i,e,r,c]);return[u,d]}function ue({defaultProp:e,onChange:t}){const[n,s]=a.useState(e),o=a.useRef(n),r=a.useRef(t);return ie(()=>{r.current=t},[t]),a.useEffect(()=>{o.current!==n&&(r.current?.(n),o.current=n)},[n,o]),[n,s,r]}function de(e){return typeof e=="function"}function le(e){const t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}function fe(e){const[t,n]=a.useState(void 0);return E(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const s=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const r=o[0];let c,i;if("borderBoxSize"in r){const u=r.borderBoxSize,d=Array.isArray(u)?u[0]:u;c=d.inlineSize,i=d.blockSize}else c=e.offsetWidth,i=e.offsetHeight;n({width:c,height:i})});return s.observe(e,{box:"border-box"}),()=>s.unobserve(e)}else n(void 0)},[e]),t}function pe(e,t){return a.useReducer((n,s)=>t[n][s]??n,e)}var j=e=>{const{present:t,children:n}=e,s=me(t),o=typeof n=="function"?n({present:s.isPresent}):a.Children.only(n),r=T(s.ref,he(o));return typeof n=="function"||s.isPresent?a.cloneElement(o,{ref:r}):null};j.displayName="Presence";function me(e){const[t,n]=a.useState(),s=a.useRef(null),o=a.useRef(e),r=a.useRef("none"),c=e?"mounted":"unmounted",[i,u]=pe(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return a.useEffect(()=>{const d=N(s.current);r.current=i==="mounted"?d:"none"},[i]),E(()=>{const d=s.current,f=o.current;if(f!==e){const m=r.current,h=N(d);e?u("MOUNT"):h==="none"||d?.display==="none"?u("UNMOUNT"):u(f&&m!==h?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,u]),E(()=>{if(t){let d;const f=t.ownerDocument.defaultView??window,l=h=>{const g=N(s.current).includes(CSS.escape(h.animationName));if(h.target===t&&g&&(u("ANIMATION_END"),!o.current)){const C=t.style.animationFillMode;t.style.animationFillMode="forwards",d=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=C)})}},m=h=>{h.target===t&&(r.current=N(s.current))};return t.addEventListener("animationstart",m),t.addEventListener("animationcancel",l),t.addEventListener("animationend",l),()=>{f.clearTimeout(d),t.removeEventListener("animationstart",m),t.removeEventListener("animationcancel",l),t.removeEventListener("animationend",l)}}else u("ANIMATION_END")},[t,u]),{isPresent:["mounted","unmountSuspended"].includes(i),ref:a.useCallback(d=>{s.current=d?getComputedStyle(d):null,n(d)},[])}}function N(e){return e?.animationName||"none"}function he(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var D=ee();const Fe=K(D);var be=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],y=be.reduce((e,t)=>{const n=J(`Primitive.${t}`),s=a.forwardRef((o,r)=>{const{asChild:c,...i}=o,u=c?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),p.jsx(u,{...i,ref:r})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function Be(e,t){e&&D.flushSync(()=>e.dispatchEvent(t))}var R="Checkbox",[ve,We]=se(R),[xe,_]=ve(R);function ge(e){const{__scopeCheckbox:t,checked:n,children:s,defaultChecked:o,disabled:r,form:c,name:i,onCheckedChange:u,required:d,value:f="on",internal_do_not_use_render:l}=e,[m,h]=ce({prop:n,defaultProp:o??!1,onChange:u,caller:R}),[b,g]=a.useState(null),[C,v]=a.useState(null),x=a.useRef(!1),S=b?!!c||!!b.closest("form"):!0,w={checked:m,disabled:r,setChecked:h,control:b,setControl:g,name:i,form:c,value:f,hasConsumerStoppedPropagationRef:x,required:d,defaultChecked:k(o)?!1:o,isFormControl:S,bubbleInput:C,setBubbleInput:v};return p.jsx(xe,{scope:t,...w,children:Ce(l)?l(w):s})}var z="CheckboxTrigger",L=a.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...s},o)=>{const{control:r,value:c,disabled:i,checked:u,required:d,setControl:f,setChecked:l,hasConsumerStoppedPropagationRef:m,isFormControl:h,bubbleInput:b}=_(z,e),g=T(o,f),C=a.useRef(u);return a.useEffect(()=>{const v=r?.form;if(v){const x=()=>l(C.current);return v.addEventListener("reset",x),()=>v.removeEventListener("reset",x)}},[r,l]),p.jsx(y.button,{type:"button",role:"checkbox","aria-checked":k(u)?"mixed":u,"aria-required":d,"data-state":W(u),"data-disabled":i?"":void 0,disabled:i,value:c,...s,ref:g,onKeyDown:P(t,v=>{v.key==="Enter"&&v.preventDefault()}),onClick:P(n,v=>{l(x=>k(x)?!0:!x),b&&h&&(m.current=v.isPropagationStopped(),m.current||v.stopPropagation())})})});L.displayName=z;var H=a.forwardRef((e,t)=>{const{__scopeCheckbox:n,name:s,checked:o,defaultChecked:r,required:c,disabled:i,value:u,onCheckedChange:d,form:f,...l}=e;return p.jsx(ge,{__scopeCheckbox:n,checked:o,defaultChecked:r,disabled:i,required:c,onCheckedChange:d,name:s,form:f,value:u,internal_do_not_use_render:({isFormControl:m})=>p.jsxs(p.Fragment,{children:[p.jsx(L,{...l,ref:t,__scopeCheckbox:n}),m&&p.jsx(B,{__scopeCheckbox:n})]})})});H.displayName=R;var $="CheckboxIndicator",U=a.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:s,...o}=e,r=_($,n);return p.jsx(j,{present:s||k(r.checked)||r.checked===!0,children:p.jsx(y.span,{"data-state":W(r.checked),"data-disabled":r.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});U.displayName=$;var F="CheckboxBubbleInput",B=a.forwardRef(({__scopeCheckbox:e,...t},n)=>{const{control:s,hasConsumerStoppedPropagationRef:o,checked:r,defaultChecked:c,required:i,disabled:u,name:d,value:f,form:l,bubbleInput:m,setBubbleInput:h}=_(F,e),b=T(n,h),g=le(r),C=fe(s);a.useEffect(()=>{const x=m;if(!x)return;const S=window.HTMLInputElement.prototype,M=Object.getOwnPropertyDescriptor(S,"checked").set,X=!o.current;if(g!==r&&M){const Q=new Event("click",{bubbles:X});x.indeterminate=k(r),M.call(x,k(r)?!1:r),x.dispatchEvent(Q)}},[m,g,r,o]);const v=a.useRef(k(r)?!1:r);return p.jsx(y.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??v.current,required:i,disabled:u,name:d,value:f,form:l,...t,tabIndex:-1,ref:b,style:{...t.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});B.displayName=F;function Ce(e){return typeof e=="function"}function k(e){return e==="indeterminate"}function W(e){return k(e)?"indeterminate":e?"checked":"unchecked"}function qe({className:e,...t}){return p.jsx(H,{"data-slot":"checkbox",className:A("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:p.jsx(U,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:p.jsx(ne,{className:"size-3.5"})})})}var ke="Separator",O="horizontal",ye=["horizontal","vertical"],q=a.forwardRef((e,t)=>{const{decorative:n,orientation:s=O,...o}=e,r=Ne(s)?s:O,i=n?{role:"none"}:{"aria-orientation":r==="vertical"?r:void 0,role:"separator"};return p.jsx(y.div,{"data-orientation":r,...i,...o,ref:t})});q.displayName=ke;function Ne(e){return ye.includes(e)}var Ee=q;function Ve({className:e,orientation:t="horizontal",decorative:n=!0,...s}){return p.jsx(Ee,{"data-slot":"separator",decorative:n,orientation:t,className:A("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...s})}const Ae=Y("inline-flex items-center gap-1 font-medium",{variants:{status:{EXACT_MATCH:"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",MATCH_WITH_NOTES:"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800",REQUIRES_MODIFICATION:"bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800",PARTIAL_MATCH:"bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"},size:{sm:"text-xs px-2 py-1",default:"text-sm px-2.5 py-1.5",lg:"text-base px-3 py-2"}},defaultVariants:{status:"EXACT_MATCH",size:"default"}}),Re={EXACT_MATCH:"Точное совпадение",MATCH_WITH_NOTES:"С примечаниями",REQUIRES_MODIFICATION:"Требует доработки",PARTIAL_MATCH:"Частичное совпадение"},Se={EXACT_MATCH:"●",MATCH_WITH_NOTES:"◐",REQUIRES_MODIFICATION:"◑",PARTIAL_MATCH:"○"},we=a.forwardRef(({className:e,status:t,size:n,...s},o)=>p.jsxs(Z,{ref:o,className:A(Ae({status:t,size:n,className:e})),variant:"outline",...s,children:[p.jsx("span",{className:"text-current",children:Se[t]}),Re[t]]}));we.displayName="StatusBadge";var Ie="Label",V=a.forwardRef((e,t)=>p.jsx(y.label,{...e,ref:t,onMouseDown:n=>{n.target.closest("button, input, select, textarea")||(e.onMouseDown?.(n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));V.displayName=Ie;var Te=V;function Xe({className:e,...t}){return p.jsx(Te,{"data-slot":"label",className:A("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}export{qe as C,He as F,Xe as L,$e as P,Fe as R,Ve as S,we as a,y as b,P as c,Be as d,ce as e,se as f,j as g,Ue as h,fe as i,le as j,E as u};
