import { jsx, jsxs } from 'react/jsx-runtime';
import * as React from 'react';
import * as CheckboxPrimitive from '@radix-ui/react-checkbox';
import { CheckIcon } from 'lucide-react';
import { c as cn } from './MainLayout_B4WbK6nK.mjs';
import * as SeparatorPrimitive from '@radix-ui/react-separator';
import { cva } from 'class-variance-authority';
import { B as Badge } from './badge_CaSK_yxb.mjs';
import * as LabelPrimitive from '@radix-ui/react-label';

function Checkbox({
  className,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    CheckboxPrimitive.Root,
    {
      "data-slot": "checkbox",
      className: cn(
        "peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",
        className
      ),
      ...props,
      children: /* @__PURE__ */ jsx(
        CheckboxPrimitive.Indicator,
        {
          "data-slot": "checkbox-indicator",
          className: "flex items-center justify-center text-current transition-none",
          children: /* @__PURE__ */ jsx(CheckIcon, { className: "size-3.5" })
        }
      )
    }
  );
}

function Separator({
  className,
  orientation = "horizontal",
  decorative = true,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    SeparatorPrimitive.Root,
    {
      "data-slot": "separator",
      decorative,
      orientation,
      className: cn(
        "bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",
        className
      ),
      ...props
    }
  );
}

const statusBadgeVariants = cva(
  "inline-flex items-center gap-1 font-medium",
  {
    variants: {
      status: {
        EXACT_MATCH: "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800",
        MATCH_WITH_NOTES: "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800",
        REQUIRES_MODIFICATION: "bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800",
        PARTIAL_MATCH: "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"
      },
      size: {
        sm: "text-xs px-2 py-1",
        default: "text-sm px-2.5 py-1.5",
        lg: "text-base px-3 py-2"
      }
    },
    defaultVariants: {
      status: "EXACT_MATCH",
      size: "default"
    }
  }
);
const statusLabels = {
  EXACT_MATCH: "Точное совпадение",
  MATCH_WITH_NOTES: "С примечаниями",
  REQUIRES_MODIFICATION: "Требует доработки",
  PARTIAL_MATCH: "Частичное совпадение"
};
const statusIcons = {
  EXACT_MATCH: "●",
  MATCH_WITH_NOTES: "◐",
  REQUIRES_MODIFICATION: "◑",
  PARTIAL_MATCH: "○"
};
const StatusBadge = React.forwardRef(
  ({ className, status, size, ...props }, ref) => {
    return /* @__PURE__ */ jsxs(
      Badge,
      {
        ref,
        className: cn(statusBadgeVariants({ status, size, className })),
        variant: "outline",
        ...props,
        children: [
          /* @__PURE__ */ jsx("span", { className: "text-current", children: statusIcons[status] }),
          statusLabels[status]
        ]
      }
    );
  }
);
StatusBadge.displayName = "StatusBadge";

function Label({
  className,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    LabelPrimitive.Root,
    {
      "data-slot": "label",
      className: cn(
        "flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",
        className
      ),
      ...props
    }
  );
}

export { Checkbox as C, Label as L, Separator as S, StatusBadge as a };
