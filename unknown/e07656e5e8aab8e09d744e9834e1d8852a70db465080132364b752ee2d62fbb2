import { e as createComponent, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { $ as $$MainLayout } from '../chunks/MainLayout_B4WbK6nK.mjs';
import { t as trpcClient, T as TrpcProvider } from '../chunks/TrpcProvider_JR8NncQp.mjs';
import { S as SearchForm } from '../chunks/SearchForm_C2H808e6.mjs';
import { C as CategoryCard } from '../chunks/CategoryCard_DgfIuIKk.mjs';
import { P as PartCard } from '../chunks/PartCard_Cv7l0RIJ.mjs';
import { r as rootCategoriesInclude, p as partListInclude } from '../chunks/queries_B6FA7B2T.mjs';
export { renderers } from '../renderers.mjs';

const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  let featuredCategories = [];
  let recentParts = [];
  let stats = { parts: 0, categories: 0, brands: 0 };
  try {
    const categoriesResponse = await trpcClient.crud.partCategory.findMany.query({
      where: { level: 0 },
      include: rootCategoriesInclude,
      take: 6,
      orderBy: { name: "asc" }
    });
    featuredCategories = categoriesResponse ?? [];
    const partsResponse = await trpcClient.crud.part.findMany.query({
      include: partListInclude,
      take: 8,
      orderBy: { updatedAt: "desc" }
    });
    recentParts = partsResponse ?? [];
    const [partsCount, categoriesCount, brandsCount] = await Promise.all([
      trpcClient.crud.part.count.query(),
      trpcClient.crud.partCategory.count.query(),
      trpcClient.crud.brand.count.query()
    ]);
    stats = {
      parts: partsCount || 0,
      categories: categoriesCount || 0,
      brands: brandsCount || 0
    };
  } catch (error) {
    console.error("Error loading homepage data:", error);
  }
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "\u0413\u043B\u0430\u0432\u043D\u0430\u044F", "description": "PartTec3 - \u043F\u0440\u043E\u0444\u0435\u0441\u0441\u0438\u043E\u043D\u0430\u043B\u044C\u043D\u044B\u0439 \u043A\u0430\u0442\u0430\u043B\u043E\u0433 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u044B\u0445 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439 \u0434\u043B\u044F \u0442\u0435\u0445\u043D\u0438\u043A\u0438" }, { "default": async ($$result2) => renderTemplate` ${renderComponent($$result2, "TrpcProvider", TrpcProvider, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/providers/TrpcProvider", "client:component-export": "TrpcProvider" }, { "default": async ($$result3) => renderTemplate`  ${maybeRenderHead()}<section class="w-full py-12 md:py-24 lg:py-32 xl:py-48 bg-gradient-to-b from-background to-muted/20"> <div class="container px-4 md:mx-auto"> <div class="flex flex-col items-center space-y-4 text-center"> <div class="space-y-2"> <h1 class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
Каталог взаимозаменяемых запчастей
</h1> <p class="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
Профессиональная система поиска идентичных и совместимых запчастей от различных производителей
</p> </div> <div class="w-full max-w-md space-y-2"> ${renderComponent($$result3, "SearchForm", SearchForm, { "client:load": true, "placeholder": "\u041D\u0430\u0439\u0442\u0438 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u044C...", "client:component-hydration": "load", "client:component-path": "@/components/catalog/SearchForm", "client:component-export": "SearchForm" })} </div> </div> </div> </section>  <section class="w-full py-12 md:py-24 lg:py-32 bg-muted/20"> <div class="container px-4 md:mx-auto"> <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center"> <div class="space-y-2"> <h3 class="text-3xl font-bold">${stats.parts.toLocaleString("ru-RU")}</h3> <p class="text-muted-foreground">Групп запчастей</p> </div> <div class="space-y-2"> <h3 class="text-3xl font-bold">${stats.categories.toLocaleString("ru-RU")}</h3> <p class="text-muted-foreground">Категорий</p> </div> <div class="space-y-2"> <h3 class="text-3xl font-bold">${stats.brands.toLocaleString("ru-RU")}</h3> <p class="text-muted-foreground">Брендов</p> </div> </div> </div> </section>  ${featuredCategories.length > 0 && renderTemplate`<section class="w-full py-12 md:py-24 lg:py-32"> <div class="container px-4 md:mx-auto"> <div class="flex flex-col items-center space-y-4 text-center"> <div class="space-y-2"> <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
Основные категории
</h2> <p class="mx-auto max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
Выберите категорию для поиска нужных запчастей
</p> </div> </div> <div class="mx-auto grid max-w-5xl grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8"> ${featuredCategories.map((category) => renderTemplate`${renderComponent($$result3, "CategoryCard", CategoryCard, { "category": category, "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/catalog/CategoryCard", "client:component-export": "CategoryCard" })}`)} </div> <div class="flex justify-center mt-8"> <a href="/categories" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
Все категории
</a> </div> </div> </section>`} ${recentParts.length > 0 && renderTemplate`<section class="w-full py-12 md:py-24 lg:py-32 bg-muted/20"> <div class="container px-4 md:mx-auto"> <div class="flex flex-col items-center space-y-4 text-center"> <div class="space-y-2"> <h2 class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
Недавно обновленные
</h2> <p class="mx-auto max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
Последние изменения в каталоге запчастей
</p> </div> </div> <div class="mx-auto grid max-w-6xl grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-8"> ${recentParts.map((part) => renderTemplate`${renderComponent($$result3, "PartCard", PartCard, { "part": part, "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/catalog/PartCard", "client:component-export": "PartCard" })}`)} </div> <div class="flex justify-center gap-4 mt-8"> <a href="/catalog" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-9 px-4 py-2">
Открыть каталог
</a> <a href="/catalog-search" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
Профессиональный поиск
</a> </div> </div> </section>`}` })} ` })}`;
}, "D:/Dev/parttec/site/src/pages/index.astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/index.astro";
const $$url = "";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
