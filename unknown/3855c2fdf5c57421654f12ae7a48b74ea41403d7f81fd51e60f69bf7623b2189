import { jsx } from 'react/jsx-runtime';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { createTRPCClient, loggerLink, httpBatchLink } from '@trpc/client';
import { useState } from 'react';
import superjson from 'superjson';
import { createTRPCReact } from '@trpc/react-query';

const baseURL$1 = "http://localhost:3000";
const trpc = createTRPCReact();
const trpcClient = createTRPCClient({
  links: [
    loggerLink({
      enabled: () => false
    }),
    httpBatchLink({
      url: `${baseURL$1}/trpc`,
      transformer: superjson,
      fetch: (url, options) => fetch(url, {
        ...options,
        credentials: "include"
      })
    })
  ]
});

const baseURL = "http://localhost:3000";
function TrpcProvider({ children }) {
  const [queryClient] = useState(
    () => new QueryClient({
      defaultOptions: {
        queries: {
          staleTime: 3e4,
          gcTime: 5 * 6e4,
          retry: 1,
          refetchOnWindowFocus: false,
          refetchOnReconnect: true
        },
        mutations: {
          retry: 1
        }
      }
    })
  );
  const [trpcClient] = useState(
    () => trpc.createClient({
      links: [
        loggerLink({
          enabled: () => false
        }),
        httpBatchLink({
          url: `${baseURL}/trpc`,
          transformer: superjson,
          fetch: (url, options) => fetch(url, {
            ...options,
            credentials: "include"
          })
        })
      ]
    })
  );
  return /* @__PURE__ */ jsx(trpc.Provider, { client: trpcClient, queryClient, children: /* @__PURE__ */ jsx(QueryClientProvider, { client: queryClient, children }) });
}

export { TrpcProvider as T, trpc as a, trpcClient as t };
