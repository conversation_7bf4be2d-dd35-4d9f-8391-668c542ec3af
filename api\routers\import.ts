import { z } from 'zod'
import { router, expertProcedure } from '../trpc'
import { ExcelImportDryRunService } from '../services/excel/excel-import-dryrun.service'
import { ExcelImportExecuteService } from '../services/excel/excel-import-execute.service'

export const importRouter = router({
  dryRun: expertProcedure
    .input(z.object({ base64: z.string(), overrides: z.object({ createMissingRefs: z.boolean().optional(), onConflict: z.enum(['upsert','update_only','skip','error']).optional() }).optional() }))
    .mutation(async ({ ctx, input }) => {
      const buf = Buffer.from(input.base64, 'base64')
      const service = new ExcelImportDryRunService(ctx.db)
      const result = await service.dryRun(buf, input.overrides)
      return result
    }),
  execute: expertProcedure
    .input(z.object({ base64: z.string(), overrides: z.object({ createMissingRefs: z.boolean().optional(), onConflict: z.enum(['upsert','update_only','skip','error']).optional() }).optional() }))
    .mutation(async ({ ctx, input }) => {
      const buf = Buffer.from(input.base64, 'base64')
      const service = new ExcelImportExecuteService(ctx.db)
      const result = await service.execute(buf, input.overrides)
      return result
    }),
})

export type ImportRouter = typeof importRouter

