"use client"
import { cn } from "@/lib/utils"
import { CheckCircle, Info, AlertTriangle, AlertCircle } from "lucide-react"

interface StatusBadgeProps {
  status: "EXACT_MATCH" | "MATCH_WITH_NOTES" | "REQUIRES_MODIFICATION" | "PARTIAL_MATCH"
  size?: "sm" | "md" | "lg"
  showIcon?: boolean
  className?: string
}

const statusConfig = {
  EXACT_MATCH: {
    label: "Точное совпадение",
    icon: CheckCircle,
    className: "status-exact",
  },
  MATCH_WITH_NOTES: {
    label: "С примечаниями",
    icon: Info,
    className: "status-notes",
  },
  REQUIRES_MODIFICATION: {
    label: "Требует доработки",
    icon: AlertTriangle,
    className: "status-modification",
  },
  PARTIAL_MATCH: {
    label: "Частичное совпадение",
    icon: AlertCircle,
    className: "status-partial",
  },
}

export function StatusBadge({ status, size = "md", showIcon = true, className }: StatusBadgeProps) {
  const config = statusConfig[status]
  const Icon = config.icon

  return (
    <div
      className={cn(
        "inline-flex items-center gap-1.5 rounded-full border px-2.5 py-0.5 font-medium transition-colors",
        config.className,
        {
          "text-xs": size === "sm",
          "text-sm": size === "md",
          "text-base": size === "lg",
        },
        className,
      )}
    >
      {showIcon && <Icon className="h-3.5 w-3.5" />}
      <span>{config.label}</span>
    </div>
  )
}
