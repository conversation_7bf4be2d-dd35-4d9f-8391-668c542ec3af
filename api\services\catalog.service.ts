import type { Context } from '../trpc'
import { z } from 'zod'

// Входы через Zod (ZenStack/Prisma)
import { PartInputSchema } from '../generated/zod/input/PartInput.schema'
import { PartCategoryInputSchema } from '../generated/zod/input/PartCategoryInput.schema'
import { BrandInputSchema } from '../generated/zod/input/BrandInput.schema'

// DTO вынесен в отдельный модуль схем
import { PartListItemDto } from '../schemas/catalog'

export type PartFindManyInput = typeof PartInputSchema.findMany._type
export type PartCountInput = typeof PartInputSchema.count._type
export type CategoryFindManyInput = typeof PartCategoryInputSchema.findMany._type
export type BrandFindManyInput = typeof BrandInputSchema.findMany._type

export class CatalogService {
  static async listParts(db: Context['db'], input: PartFindManyInput) {
    const rows = await db.part.findMany(input as any)
    return rows.map((r: unknown) => PartListItemDto.parse(r))
  }

  static async countParts(db: Context['db'], input?: PartCountInput) {
    return db.part.count(input as any)
  }

  static async listCategories(db: Context['db'], input?: CategoryFindManyInput) {
    return db.partCategory.findMany(input as any)
  }

  static async listBrands(db: Context['db'], input?: BrandFindManyInput) {
    return db.brand.findMany(input as any)
  }
}

