"use client"

import { useState } from "react"
import { ChevronDown, ChevronUp, X, Filter, Sliders, Search, Sparkles } from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { ModernCard, ModernCardContent, ModernCardHeader, ModernCardTitle } from "@/components/ui/modern-card"
import { ModernInput } from "@/components/ui/modern-input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { AttributeFilter } from "./AttributeFilter"
import { StatusBadge } from "@/components/ui/status-badge"
import type { SearchFilters, AttributeTemplate } from "../types/catalog"

interface ModernFiltersPanelProps {
  filters: SearchFilters
  setFilters: (filters: SearchFilters) => void
  activeFiltersCount: number
  onClearAll: () => void
  mockCategories: Array<{ id: number; name: string; slug: string; level: number; path: string }>
  mockBrands: Array<{ id: number; name: string; slug: string; isOem: boolean }>
  mockAttributeTemplates: AttributeTemplate[]
  availableAttributeValues: {
    values: Record<number, string[]>
    numericStats: Record<number, { min: number; max: number; avg: number }>
  }
}

export function ModernFiltersPanel({
  filters,
  setFilters,
  activeFiltersCount,
  onClearAll,
  mockCategories,
  mockBrands,
  mockAttributeTemplates,
  availableAttributeValues,
}: ModernFiltersPanelProps) {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    categories: true,
    brands: true,
    accuracy: true,
  })

  // Состояния поиска для каждой секции
  const [categorySearch, setCategorySearch] = useState("")
  const [brandSearch, setBrandSearch] = useState("")
  const [accuracySearch, setAccuracySearch] = useState("")

  const handleMultiSelect = (
    value: string | number,
    selected: (string | number)[],
    setter: (values: (string | number)[]) => void,
  ) => {
    if (selected.includes(value)) {
      setter(selected.filter((v) => v !== value))
    } else {
      setter([...selected, value])
    }
  }

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  const accuracyOptions = [
    { value: "EXACT_MATCH", label: "Точное совпадение" },
    { value: "MATCH_WITH_NOTES", label: "С примечаниями" },
    { value: "REQUIRES_MODIFICATION", label: "Требует доработки" },
    { value: "PARTIAL_MATCH", label: "Частичное совпадение" },
  ]

  // Фильтрация элементов по поисковым запросам
  const filteredCategories = mockCategories.filter((category) =>
    category.name.toLowerCase().includes(categorySearch.toLowerCase()),
  )

  const filteredBrands = mockBrands.filter((brand) => brand.name.toLowerCase().includes(brandSearch.toLowerCase()))

  const filteredAccuracyOptions = accuracyOptions.filter((option) =>
    option.label.toLowerCase().includes(accuracySearch.toLowerCase()),
  )

  return (
    <div className="w-72 border-r border-border-strong bg-surface/50">
      <div className="sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto">
        <div className="p-4 space-y-4">
          {/* Compact Header with Active Filters Counter */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Filter className="h-4 w-4 text-primary" />
                {activeFiltersCount > 0 && (
                  <div className="absolute -top-1 -right-1 h-3 w-3 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">
                    {activeFiltersCount}
                  </div>
                )}
              </div>
              <div>
                <h2 className="font-semibold text-sm">Фильтры</h2>
                {activeFiltersCount > 0 && (
                  <p className="text-xs text-muted-foreground">{activeFiltersCount} активных</p>
                )}
              </div>
            </div>
            {activeFiltersCount > 0 && (
              <ModernButton
                variant="outline"
                size="sm"
                onClick={onClearAll}
                className="h-7 px-2 text-xs gap-1 hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30"
              >
                <X className="h-3 w-3" />
                Очистить
              </ModernButton>
            )}
          </div>

          {/* Compact Active Filters Summary */}
          {activeFiltersCount > 0 && (
            <ModernCard variant="glass" className="border-primary/20 bg-primary/5">
              <ModernCardContent className="p-3">
                <div className="flex items-center gap-2 mb-2">
                  <Sparkles className="h-3 w-3 text-primary" />
                  <span className="text-xs font-medium">Активные фильтры</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {filters.categoryIds.length > 0 && (
                    <Badge variant="secondary" className="text-xs h-5">
                      Категории: {filters.categoryIds.length}
                    </Badge>
                  )}
                  {filters.brandIds.length > 0 && (
                    <Badge variant="secondary" className="text-xs h-5">
                      Бренды: {filters.brandIds.length}
                    </Badge>
                  )}
                  {filters.accuracyLevels.length > 0 && (
                    <Badge variant="secondary" className="text-xs h-5">
                      Точность: {filters.accuracyLevels.length}
                    </Badge>
                  )}
                  {Object.keys(filters.attributeFilters).length > 0 && (
                    <Badge variant="secondary" className="text-xs h-5">
                      Параметры: {Object.keys(filters.attributeFilters).length}
                    </Badge>
                  )}
                  {filters.isOemOnly && (
                    <Badge variant="secondary" className="text-xs h-5">
                      Только OEM
                    </Badge>
                  )}
                </div>
              </ModernCardContent>
            </ModernCard>
          )}

          {/* Categories */}
          <ModernCard variant="default">
            <Collapsible open={expandedSections.categories} onOpenChange={() => toggleSection("categories")}>
              <CollapsibleTrigger asChild>
                <ModernCardHeader className="cursor-pointer p-3 hover:bg-accent/5 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <ModernCardTitle className="text-sm">Категории</ModernCardTitle>
                      {filters.categoryIds.length > 0 && (
                        <Badge variant="default" className="h-4 w-4 rounded-full p-0 text-xs">
                          {filters.categoryIds.length}
                        </Badge>
                      )}
                    </div>
                    {expandedSections.categories ? (
                      <ChevronUp className="h-3 w-3 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-3 w-3 text-muted-foreground" />
                    )}
                  </div>
                </ModernCardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <ModernCardContent className="p-3 pt-0 space-y-2">
                  {/* Search */}
                  {mockCategories.length > 5 && (
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground" />
                      <ModernInput
                        placeholder="Поиск категорий..."
                        value={categorySearch}
                        onChange={(e) => setCategorySearch(e.target.value)}
                        className="pl-7 h-7 text-xs"
                        variant="ghost"
                      />
                    </div>
                  )}

                  {filteredCategories.map((category) => (
                    <div key={category.id} className="flex items-center space-x-2 group">
                      <Checkbox
                        id={`category-${category.id}`}
                        checked={filters.categoryIds.includes(category.id)}
                        onCheckedChange={() =>
                          handleMultiSelect(category.id, filters.categoryIds, (values) =>
                            setFilters({ ...filters, categoryIds: values as number[] }),
                          )
                        }
                        className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"
                      />
                      <Label
                        htmlFor={`category-${category.id}`}
                        className="text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors"
                      >
                        {category.name}
                      </Label>
                    </div>
                  ))}

                  {filteredCategories.length === 0 && categorySearch && (
                    <p className="text-xs text-muted-foreground text-center py-1">
                      Ничего не найдено по запросу "{categorySearch}"
                    </p>
                  )}
                </ModernCardContent>
              </CollapsibleContent>
            </Collapsible>
          </ModernCard>

          {/* Brands */}
          <ModernCard variant="default">
            <Collapsible open={expandedSections.brands} onOpenChange={() => toggleSection("brands")}>
              <CollapsibleTrigger asChild>
                <ModernCardHeader className="cursor-pointer p-3 hover:bg-accent/5 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <ModernCardTitle className="text-sm">Производители</ModernCardTitle>
                      {(filters.brandIds.length > 0 || filters.isOemOnly) && (
                        <Badge variant="default" className="h-4 w-4 rounded-full p-0 text-xs">
                          {filters.brandIds.length + (filters.isOemOnly ? 1 : 0)}
                        </Badge>
                      )}
                    </div>
                    {expandedSections.brands ? (
                      <ChevronUp className="h-3 w-3 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-3 w-3 text-muted-foreground" />
                    )}
                  </div>
                </ModernCardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <ModernCardContent className="p-3 pt-0 space-y-2">
                  {/* OEM Filter */}
                  <div className="flex items-center space-x-2 p-2 rounded bg-accent/5 border border-border/30">
                    <Checkbox
                      id="oem-only"
                      checked={filters.isOemOnly}
                      onCheckedChange={(checked) => setFilters({ ...filters, isOemOnly: !!checked })}
                      className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"
                    />
                    <Label htmlFor="oem-only" className="text-xs cursor-pointer font-medium">
                      Только OEM производители
                    </Label>
                  </div>

                  <Separator className="bg-border/30" />

                  {/* Search */}
                  {mockBrands.length > 5 && (
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground" />
                      <ModernInput
                        placeholder="Поиск брендов..."
                        value={brandSearch}
                        onChange={(e) => setBrandSearch(e.target.value)}
                        className="pl-7 h-7 text-xs"
                        variant="ghost"
                      />
                    </div>
                  )}

                  {filteredBrands.map((brand) => (
                    <div key={brand.id} className="flex items-center space-x-2 group">
                      <Checkbox
                        id={`brand-${brand.id}`}
                        checked={filters.brandIds.includes(brand.id)}
                        onCheckedChange={() =>
                          handleMultiSelect(brand.id, filters.brandIds, (values) =>
                            setFilters({ ...filters, brandIds: values as number[] }),
                          )
                        }
                        className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"
                      />
                      <Label
                        htmlFor={`brand-${brand.id}`}
                        className="text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors flex items-center gap-1"
                      >
                        {brand.name}
                        {brand.isOem && (
                          <Badge variant="outline" className="text-xs h-4">
                            OEM
                          </Badge>
                        )}
                      </Label>
                    </div>
                  ))}

                  {filteredBrands.length === 0 && brandSearch && (
                    <p className="text-xs text-muted-foreground text-center py-1">
                      Ничего не найдено по запросу "{brandSearch}"
                    </p>
                  )}
                </ModernCardContent>
              </CollapsibleContent>
            </Collapsible>
          </ModernCard>

          {/* Accuracy Levels */}
          <ModernCard variant="default">
            <Collapsible open={expandedSections.accuracy} onOpenChange={() => toggleSection("accuracy")}>
              <CollapsibleTrigger asChild>
                <ModernCardHeader className="cursor-pointer p-3 hover:bg-accent/5 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <ModernCardTitle className="text-sm">Точность применимости</ModernCardTitle>
                      {filters.accuracyLevels.length > 0 && (
                        <Badge variant="default" className="h-4 w-4 rounded-full p-0 text-xs">
                          {filters.accuracyLevels.length}
                        </Badge>
                      )}
                    </div>
                    {expandedSections.accuracy ? (
                      <ChevronUp className="h-3 w-3 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-3 w-3 text-muted-foreground" />
                    )}
                  </div>
                </ModernCardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <ModernCardContent className="p-3 pt-0 space-y-2">
                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground" />
                    <ModernInput
                      placeholder="Поиск по точности..."
                      value={accuracySearch}
                      onChange={(e) => setAccuracySearch(e.target.value)}
                      className="pl-7 h-7 text-xs"
                      variant="ghost"
                    />
                  </div>

                  {filteredAccuracyOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2 group">
                      <Checkbox
                        id={`accuracy-${option.value}`}
                        checked={filters.accuracyLevels.includes(option.value)}
                        onCheckedChange={() =>
                          handleMultiSelect(option.value, filters.accuracyLevels, (values) =>
                            setFilters({ ...filters, accuracyLevels: values as string[] }),
                          )
                        }
                        className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"
                      />
                      <Label
                        htmlFor={`accuracy-${option.value}`}
                        className="text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors"
                      >
                        <StatusBadge
                          status={option.value as any}
                          size="sm"
                          className="border-0 bg-transparent p-0 text-xs font-normal"
                        />
                      </Label>
                    </div>
                  ))}

                  {filteredAccuracyOptions.length === 0 && accuracySearch && (
                    <p className="text-xs text-muted-foreground text-center py-1">
                      Ничего не найдено по запросу "{accuracySearch}"
                    </p>
                  )}
                </ModernCardContent>
              </CollapsibleContent>
            </Collapsible>
          </ModernCard>

          {/* Advanced Filters */}
          <ModernCard variant="default">
            <Collapsible open={showAdvancedFilters} onOpenChange={setShowAdvancedFilters}>
              <CollapsibleTrigger asChild>
                <ModernCardHeader className="cursor-pointer p-3 hover:bg-accent/5 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Sliders className="h-3 w-3 text-primary" />
                      <ModernCardTitle className="text-sm">Технические параметры</ModernCardTitle>
                      {Object.keys(filters.attributeFilters).length > 0 && (
                        <Badge variant="default" className="h-4 w-4 rounded-full p-0 text-xs">
                          {Object.keys(filters.attributeFilters).length}
                        </Badge>
                      )}
                    </div>
                    {showAdvancedFilters ? (
                      <ChevronUp className="h-3 w-3 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-3 w-3 text-muted-foreground" />
                    )}
                  </div>
                </ModernCardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <ModernCardContent className="p-3 pt-0 space-y-3">
                  {mockAttributeTemplates.map((template) => (
                    <AttributeFilter
                      key={template.id}
                      template={template}
                      selectedValues={filters.attributeFilters[template.id]?.values || []}
                      numericRange={filters.attributeFilters[template.id]?.numericRange}
                      onValuesChange={(values) => {
                        setFilters({
                          ...filters,
                          attributeFilters: {
                            ...filters.attributeFilters,
                            [template.id]: {
                              ...filters.attributeFilters[template.id],
                              values,
                            },
                          },
                        })
                      }}
                      onRangeChange={(range) => {
                        setFilters({
                          ...filters,
                          attributeFilters: {
                            ...filters.attributeFilters,
                            [template.id]: {
                              ...filters.attributeFilters[template.id],
                              numericRange: range,
                            },
                          },
                        })
                      }}
                      availableValues={availableAttributeValues.values[template.id] || []}
                      numericStats={availableAttributeValues.numericStats[template.id]}
                    />
                  ))}
                </ModernCardContent>
              </CollapsibleContent>
            </Collapsible>
          </ModernCard>
        </div>
      </div>
    </div>
  )
}
