import{j as s}from"./jsx-runtime.D_zvdyIk.js";import{C as r,b as t,c as d,d as m,a as c}from"./card.DUNQ4BBf.js";import{B as i}from"./badge.BTnnEvrQ.js";import{n as x}from"./router.WLLD8StG.js";import"./index.GzzYzsTh.js";import"./utils.CBfrqCZ4.js";import"./index.3rXK4OIH.js";function N({category:e,onClick:l}){const n=()=>{l?l():x(`/catalog/categories/${e.slug}`)};return s.jsxs(r,{className:"cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]",onClick:n,children:[s.jsx(t,{className:"pb-3",children:s.jsxs("div",{className:"flex items-start justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs(d,{className:"text-lg line-clamp-2 flex items-center gap-2",children:[e.icon&&s.jsx("span",{className:"text-xl",children:e.icon}),e.name]}),e.description&&s.jsx(m,{className:"mt-1 line-clamp-2",children:e.description})]}),e.image&&s.jsx("img",{src:e.image.url,alt:e.name,className:"w-16 h-16 object-cover rounded-md ml-3"})]})}),s.jsxs(c,{className:"pt-0",children:[s.jsx("div",{className:"flex items-center justify-between",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsxs(i,{variant:"secondary",children:["Уровень ",e.level]}),e._count&&s.jsxs(i,{variant:"outline",children:[e._count.parts," запчастей"]})]})}),e.children&&e.children.length>0&&s.jsxs("div",{className:"mt-3 pt-3 border-t",children:[s.jsx("div",{className:"text-xs text-muted-foreground mb-2",children:"Подкатегории:"}),s.jsxs("div",{className:"flex flex-wrap gap-1",children:[e.children.slice(0,3).map(a=>s.jsx(i,{variant:"outline",className:"text-xs",children:a.name},a.id)),e.children.length>3&&s.jsxs(i,{variant:"outline",className:"text-xs",children:["+",e.children.length-3]})]})]})]})]})}export{N as CategoryCard};
