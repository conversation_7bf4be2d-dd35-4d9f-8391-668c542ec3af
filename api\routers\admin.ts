import { z } from 'zod'
import { RoleSchema } from '../generated/zod/enums/Role.schema'
import { router, expertProcedure } from '../trpc'
import type { Context } from '../trpc'
import { AdminService } from '../services/admin.service'

/**
 * Административный tRPC роутер для управления пользователями
 * Использует better-auth admin plugin API
 * Доступен только пользователям с ролью ADMIN (админы)
 */
export const adminRouter = router({
  
  // =====================================================
  // ДАШБОРД АДМИНКИ
  // =====================================================
  getDashboard: expertProcedure
    .input(z.object({
      rangeDays: z.number().min(1).max(90).default(30)
    }))
    .query(async ({ input }: { input: { rangeDays: number } }) => AdminService.getDashboard(input)),
  
  // =====================================================
  // УПРАВЛЕНИЕ ПОЛЬЗОВАТЕЛЯМИ
  // =====================================================
  
  /**
   * Создание нового пользователя администратором
   */
  createUser: expertProcedure
    .input(z.object({
      name: z.string().min(1, 'Имя обязательно').max(100, 'Имя слишком длинное'),
      email: z.string().email('Некорректный email').max(255, 'Email слишком длинный'),
      password: z.string().min(6, 'Пароль должен содержать минимум 6 символов').max(100, 'Пароль слишком длинный'),
      role: RoleSchema.default('USER'),
      emailVerified: z.boolean().default(false),
      data: z.record(z.any()).optional() // Дополнительные поля
    }))
    .mutation(async ({ input, ctx }: { input: { name: string; email: string; password: string; role: any; emailVerified: boolean; data?: Record<string, unknown> }; ctx: Context }) => AdminService.createUser(ctx, input)),

  /**
   * Получение списка пользователей с фильтрацией и пагинацией
   */
  listUsers: expertProcedure
    .input(z.object({
      search: z.string().optional(),
      searchField: z.enum(['email', 'name']).default('email'),
      searchOperator: z.enum(['contains', 'starts_with', 'ends_with']).default('contains'),
      filterField: z.string().optional(),
      filterOperator: z.enum(['eq', 'contains', 'starts_with', 'ends_with']).default('eq'),
      filterValue: z.string().optional(),
      sortBy: z.string().default('createdAt'),
      sortDirection: z.enum(['asc', 'desc']).default('desc'),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0)
    }))
    .query(async ({ input, ctx }: { input: any; ctx: Context }) => AdminService.listUsers(ctx, input)),

  /**
   * Изменение роли пользователя
   */
  setUserRole: expertProcedure
    .input(z.object({
      userId: z.string(),
      role: RoleSchema
    }))
    .mutation(async ({ input, ctx }: { input: { userId: string; role: any }; ctx: Context }) => AdminService.setUserRole(ctx, input)),

  /**
   * Сброс пароля пользователя администратором
   */
  resetUserPassword: expertProcedure
    .input(z.object({
      userId: z.string(),
      newPassword: z.string().min(6, 'Пароль должен содержать минимум 6 символов').max(100, 'Пароль слишком длинный'),
      sendByEmail: z.boolean().default(false)
    }))
    .mutation(async ({ input, ctx }: { input: { userId: string; newPassword: string; sendByEmail: boolean }; ctx: Context }) => AdminService.resetUserPassword(ctx, input)),

  /**
   * Удаление пользователя
   */
  removeUser: expertProcedure
    .input(z.object({
      userId: z.string().uuid('Некорректный ID пользователя')
    }))
    .mutation(async ({ input, ctx }: { input: { userId: string }; ctx: Context }) => AdminService.removeUser(ctx, input)),

  // =====================================================
  // СИСТЕМА БАНОВ
  // =====================================================

  /**
   * Блокировка пользователя
   */
  banUser: expertProcedure
    .input(z.object({
      userId: z.string().uuid('Некорректный ID пользователя'),
      banReason: z.string().max(500, 'Причина блокировки слишком длинная').optional(),
      banExpiresIn: z.number().min(60, 'Минимальный срок блокировки - 1 минута').max(60 * 60 * 24 * 365, 'Максимальный срок блокировки - 1 год').optional()
    }))
    .mutation(async ({ input, ctx }: { input: { userId: string; banReason?: string; banExpiresIn?: number }; ctx: Context }) => AdminService.banUser(ctx, input)),

  /**
   * Разблокировка пользователя
   */
  unbanUser: expertProcedure
    .input(z.object({
      userId: z.string().uuid('Некорректный ID пользователя')
    }))
    .mutation(async ({ input, ctx }: { input: { userId: string }; ctx: Context }) => AdminService.unbanUser(ctx, input)),

  // =====================================================
  // УПРАВЛЕНИЕ СЕССИЯМИ
  // =====================================================

  /**
   * Получение списка сессий пользователя
   */
  listUserSessions: expertProcedure
    .input(z.object({
      userId: z.string()
    }))
    .query(async ({ input, ctx }: { input: { userId: string }; ctx: Context }) => AdminService.listUserSessions(ctx, input)),

  /**
   * Отзыв конкретной сессии пользователя
   */
  revokeUserSession: expertProcedure
    .input(z.object({
      sessionToken: z.string()
    }))
    .mutation(async ({ input, ctx }: { input: { sessionToken: string }; ctx: Context }) => AdminService.revokeUserSession(ctx, input)),

  /**
   * Отзыв всех сессий пользователя
   */
  revokeAllUserSessions: expertProcedure
    .input(z.object({
      userId: z.string()
    }))
    .mutation(async ({ input, ctx }: { input: { userId: string }; ctx: Context }) => AdminService.revokeAllUserSessions(ctx, input)),

  // =====================================================
  // IMPERSONATION (ИМПЕРСОНАЦИЯ)
  // =====================================================

  /**
   * Вход от имени пользователя (impersonation)
   */
  impersonateUser: expertProcedure
    .input(z.object({
      userId: z.string().uuid('Некорректный ID пользователя')
    }))
    .mutation(async ({ input, ctx }: { input: { userId: string }; ctx: Context }) => AdminService.impersonateUser(ctx, input)),

  /**
   * Выход из режима impersonation
   */
  stopImpersonating: expertProcedure
    .mutation(async ({ ctx }: { ctx: Context }) => AdminService.stopImpersonating(ctx)),

  // =====================================================
  // ПРОВЕРКА ПРАВ ДОСТУПА
  // =====================================================

  /**
   * Проверка прав пользователя
   */
  hasPermission: expertProcedure
    .input(
      z
        .object({
          permission: z.record(z.array(z.string())).optional(),
          permissions: z.record(z.array(z.string())).optional(),
          userId: z.string().optional(),
          role: z.string().optional(),
        })
        .refine(
          (i) =>
            (i.permission && Object.keys(i.permission).length > 0) ||
            (i.permissions && Object.keys(i.permissions).length > 0),
          { message: 'Передайте непустой permission или permissions' }
        )
    )
    .query(async ({ input, ctx }: { input: any; ctx: Context }) => AdminService.hasPermission(ctx, input)),

  // =====================================================
  // ЖУРНАЛ АУДИТА
  // =====================================================

  /**
   * Получение логов административных действий
   */
  getAuditLogs: expertProcedure
    .input(z.object({
      adminId: z.string().optional(),
      action: z.string().optional(),
      targetUserId: z.string().optional(),
      targetUserEmail: z.string().optional(),
      search: z.string().optional(),
      searchField: z.enum(['adminEmail', 'targetUserEmail']).optional(),
      success: z.boolean().optional(),
      dateFrom: z.date().optional(),
      dateTo: z.date().optional(),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
      sortBy: z.string().default('createdAt'),
      sortDirection: z.enum(['asc', 'desc']).default('desc')
    }))
    .query(async ({ input, ctx }: { input: any; ctx: Context }) => AdminService.getAuditLogs(ctx, input)),

  /**
   * Получение статистики административных действий
   */
  getAuditStats: expertProcedure
    .input(z.object({
      adminId: z.string().optional()
    }))
    .query(async ({ input, ctx }: { input: any; ctx: Context }) => AdminService.getAuditStats(ctx, input))
})
