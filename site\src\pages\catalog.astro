---
import MainLayout from "../layouts/MainLayout.astro";
import { trpcClient } from "@/lib/trpc";
import { TrpcProvider } from "@/components/providers/TrpcProvider";
import { SearchForm } from "@/components/catalog/SearchForm";
import { PartCard } from "@/components/catalog/PartCard";
import { partListInclude, rootCategoriesInclude, brandListInclude } from "@/lib/queries";
import { buildPartWhere } from "@/lib/catalog";

// Получаем параметры поиска из URL
const searchParams = Astro.url.searchParams;
const searchQuery = searchParams.get('search') || '';
const categoryId = searchParams.get('category') ? parseInt(searchParams.get('category')!) : undefined;
const brandId = searchParams.get('brand') ? parseInt(searchParams.get('brand')!) : undefined;
const page = parseInt(searchParams.get('page') || '1');
const limit = 20;

// Загружаем данные каталога
import type { PartListItem, PartCategoryItem, BrandItem } from "@/types/catalog";
let parts: PartListItem[] = [];
let totalCount = 0;
let categories: PartCategoryItem[] = [];
let brands: BrandItem[] = [];

try {
  // Получаем запчасти
  const whereClause = buildPartWhere({ query: searchQuery, categoryId, brandId });

  const partsResponse = await trpcClient.crud.part.findMany.query({
    where: whereClause,
    include: partListInclude,
    take: limit,
    skip: (page - 1) * limit,
    orderBy: { updatedAt: 'desc' }
  });
  parts = (partsResponse ?? []) as unknown as PartListItem[];

  // Получаем общее количество
  totalCount = await trpcClient.crud.part.count.query({ where: whereClause });

  // Получаем категории для фильтра
  const categoriesResponse = await trpcClient.crud.partCategory.findMany.query({
    where: { level: 0 },
    include: rootCategoriesInclude,
    orderBy: { name: 'asc' }
  });
  categories = categoriesResponse || [];

  // Получаем бренды для фильтра
  const brandsResponse = await trpcClient.crud.brand.findMany.query({
    include: brandListInclude,
    orderBy: { name: 'asc' },
    take: 50
  });
  brands = brandsResponse || [];

} catch (error) {
  console.error('Error loading catalog data:', error);
}

const totalPages = Math.ceil(totalCount / limit);

import { navigate } from 'astro:transitions/client';
const handleSearch = (query: string) => {
  const params = new URLSearchParams();
  if (query.trim()) params.set('search', query);
  if (categoryId) params.set('category', categoryId.toString());
  if (brandId) params.set('brand', brandId.toString());
  navigate(`/catalog?${params.toString()}`);
};
---

<MainLayout title="Каталог запчастей" description="Поиск и просмотр каталога взаимозаменяемых запчастей">
  <TrpcProvider client:load>
    <div class="container mx-auto px-4 py-8">
      <!-- Заголовок и поиск -->
      <div class="flex flex-col space-y-4 mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 class="text-3xl font-bold tracking-tight">Каталог запчастей</h1>
            <p class="text-muted-foreground">
              {totalCount.toLocaleString('ru-RU')} групп запчастей найдено
            </p>
          </div>
          <SearchForm client:load onSearch={handleSearch} defaultValue={searchQuery} />
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Боковая панель фильтров -->
        <div class="lg:col-span-1">
          <div class="space-y-6">
            <!-- Фильтр по категориям -->
            {categories.length > 0 && (
              <div>
                <h3 class="font-semibold mb-3">Категории</h3>
                <div class="space-y-2">
                  <a 
                    href="/catalog" 
                    class={`block px-3 py-2 text-sm rounded-md transition-colors ${!categoryId ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
                  >
                    Все категории
                  </a>
                  {categories.map((category) => (
                    <a 
                      href={`/catalog?category=${category.id}${searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : ''}`}
                      class={`block px-3 py-2 text-sm rounded-md transition-colors ${categoryId === category.id ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
                    >
                      {category.name}
                      {category._count && (
                        <span class="text-xs opacity-70 ml-1">({category._count.parts})</span>
                      )}
                    </a>
                  ))}
                </div>
              </div>
            )}

            <!-- Фильтр по брендам -->
            {brands.length > 0 && (
              <div>
                <h3 class="font-semibold mb-3">Бренды</h3>
                <div class="space-y-2 max-h-64 overflow-y-auto">
                  <a 
                    href={`/catalog${searchQuery ? `?search=${encodeURIComponent(searchQuery)}` : ''}${categoryId ? `${searchQuery ? '&' : '?'}category=${categoryId}` : ''}`}
                    class={`block px-3 py-2 text-sm rounded-md transition-colors ${!brandId ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
                  >
                    Все бренды
                  </a>
                  {brands.slice(0, 20).map((brand) => (
                    <a 
                      href={`/catalog?brand=${brand.id}${searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : ''}${categoryId ? `&category=${categoryId}` : ''}`}
                      class={`block px-3 py-2 text-sm rounded-md transition-colors ${brandId === brand.id ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
                    >
                      {brand.name}
                      {brand._count && (
                        <span class="text-xs opacity-70 ml-1">({brand._count.catalogItems})</span>
                      )}
                    </a>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <!-- Основной контент -->
        <div class="lg:col-span-3">
          {parts.length > 0 ? (
            <div class="space-y-6">
              <!-- Сетка запчастей -->
              <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {parts.map((part) => (
                  <PartCard part={part} client:load />
                ))}
              </div>

              <!-- Пагинация -->
              {totalPages > 1 && (
                <div class="flex justify-center space-x-2">
                  {page > 1 && (
                    <a 
                      href={`/catalog?page=${page - 1}${searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : ''}${categoryId ? `&category=${categoryId}` : ''}${brandId ? `&brand=${brandId}` : ''}`}
                      class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
                    >
                      Предыдущая
                    </a>
                  )}
                  
                  <span class="inline-flex items-center justify-center rounded-md text-sm font-medium h-9 px-4 py-2 bg-primary text-primary-foreground">
                    {page} из {totalPages}
                  </span>
                  
                  {page < totalPages && (
                    <a 
                      href={`/catalog?page=${page + 1}${searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : ''}${categoryId ? `&category=${categoryId}` : ''}${brandId ? `&brand=${brandId}` : ''}`}
                      class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
                    >
                      Следующая
                    </a>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div class="text-center py-12">
              <h3 class="text-lg font-semibold mb-2">Запчасти не найдены</h3>
              <p class="text-muted-foreground mb-4">
                Попробуйте изменить параметры поиска или фильтры
              </p>
              <a 
                href="/catalog" 
                class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-9 px-4 py-2"
              >
                Сбросить фильтры
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  </TrpcProvider>
</MainLayout>
