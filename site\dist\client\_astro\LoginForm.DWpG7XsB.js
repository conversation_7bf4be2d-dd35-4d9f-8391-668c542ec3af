import{j as e}from"./jsx-runtime.D_zvdyIk.js";import{r as t}from"./index.GzzYzsTh.js";import{a as x}from"./auth-client.D86yjSFk.js";import{I as m}from"./input.BvZ2Z-in.js";import{B as f}from"./button.CYGhVy7y.js";import"./utils.CBfrqCZ4.js";import"./index.3rXK4OIH.js";function N(){const[a,c]=t.useState(""),[r,u]=t.useState(""),[o,l]=t.useState(!1),[n,i]=t.useState(null),d=async s=>{s.preventDefault(),i(null),l(!0);try{await x.signIn.email({email:a,password:r},{onError:p=>i(p.error.message??"Ошибка входа"),onSuccess:()=>{window.location.href="/account"}})}finally{l(!1)}};return e.jsxs("form",{className:"space-y-4",onSubmit:d,children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm",children:"Email"}),e.jsx(m,{type:"email",value:a,onChange:s=>c(s.target.value),required:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm",children:"Пароль"}),e.jsx(m,{type:"password",value:r,onChange:s=>u(s.target.value),required:!0})]}),n&&e.jsx("div",{className:"text-sm text-red-500",children:n}),e.jsx(f,{type:"submit",disabled:o,children:o?"Входим...":"Войти"})]})}export{N as default};
