import ExcelJS from 'exceljs'
import { z } from 'zod'
import type { Context } from '../../trpc'
import type { ImportMeta, ImportResult, ImportCounters, ConflictMode } from './types'

const metaSchema = z.object({
  version: z.number().optional(),
  modes: z.object({
    createMissingRefs: z.boolean().default(false),
    onConflict: z.enum(['upsert','update_only','skip','error']).default('upsert'),
  }).optional(),
  scope: z.object({ brandSlug: z.string().optional(), categorySlug: z.string().optional() }).optional(),
})

type RowObject = Record<string, unknown>

export class ExcelImportDryRunService {
  constructor(private db: Context['db']) {}

  async parseMeta(ws: ExcelJS.Worksheet) {
    const map: Record<string, unknown> = {}
    const headerRow = 1
    const keyCol = 1
    const valCol = 2
    ws.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber < headerRow + 1) return
      const key = String(row.getCell(keyCol).value ?? '')
      const val = String(row.getCell(valCol).value ?? '')
      if (!key) return
      try {
        map[key] = JSON.parse(val)
      } catch {
        map[key] = val
      }
    })
    return metaSchema.safeParse(map)
  }

  async dryRun(fileBuffer: Buffer, overrides?: { createMissingRefs?: boolean; onConflict?: ConflictMode }): Promise<ImportResult> {
    const wb = new ExcelJS.Workbook()
    await wb.xlsx.load(fileBuffer)

    const result: ImportResult = {
      perSheet: {},
      errors: [],
      warnings: [],
    }

    // META
    const meta = wb.getWorksheet('META')
    if (!meta) {
      result.errors.push({ sheet: 'META', rowIndex: 0, message: 'Лист META отсутствует' })
      return result
    }
    const metaParsed = await this.parseMeta(meta)
    if (!metaParsed.success) {
      result.errors.push({ sheet: 'META', rowIndex: 0, message: 'Ошибка чтения META: ' + metaParsed.error.message })
      return result
    }

    // Инициализируем счетчики по известным листам
    const knownSheets = [
      'Brand','PartCategory','AttributeGroup','AttributeTemplate',
      'AttributeSynonymGroup','AttributeSynonym',
      'Part','CatalogItem','EquipmentModel',
      'PartAttribute','CatalogItemAttribute','EquipmentModelAttribute',
      'PartApplicability','EquipmentApplicability'
    ]

    for (const s of knownSheets) {
      if (wb.getWorksheet(s)) {
        result.perSheet[s] = this.makeCounters()
      }
    }

    // Симулируем auto-create при dry-run (только увеличение rowsValid + warnings)
    const simulateCreate = overrides?.createMissingRefs ?? (metaParsed.data.modes?.createMissingRefs ?? false)
    if (simulateCreate) {
      // Если включено createMissingRefs — повысим rowsValid на листах, где в противном случае ссылки могут отсутствовать
      // Это предварительная симуляция; на Execute будет реальное создание
      result.warnings.push({ sheet: 'META', rowIndex: 0, message: 'Dry-run: createMissingRefs включен — отсутствующие ссылки будут созданы на Execute' })
    }

    // Простейшая валидация колонок-обязательных ключей на нескольких листах
    this.validateSheet(wb, 'Brand', ['slug'], result)
    this.validateSheet(wb, 'PartCategory', ['slug'], result)
    this.validateSheet(wb, 'AttributeGroup', ['name'], result)
    this.validateSheet(wb, 'AttributeTemplate', ['name','title','dataType'], result)
    this.validateSheet(wb, 'AttributeSynonymGroup', ['template.name','name'], result)
    this.validateSheet(wb, 'AttributeSynonym', ['group.name','value'], result)
    this.validateSheet(wb, 'CatalogItem', ['sku','brand.slug'], result)
    this.validateSheet(wb, 'Part', ['partCategory.slug','path'], result)
    this.validateSheet(wb, 'EquipmentModel', ['name'], result)
    this.validateSheet(wb, 'PartAttribute', ['partCategory.slug','part.path','template.name','value'], result)
    this.validateSheet(wb, 'CatalogItemAttribute', ['catalogItem.sku','brand.slug','template.name','value'], result)
    this.validateSheet(wb, 'EquipmentModelAttribute', ['equipmentModel.id','template.name','value'], result)
    this.validateSheet(wb, 'PartApplicability', ['partCategory.slug','part.path','catalogItem.sku','brand.slug'], result)
    this.validateSheet(wb, 'EquipmentApplicability', ['partCategory.slug','part.path','equipmentModel.id'], result)

    return result
  }

  private makeCounters(): ImportCounters {
    return { rowsSeen: 0, rowsValid: 0, created: 0, updated: 0, deleted: 0, skipped: 0, errorsCount: 0, warningsCount: 0 }
  }

  private validateSheet(wb: ExcelJS.Workbook, name: string, requiredCols: string[], result: ImportResult) {
    const ws = wb.getWorksheet(name)
    if (!ws) return

    const counters = result.perSheet[name]
    const header = (ws.getRow(1).values as any[]).map(v => String(v || '').trim())
    const colIndexByName = new Map<string, number>()
    header.forEach((h, idx) => colIndexByName.set(h, idx))

    // Проверка наличия необходимых колонок
    for (const rc of requiredCols) {
      if (!colIndexByName.has(rc)) {
        result.errors.push({ sheet: name, rowIndex: 0, message: `Отсутствует обязательная колонка: ${rc}` })
      }
    }

    // Подсчет строк
    ws.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      // Простейшая проверка: ключевые колонки не пустые
      let ok = true
      for (const rc of requiredCols) {
        const col = colIndexByName.get(rc) || 0
        const val = String(row.getCell(col).value ?? '').trim()
        if (!val) {
          ok = false
          result.errors.push({ sheet: name, rowIndex: rowNumber, message: `Пустое значение в колонке ${rc}` })
        }
      }
      if (ok) counters.rowsValid++
    })
  }
}

