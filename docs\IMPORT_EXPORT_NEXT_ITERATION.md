## Импорт/Экспорт каталога в Excel (xlsx): следующая итерация

Этот документ описывает задачи и план работ следующей итерации для функционала импорта/экспорта каталога в формате Excel (xlsx) для cpanel. Документ служит ориентиром для разработки, тестирования и ревью.

### 1) Краткое резюме текущего состояния
- Экспорт (exceljs):
  - Поддержаны листы: META, Brand, PartCategory, AttributeTemplate, Part, CatalogItem, EquipmentModel,
    PartAttribute, CatalogItemAttribute, EquipmentModelAttribute, PartApplicability, EquipmentApplicability.
- Импорт:
  - Dry‑run: базовая валидация обязательных колонок на всех листах, сбор counters и ошибок.
  - Execute: upsert для основных сущностей и атрибутов/применимостей, поддержка onConflict (upsert|update_only|skip|error),
    отчётный файл с колонками __result / __error.
- UI cpanel:
  - Страница /admin/import-export с выбором листов экспорта, загрузкой файла, Dry‑run и Execute, скачиванием отчёта.

### 2) Цели следующей итерации
- Надёжная валидация (dry‑run): строгие типы, enum‑ы, числа/даты, больше полезных сообщений.
- Полный охват справочников: AttributeGroup, AttributeSynonymGroup, AttributeSynonym (экспорт + импорт).
- История импортов и аудит: учёт операций, скачивание исходника и отчёта, counters, метаданные.
- Улучшения UX: превью листов, прогресс, удобные статусы, единый UI для counters.
- Экспорт пустых шаблонов (exportTemplate): быстрый старт загрузки.

### 3) Область работ
#### 3.1 Backend (tRPC + exceljs)
1. Dry‑run усиленный:
   - Валидация enum (AttributeDataType, AttributeUnit, SynonymCompatibilityLevel, ApplicabilityAccuracy).
   - Нормализация чисел (точка/запятая), безопасный парс дат, boolean.
   - Проверка уникальностей до execute (например, дубликаты по (sku, brandSlug) в одном файле).
   - Диагностика ссылок (FK) с чёткими подсказками: какая ссылка и в какой строке не резолвится.
2. Экспорт/импорт для синонимов и групп атрибутов:
   - AttributeGroup: иерархия, name — natural key.
   - AttributeSynonymGroup: (template.name + group.name), parent, canonicalValue, compatibilityLevel, notes.
   - AttributeSynonym: уникален в группе по value, опционально brand, персональный compatibilityLevel.
3. История импортов и аудит:
   - Таблица ImportJob: id, userId, startedAt/finishedAt, status, options (JSON), counters (JSON),
     sourceFile (path/hash), reportFile (path), errorsCount, warningsCount.
   - tRPC: import.history.list, import.history.get, import.history.downloadSource, import.history.downloadReport.
   - Сохранение исходного файла и отчёта в файловом хранилище (локально в /api/uploads/imports/*).
4. Execute улучшения:
   - Батч‑обработка (настраиваемый batchSize), транзакции per‑batch.
   - Единый код ошибок и формулировок для __error (например, E_NOT_FOUND_REF, E_DUPLICATE, E_ENUM, E_UNIQUE, E_PARSE_NUMBER).
   - Поддержка мягких delete (если понадобится сценарий replace со scope).
5. ExportTemplate:
   - Новый метод exportTemplate: формирует xlsx с листами и только заголовками колонок + 1 примерной строкой (комментарии значений).

#### 3.2 Frontend (cpanel)
1. UX импорт/экспорт:
   - Предпросмотр листов: отображение первых N строк, поиск по колонкам, подсветка проблемных значений.
   - Прогресс‑индикатор для execute (пошаговые статусы, counters по мере выполнения).
   - Упрощённая форма настроек (mode/onConflict/createMissingRefs) + подсказки.
2. История импортов:
   - Таблица: дата, пользователь, статус, ошибки/предупреждения, counters, кнопки скачать исходник и отчёт.
   - Детальная карточка задачи импорта с разбиением по листам.
3. Кнопка «Скачать шаблон» (exportTemplate) рядом с Экспорт/Импорт.

### 4) Контракты API (tRPC)
- importExport.exportXlsx(input): { include, filters?, meta? } → { fileName, base64 }
- import.exportTemplate(input?): { include? } → { fileName, base64 }
- import.dryRun({ base64 }) → { perSheet, errors[], warnings[] }
- import.execute({ base64 }) → { perSheet, errors[], warnings[], reportBase64 }
- import.history.list({ cursor?, limit? }) → { items: ImportJob[], nextCursor? }
- import.history.get({ id }) → ImportJob
- import.history.downloadSource({ id }) → { fileName, base64 }
- import.history.downloadReport({ id }) → { fileName, base64 }

Примечания:
- Доступ только ADMIN (expertProcedure).
- createMissingRefs по умолчанию false; onConflict по умолчанию upsert (можно переопределять в META).

### 5) Форматы листов (кратко)
- Общие служебные колонки: id?, __key?, __op?, __result? (out), __error? (out)
- Ссылки: field.id | field.__key | natural; приоритет id > __key > natural
- Ключевые natural‑keys:
  - Brand: slug
  - PartCategory: slug
  - AttributeTemplate: name
  - AttributeGroup: name
  - AttributeSynonymGroup: (template.name + name)
  - AttributeSynonym: (group + value)
  - Part: (partCategory.slug + path)
  - CatalogItem: (sku + brand.slug)
  - EquipmentModel: id или (name + brand.slug)
  - Атрибуты: (владелец + template.name)

### 6) Тест‑план
- Unit (backend):
  - Парсер META; конвертеры типов (number/boolean/date/enum); резолверы FK; маппинг ошибок.
- Integration (backend):
  - Dry‑run/execute на небольших наборах данных: happy path, отсутствующие ссылки, дубликаты, конфликт уникальности.
  - Атрибуты и применимости: создание/обновление, уникальные ключи.
  - Синонимы: группы/иерархия, совместимость уровней, бренд‑специфичные синонимы.
- E2E (ручная проверка в cpanel):
  - Экспорт → правка → Dry‑run → Execute → отчёт и counters соответствуют ожиданию.
  - История импортов: видна запись, скачиваются исходник и отчёт.

### 7) Риски и меры
- Большие файлы: батчи, потоковая обработка, лимиты по листам/строкам с предупреждением.
- Несовместимость версий шаблонов: META.version + schemaHash, early‑fail и понятное сообщение.
- Частично валидный файл: не прерывать весь процесс — помечать строки, суммировать ошибки.
- Replace‑сценарии: только со scope и авто‑экспортом бэкапа.

### 8) Критерии готовности (DoD)
- Экспорт/Импорт покрывают все заявленные листы, включая AttributeGroup/SynonymGroup/Synonym.
- Dry‑run выдаёт детальные ошибки с кодами; покрыт unit‑тестами.
- Execute стабильный на типовых кейсах; counters корректны; отчётные файлы корректно скачиваются.
- История импортов доступна в UI, есть скачивание исходника и отчёта.
- Документация обновлена (этот README, подсказки в UI, примеры шаблонов).

### 9) Развёртывание и окружение
- API: bun run dev (локально), порт 3000
- cpanel: стандартный dev‑скрипт (см. cpanel/README.md)
- Права доступа: только ADMIN
- Хранилище файлов: локально /api/uploads/imports/* (dev), прод — согласовать путь

### 10) Выпуск артефактов
- example.xlsx (минимальный набор с 1–2 строками на лист, валидный по схемам)
- template.xlsx (пустые листы с заголовками + одна примерная строка)
- Скриншоты UI (импорт/экспорт, история)

### 11) Чек‑лист исполнителя
- [ ] Экспорт/импорт AttributeGroup, AttributeSynonymGroup, AttributeSynonym
- [ ] Dry‑run: строгие enum/числа/даты, коды ошибок, проверки уникальностей
- [ ] Execute: батчи, аккуратные транзакции, расширенные ошибки
- [ ] История импортов: таблица, деталка, скачивание исходника и отчёта
- [ ] ExportTemplate эндпоинт
- [ ] Обновить документацию и положить шаблоны в репозиторий

### 12) Пример использования (tRPC) из фронтенда
- Экспорт:
  - trpc.importExport.exportXlsx.mutate({ include: { Brand: true, Part: true }, filters: { brandSlugs: ['cat', 'komatsu'] } })
- Импорт Dry‑run:
  - trpc.import.dryRun.mutate({ base64 })
- Импорт Execute:
  - trpc.import.execute.mutate({ base64 })
- История:
  - trpc.import.history.list({ limit: 20 })

---

Поддерживаем принципы: максимально прозрачный код, чистая архитектура, DRY/SOLID, строгая типизация и уважение к существующим API. createMissingRefs=false по умолчанию, гибкая привязка по natural keys и id/.__key, админ‑только доступ.

