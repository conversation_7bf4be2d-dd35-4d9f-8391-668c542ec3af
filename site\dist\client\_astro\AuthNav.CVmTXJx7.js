import{j as e}from"./jsx-runtime.D_zvdyIk.js";import"./index.GzzYzsTh.js";import{a as t}from"./auth-client.D86yjSFk.js";import{B as n}from"./button.CYGhVy7y.js";import"./index.3rXK4OIH.js";import"./utils.CBfrqCZ4.js";function u(){const{data:r,isPending:s}=t.useSession();return s?null:r?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("a",{href:"/account",className:"text-sm text-foreground/80 hover:underline",children:"Кабинет"}),e.jsx(n,{size:"sm",variant:"outline",onClick:async()=>{await t.signOut({fetchOptions:{onSuccess:()=>window.location.href="/"}})},children:"Выйти"})]}):e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("a",{href:"/login",className:"text-sm text-foreground/80 hover:underline",children:"Вход"}),e.jsx("a",{href:"/register",className:"text-sm text-foreground/80 hover:underline",children:"Регистрация"})]})}export{u as AuthNav};
