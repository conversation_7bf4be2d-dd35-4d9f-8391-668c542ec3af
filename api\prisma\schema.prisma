//////////////////////////////////////////////////////////////////////////////////////////////
// DO NOT MODIFY THIS FILE                                                                  //
// This file is automatically generated by ZenStack CLI and should not be manually updated. //
//////////////////////////////////////////////////////////////////////////////////////////////

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

enum AttributeDataType {
  STRING
  NUMBER
  BOOLEAN
  DATE
  JSON
}

enum AttributeUnit {
  MM
  INCH
  FT
  G
  KG
  T
  LB
  ML
  L
  GAL
  SEC
  MIN
  H
  PCS
  SET
  PAIR
  BAR
  PSI
  KW
  HP
  NM
  RPM
  C
  F
  PERCENT
}

enum ApplicabilityAccuracy {
  EXACT_MATCH
  MATCH_WITH_NOTES
  REQUIRES_MODIFICATION
  PARTIAL_MATCH
}

enum SynonymCompatibilityLevel {
  EXACT
  NEAR
  LEGACY
}

enum ProposalStatus {
  PENDING
  APPROVED
  REJECTED
  INVALIDATED
}

enum Role {
  GUEST
  USER
  SHOP
  ADMIN
}

model MediaAsset {
  id           Int           @id() @default(autoincrement())
  fileName     String
  mimeType     String
  fileSize     Int?
  url          String        @unique()
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt()
  part         Part?         @relation("PartImage")
  partCategory PartCategory? @relation("PartCategoryImage")
  parts        Part[]        @relation("PartMediaAssets")
  catalogItems CatalogItem[] @relation("CatalogItemMediaAssets")
  catalogItem  CatalogItem?  @relation("CatalogItemImage")

  @@index([mimeType])
  @@index([fileName])
}

model AttributeGroup {
  id          Int                 @id() @default(autoincrement())
  name        String              @unique()
  description String?
  templates   AttributeTemplate[]
  parent      AttributeGroup?     @relation("AttributeGroupHierarchy", fields: [parentId], references: [id])
  parentId    Int?
  children    AttributeGroup[]    @relation("AttributeGroupHierarchy")

  @@index([parentId])
}

model AttributeTemplate {
  id                    Int                       @id() @default(autoincrement())
  name                  String                    @unique()
  title                 String
  description           String?
  dataType              AttributeDataType         @default(STRING)
  unit                  AttributeUnit?
  isRequired            Boolean                   @default(false)
  minValue              Float?
  maxValue              Float?
  allowedValues         String[]
  tolerance             Float?                    @default(0)
  group                 AttributeGroup?           @relation(fields: [groupId], references: [id])
  groupId               Int?
  partAttributes        PartAttribute[]
  catalogItemAttributes CatalogItemAttribute[]
  equipmentAttributes   EquipmentModelAttribute[]
  synonymGroups         AttributeSynonymGroup[]
  createdAt             DateTime                  @default(now())
  updatedAt             DateTime                  @updatedAt()

  @@index([groupId])
}

model AttributeSynonymGroup {
  id                 Int                       @id() @default(autoincrement())
  name               String                    @unique()
  description        String?
  template           AttributeTemplate         @relation(fields: [templateId], references: [id], onDelete: Cascade)
  templateId         Int
  parent             AttributeSynonymGroup?    @relation("SynonymGroupHierarchy", fields: [parentId], references: [id])
  parentId           Int?
  children           AttributeSynonymGroup[]   @relation("SynonymGroupHierarchy")
  canonicalValue     String?
  synonyms           AttributeSynonym[]
  compatibilityLevel SynonymCompatibilityLevel @default(EXACT)
  notes              String?

  @@index([parentId])
  @@index([templateId])
}

model AttributeSynonym {
  id                 Int                        @id() @default(autoincrement())
  value              String
  group              AttributeSynonymGroup      @relation(fields: [groupId], references: [id], onDelete: Cascade)
  groupId            Int
  notes              String?
  brand              Brand?                     @relation(fields: [brandId], references: [id])
  brandId            Int?
  compatibilityLevel SynonymCompatibilityLevel?

  @@unique([groupId, value])
  @@index([value])
}

model PartAttribute {
  id           Int               @id() @default(autoincrement())
  value        String
  numericValue Float?
  part         Part              @relation(fields: [partId], references: [id], onDelete: Cascade)
  partId       Int
  template     AttributeTemplate @relation(fields: [templateId], references: [id], onDelete: Restrict)
  templateId   Int

  @@unique([partId, templateId])
  @@index([partId])
  @@index([templateId])
  @@index([value])
  @@index([numericValue])
}

model CatalogItemAttribute {
  id            Int               @id() @default(autoincrement())
  value         String
  numericValue  Float?
  catalogItem   CatalogItem       @relation(fields: [catalogItemId], references: [id], onDelete: Cascade)
  catalogItemId Int
  template      AttributeTemplate @relation(fields: [templateId], references: [id], onDelete: Restrict)
  templateId    Int

  @@unique([catalogItemId, templateId])
  @@index([catalogItemId])
  @@index([templateId])
  @@index([value])
  @@index([numericValue])
}

model EquipmentModelAttribute {
  id               Int               @id() @default(autoincrement())
  value            String
  numericValue     Float?
  equipmentModel   EquipmentModel    @relation(fields: [equipmentModelId], references: [id], onDelete: Cascade)
  equipmentModelId String
  template         AttributeTemplate @relation(fields: [templateId], references: [id], onDelete: Restrict)
  templateId       Int

  @@unique([equipmentModelId, templateId])
  @@index([equipmentModelId])
  @@index([templateId])
  @@index([value])
  @@index([numericValue])
}

model EquipmentApplicability {
  id               Int            @id() @default(autoincrement())
  part             Part           @relation(fields: [partId], references: [id], onDelete: Cascade)
  partId           Int
  equipmentModel   EquipmentModel @relation(fields: [equipmentModelId], references: [id], onDelete: Cascade)
  equipmentModelId String
  notes            String?

  @@unique([partId, equipmentModelId])
  @@index([equipmentModelId])
}

model Part {
  createdAt                DateTime                 @default(now())
  updatedAt                DateTime                 @updatedAt()
  id                       Int                      @id() @default(autoincrement())
  name                     String?
  parent                   Part?                    @relation("PartHierarchy", fields: [parentId], references: [id])
  parentId                 Int?
  children                 Part[]                   @relation("PartHierarchy")
  level                    Int                      @default(0)
  path                     String
  attributes               PartAttribute[]
  applicabilities          PartApplicability[]
  equipmentApplicabilities EquipmentApplicability[]
  matchingProposals        MatchingProposal[]
  aggregateSchemas         AggregateSchema[]
  schemaPositions          SchemaPosition[]
  imageId                  Int?                     @unique()
  image                    MediaAsset?              @relation("PartImage", fields: [imageId], references: [id], onDelete: SetNull)
  mediaAssets              MediaAsset[]             @relation("PartMediaAssets")
  partCategory             PartCategory             @relation(fields: [partCategoryId], references: [id])
  partCategoryId           Int

  @@index([parentId])
  @@index([path])
}

model PartApplicability {
  id            Int                   @id() @default(autoincrement())
  part          Part                  @relation(fields: [partId], references: [id])
  partId        Int
  catalogItem   CatalogItem           @relation(fields: [catalogItemId], references: [id])
  catalogItemId Int
  accuracy      ApplicabilityAccuracy @default(EXACT_MATCH)
  notes         String?

  @@unique([partId, catalogItemId])
  @@index([catalogItemId])
}

model CatalogItem {
  id                Int                    @id() @default(autoincrement())
  sku               String
  source            String?
  description       String?
  brand             Brand                  @relation(fields: [brandId], references: [id])
  brandId           Int
  isPublic          Boolean                @default(true)
  attributes        CatalogItemAttribute[]
  applicabilities   PartApplicability[]
  matchingProposals MatchingProposal[]
  imageId           Int?                   @unique()
  image             MediaAsset?            @relation("CatalogItemImage", fields: [imageId], references: [id], onDelete: SetNull)
  mediaAssets       MediaAsset[]           @relation("CatalogItemMediaAssets")

  @@unique([sku, brandId])
  @@index([brandId])
}

model MatchingProposal {
  id                 Int                   @id() @default(autoincrement())
  catalogItem        CatalogItem           @relation(fields: [catalogItemId], references: [id], onDelete: Cascade)
  catalogItemId      Int
  part               Part                  @relation(fields: [partId], references: [id], onDelete: Cascade)
  partId             Int
  accuracySuggestion ApplicabilityAccuracy @default(EXACT_MATCH)
  notesSuggestion    String?
  details            Json?
  status             ProposalStatus        @default(PENDING)
  createdAt          DateTime              @default(now())
  updatedAt          DateTime              @updatedAt()

  @@unique([catalogItemId, partId, status])
  @@index([status])
}

/// Иерархическая категория запчастей
/// Примеры: «Двигатель», «Система смазки», «Фильтры топлива» …
model PartCategory {
  id          Int            @id() @default(autoincrement())
  name        String
  slug        String         @unique()
  description String?
  parent      PartCategory?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  parentId    Int?
  children    PartCategory[] @relation("CategoryHierarchy")
  level       Int            @default(0)
  path        String
  icon        String?
  parts       Part[]
  imageId     Int?           @unique()
  image       MediaAsset?    @relation("PartCategoryImage", fields: [imageId], references: [id], onDelete: SetNull)
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt()

  @@index([parentId])
  @@index([slug])
}

model Brand {
  id                Int                @id() @default(autoincrement())
  name              String
  slug              String             @unique()
  country           String?
  isOem             Boolean            @default(false)
  catalogItems      CatalogItem[]
  equipmentModel    EquipmentModel[]
  attributeSynonyms AttributeSynonym[]

  @@index([isOem])
}

model EquipmentModel {
  id                  String                    @id() @default(uuid())
  createdAt           DateTime                  @default(now())
  updatedAt           DateTime                  @updatedAt()
  name                String
  partApplicabilities EquipmentApplicability[]
  brand               Brand?                    @relation(fields: [brandId], references: [id])
  brandId             Int?
  attributes          EquipmentModelAttribute[]

  @@index([brandId])
  @@map("equipment_model")
}

model AggregateSchema {
  id          String             @id() @default(uuid())
  name        String
  description String?
  part        Part?              @relation(fields: [partId], references: [id])
  partId      Int?
  imageUrl    String?
  imageWidth  Int?
  imageHeight Int?
  svgContent  String?
  isActive    Boolean            @default(true)
  sortOrder   Int                @default(0)
  positions   SchemaPosition[]
  annotations SchemaAnnotation[]
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt()

  @@index([isActive])
  @@map("aggregate_schema")
}

model SchemaPosition {
  id                String          @id() @default(uuid())
  schema            AggregateSchema @relation(fields: [schemaId], references: [id])
  schemaId          String
  part              Part            @relation(fields: [partId], references: [id])
  partId            Int
  positionNumber    String
  x                 Float
  y                 Float
  width             Float?
  height            Float?
  shape             String          @default("circle")
  color             String?
  label             String?
  quantity          Int             @default(1)
  isRequired        Boolean         @default(true)
  isHighlighted     Boolean         @default(false)
  installationOrder Int?
  notes             String?
  isVisible         Boolean         @default(true)
  sortOrder         Int             @default(0)
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt()

  @@unique([schemaId, partId])
  @@unique([schemaId, positionNumber])
  @@index([schemaId])
  @@index([partId])
  @@index([positionNumber])
  @@map("schema_position")
}

model SchemaAnnotation {
  id             String          @id() @default(uuid())
  schema         AggregateSchema @relation(fields: [schemaId], references: [id])
  schemaId       String
  x              Float
  y              Float
  width          Float?
  height         Float?
  text           String
  annotationType String          @default("note")
  color          String?
  fontSize       Int?            @default(12)
  strokeWidth    Int?            @default(1)
  opacity        Float?          @default(1.0)
  isVisible      Boolean         @default(true)
  sortOrder      Int             @default(0)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt()

  @@index([schemaId])
  @@map("schema_annotation")
}

model User {
  id            String    @id() @default(uuid())
  name          String?
  email         String    @unique()
  emailVerified Boolean   @default(false)
  image         String?
  role          Role      @default(USER)
  banned        Boolean   @default(false)
  banReason     String?
  banExpires    DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt()
  accounts      Account[]
  sessions      Session[]

  @@map("user")
}

model Account {
  id                    String    @id()
  accountId             String
  providerId            String
  userId                String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("account")
}

model Session {
  id             String   @id()
  expiresAt      DateTime
  token          String   @unique()
  createdAt      DateTime
  updatedAt      DateTime
  ipAddress      String?
  userAgent      String?
  userId         String
  impersonatedBy String?
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}
