import{j as e}from"./jsx-runtime.D_zvdyIk.js";import{r as i,R as qs,b as G}from"./index.GzzYzsTh.js";import{S as Ys,u as P,c as Ge,B as Xs}from"./button.CYGhVy7y.js";import{c as Qs}from"./index.3rXK4OIH.js";import{c as R}from"./utils.CBfrqCZ4.js";import{B as I}from"./badge.BTnnEvrQ.js";import{u as st,b as A,c as T,d as Zs,R as Js,e as pe,f as ie,g as ge,h as en,i as tn,j as sn,L as ee,C as te,F as Tt,S as At,a as ke,P as gt}from"./label.T9h-zJiu.js";import{c as M,S as ne}from"./search.C9RwVH3H.js";import{t as nn,T as rn}from"./TrpcProvider._sdBuAbb.js";import"./index.BSy9ry4M.js";/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const an=[["path",{d:"M8 3 4 7l4 4",key:"9rb6wj"}],["path",{d:"M4 7h16",key:"6tx8e3"}],["path",{d:"m16 21 4-4-4-4",key:"siv7j2"}],["path",{d:"M20 17H4",key:"h6l3hr"}]],on=M("arrow-left-right",an);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cn=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],ln=M("bell",cn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dn=[["path",{d:"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z",key:"yt0hxn"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}]],un=M("bolt",dn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mn=[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]],Ee=M("bot",mn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fn=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],hn=M("building",fn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xn=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]],pn=M("calculator",xn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gn=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],vn=M("chart-column",gn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bn=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],fe=M("chevron-down",bn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yn=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],vt=M("chevron-left",yn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jn=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],qe=M("chevron-right",jn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nn=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],he=M("chevron-up",Nn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wn=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],Cn=M("clock",wn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sn=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],Ye=M("download",Sn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kn=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],En=M("eye",kn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const In=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],se=M("file-text",In);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mn=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]],Rn=M("grid-3x3",Mn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dn=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],nt=M("image",Dn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tn=[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]],An=M("lightbulb",Tn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _n=[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]],On=M("list",_n);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pn=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"m21 3-7 7",key:"1l2asr"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M9 21H3v-6",key:"wtvkvv"}]],Fn=M("maximize-2",Pn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ln=[["path",{d:"M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719",key:"1sd12s"}]],zn=M("message-circle",Ln);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $n=[["path",{d:"m14 10 7-7",key:"oa77jy"}],["path",{d:"M20 10h-6V4",key:"mjg0md"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M4 14h6v6",key:"rmj7iw"}]],Bn=M("minimize-2",$n);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vn=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],Kn=M("save",Vn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wn=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],Un=M("send",Wn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hn=[["line",{x1:"4",x2:"4",y1:"21",y2:"14",key:"1p332r"}],["line",{x1:"4",x2:"4",y1:"10",y2:"3",key:"gb41h5"}],["line",{x1:"12",x2:"12",y1:"21",y2:"12",key:"hf2csr"}],["line",{x1:"12",x2:"12",y1:"8",y2:"3",key:"1kfi7u"}],["line",{x1:"20",x2:"20",y1:"21",y2:"16",key:"1lhrwl"}],["line",{x1:"20",x2:"20",y1:"12",y2:"3",key:"16vvfq"}],["line",{x1:"2",x2:"6",y1:"14",y2:"14",key:"1uebub"}],["line",{x1:"10",x2:"14",y1:"8",y2:"8",key:"1yglbp"}],["line",{x1:"18",x2:"22",y1:"16",y2:"16",key:"1jxqpz"}]],Gn=M("sliders-vertical",Hn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qn=[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]],bt=M("sparkles",qn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yn=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],_t=M("trending-up",Yn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xn=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],rt=M("x",Xn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qn=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],Ot=M("zap",Qn);/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zn=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]],Jn=M("zoom-in",Zn),er=Qs("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"gradient-primary text-primary-foreground shadow-sm hover:opacity-90"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),E=i.forwardRef(({className:t,variant:s,size:n,asChild:r=!1,...a},o)=>{const c=r?Ys:"button";return e.jsx(c,{className:R(er({variant:s,size:n,className:t})),ref:o,...a})});E.displayName="ModernButton";const O=i.forwardRef(({className:t,variant:s="default",...n},r)=>e.jsx("div",{ref:r,className:R("rounded-md border transition-all duration-200 animate-theme-transition",{"bg-card text-card-foreground border-border shadow-professional hover:shadow-elevated hover:border-border-strong":s==="default","bg-card text-card-foreground border-border-strong shadow-elevated hover:shadow-strong":s==="elevated","glass-effect text-card-foreground border-border":s==="glass"},t),...n}));O.displayName="ModernCard";const $=i.forwardRef(({className:t,...s},n)=>e.jsx("div",{ref:n,className:R("flex flex-col space-y-1 p-4 pb-2",t),...s}));$.displayName="ModernCardHeader";const B=i.forwardRef(({className:t,...s},n)=>e.jsx("h3",{ref:n,className:R("font-semibold leading-none tracking-tight",t),...s}));B.displayName="ModernCardTitle";const tr=i.forwardRef(({className:t,...s},n)=>e.jsx("p",{ref:n,className:R("text-sm text-muted-foreground leading-relaxed",t),...s}));tr.displayName="ModernCardDescription";const L=i.forwardRef(({className:t,...s},n)=>e.jsx("div",{ref:n,className:R("p-4 pt-0",t),...s}));L.displayName="ModernCardContent";const sr=i.forwardRef(({className:t,...s},n)=>e.jsx("div",{ref:n,className:R("flex items-center p-4 pt-0",t),...s}));sr.displayName="ModernCardFooter";var nr=qs[" useId ".trim().toString()]||(()=>{}),rr=0;function re(t){const[s,n]=i.useState(nr());return st(()=>{n(r=>r??String(rr++))},[t]),t||(s?`radix-${s}`:"")}function oe(t){const s=i.useRef(t);return i.useEffect(()=>{s.current=t}),i.useMemo(()=>(...n)=>s.current?.(...n),[])}function ar(t,s=globalThis?.document){const n=oe(t);i.useEffect(()=>{const r=a=>{a.key==="Escape"&&n(a)};return s.addEventListener("keydown",r,{capture:!0}),()=>s.removeEventListener("keydown",r,{capture:!0})},[n,s])}var or="DismissableLayer",Xe="dismissableLayer.update",ir="dismissableLayer.pointerDownOutside",cr="dismissableLayer.focusOutside",yt,Pt=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ft=i.forwardRef((t,s)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:a,onFocusOutside:o,onInteractOutside:c,onDismiss:l,...y}=t,d=i.useContext(Pt),[f,m]=i.useState(null),g=f?.ownerDocument??globalThis?.document,[,w]=i.useState({}),S=P(s,C=>m(C)),u=Array.from(d.layers),[x]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),N=u.indexOf(x),h=f?u.indexOf(f):-1,j=d.layersWithOutsidePointerEventsDisabled.size>0,v=h>=N,p=ur(C=>{const b=C.target,D=[...d.branches].some(F=>F.contains(b));!v||D||(a?.(C),c?.(C),C.defaultPrevented||l?.())},g),k=mr(C=>{const b=C.target;[...d.branches].some(F=>F.contains(b))||(o?.(C),c?.(C),C.defaultPrevented||l?.())},g);return ar(C=>{h===d.layers.size-1&&(r?.(C),!C.defaultPrevented&&l&&(C.preventDefault(),l()))},g),i.useEffect(()=>{if(f)return n&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(yt=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(f)),d.layers.add(f),jt(),()=>{n&&d.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=yt)}},[f,g,n,d]),i.useEffect(()=>()=>{f&&(d.layers.delete(f),d.layersWithOutsidePointerEventsDisabled.delete(f),jt())},[f,d]),i.useEffect(()=>{const C=()=>w({});return document.addEventListener(Xe,C),()=>document.removeEventListener(Xe,C)},[]),e.jsx(A.div,{...y,ref:S,style:{pointerEvents:j?v?"auto":"none":void 0,...t.style},onFocusCapture:T(t.onFocusCapture,k.onFocusCapture),onBlurCapture:T(t.onBlurCapture,k.onBlurCapture),onPointerDownCapture:T(t.onPointerDownCapture,p.onPointerDownCapture)})});Ft.displayName=or;var lr="DismissableLayerBranch",dr=i.forwardRef((t,s)=>{const n=i.useContext(Pt),r=i.useRef(null),a=P(s,r);return i.useEffect(()=>{const o=r.current;if(o)return n.branches.add(o),()=>{n.branches.delete(o)}},[n.branches]),e.jsx(A.div,{...t,ref:a})});dr.displayName=lr;function ur(t,s=globalThis?.document){const n=oe(t),r=i.useRef(!1),a=i.useRef(()=>{});return i.useEffect(()=>{const o=l=>{if(l.target&&!r.current){let y=function(){Lt(ir,n,d,{discrete:!0})};const d={originalEvent:l};l.pointerType==="touch"?(s.removeEventListener("click",a.current),a.current=y,s.addEventListener("click",a.current,{once:!0})):y()}else s.removeEventListener("click",a.current);r.current=!1},c=window.setTimeout(()=>{s.addEventListener("pointerdown",o)},0);return()=>{window.clearTimeout(c),s.removeEventListener("pointerdown",o),s.removeEventListener("click",a.current)}},[s,n]),{onPointerDownCapture:()=>r.current=!0}}function mr(t,s=globalThis?.document){const n=oe(t),r=i.useRef(!1);return i.useEffect(()=>{const a=o=>{o.target&&!r.current&&Lt(cr,n,{originalEvent:o},{discrete:!1})};return s.addEventListener("focusin",a),()=>s.removeEventListener("focusin",a)},[s,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function jt(){const t=new CustomEvent(Xe);document.dispatchEvent(t)}function Lt(t,s,n,{discrete:r}){const a=n.originalEvent.target,o=new CustomEvent(t,{bubbles:!1,cancelable:!0,detail:n});s&&a.addEventListener(t,s,{once:!0}),r?Zs(a,o):a.dispatchEvent(o)}var Le="focusScope.autoFocusOnMount",ze="focusScope.autoFocusOnUnmount",Nt={bubbles:!1,cancelable:!0},fr="FocusScope",zt=i.forwardRef((t,s)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:a,onUnmountAutoFocus:o,...c}=t,[l,y]=i.useState(null),d=oe(a),f=oe(o),m=i.useRef(null),g=P(s,u=>y(u)),w=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let u=function(j){if(w.paused||!l)return;const v=j.target;l.contains(v)?m.current=v:U(m.current,{select:!0})},x=function(j){if(w.paused||!l)return;const v=j.relatedTarget;v!==null&&(l.contains(v)||U(m.current,{select:!0}))},N=function(j){if(document.activeElement===document.body)for(const p of j)p.removedNodes.length>0&&U(l)};document.addEventListener("focusin",u),document.addEventListener("focusout",x);const h=new MutationObserver(N);return l&&h.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",u),document.removeEventListener("focusout",x),h.disconnect()}}},[r,l,w.paused]),i.useEffect(()=>{if(l){Ct.add(w);const u=document.activeElement;if(!l.contains(u)){const N=new CustomEvent(Le,Nt);l.addEventListener(Le,d),l.dispatchEvent(N),N.defaultPrevented||(hr(br($t(l)),{select:!0}),document.activeElement===u&&U(l))}return()=>{l.removeEventListener(Le,d),setTimeout(()=>{const N=new CustomEvent(ze,Nt);l.addEventListener(ze,f),l.dispatchEvent(N),N.defaultPrevented||U(u??document.body,{select:!0}),l.removeEventListener(ze,f),Ct.remove(w)},0)}}},[l,d,f,w]);const S=i.useCallback(u=>{if(!n&&!r||w.paused)return;const x=u.key==="Tab"&&!u.altKey&&!u.ctrlKey&&!u.metaKey,N=document.activeElement;if(x&&N){const h=u.currentTarget,[j,v]=xr(h);j&&v?!u.shiftKey&&N===v?(u.preventDefault(),n&&U(j,{select:!0})):u.shiftKey&&N===j&&(u.preventDefault(),n&&U(v,{select:!0})):N===h&&u.preventDefault()}},[n,r,w.paused]);return e.jsx(A.div,{tabIndex:-1,...c,ref:g,onKeyDown:S})});zt.displayName=fr;function hr(t,{select:s=!1}={}){const n=document.activeElement;for(const r of t)if(U(r,{select:s}),document.activeElement!==n)return}function xr(t){const s=$t(t),n=wt(s,t),r=wt(s.reverse(),t);return[n,r]}function $t(t){const s=[],n=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const a=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||a?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)s.push(n.currentNode);return s}function wt(t,s){for(const n of t)if(!pr(n,{upTo:s}))return n}function pr(t,{upTo:s}){if(getComputedStyle(t).visibility==="hidden")return!0;for(;t;){if(s!==void 0&&t===s)return!1;if(getComputedStyle(t).display==="none")return!0;t=t.parentElement}return!1}function gr(t){return t instanceof HTMLInputElement&&"select"in t}function U(t,{select:s=!1}={}){if(t&&t.focus){const n=document.activeElement;t.focus({preventScroll:!0}),t!==n&&gr(t)&&s&&t.select()}}var Ct=vr();function vr(){let t=[];return{add(s){const n=t[0];s!==n&&n?.pause(),t=St(t,s),t.unshift(s)},remove(s){t=St(t,s),t[0]?.resume()}}}function St(t,s){const n=[...t],r=n.indexOf(s);return r!==-1&&n.splice(r,1),n}function br(t){return t.filter(s=>s.tagName!=="A")}var yr="Portal",Bt=i.forwardRef((t,s)=>{const{container:n,...r}=t,[a,o]=i.useState(!1);st(()=>o(!0),[]);const c=n||a&&globalThis?.document?.body;return c?Js.createPortal(e.jsx(A.div,{...r,ref:s}),c):null});Bt.displayName=yr;var $e=0;function jr(){i.useEffect(()=>{const t=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",t[0]??kt()),document.body.insertAdjacentElement("beforeend",t[1]??kt()),$e++,()=>{$e===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(s=>s.remove()),$e--}},[])}function kt(){const t=document.createElement("span");return t.setAttribute("data-radix-focus-guard",""),t.tabIndex=0,t.style.outline="none",t.style.opacity="0",t.style.position="fixed",t.style.pointerEvents="none",t}var K=function(){return K=Object.assign||function(s){for(var n,r=1,a=arguments.length;r<a;r++){n=arguments[r];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(s[o]=n[o])}return s},K.apply(this,arguments)};function Vt(t,s){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&s.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(t);a<r.length;a++)s.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(t,r[a])&&(n[r[a]]=t[r[a]]);return n}function Nr(t,s,n){if(n||arguments.length===2)for(var r=0,a=s.length,o;r<a;r++)(o||!(r in s))&&(o||(o=Array.prototype.slice.call(s,0,r)),o[r]=s[r]);return t.concat(o||Array.prototype.slice.call(s))}var Ie="right-scroll-bar-position",Me="width-before-scroll-bar",wr="with-scroll-bars-hidden",Cr="--removed-body-scroll-bar-size";function Be(t,s){return typeof t=="function"?t(s):t&&(t.current=s),t}function Sr(t,s){var n=i.useState(function(){return{value:t,callback:s,facade:{get current(){return n.value},set current(r){var a=n.value;a!==r&&(n.value=r,n.callback(r,a))}}}})[0];return n.callback=s,n.facade}var kr=typeof window<"u"?i.useLayoutEffect:i.useEffect,Et=new WeakMap;function Er(t,s){var n=Sr(null,function(r){return t.forEach(function(a){return Be(a,r)})});return kr(function(){var r=Et.get(n);if(r){var a=new Set(r),o=new Set(t),c=n.current;a.forEach(function(l){o.has(l)||Be(l,null)}),o.forEach(function(l){a.has(l)||Be(l,c)})}Et.set(n,t)},[t]),n}function Ir(t){return t}function Mr(t,s){s===void 0&&(s=Ir);var n=[],r=!1,a={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:t},useMedium:function(o){var c=s(o,r);return n.push(c),function(){n=n.filter(function(l){return l!==c})}},assignSyncMedium:function(o){for(r=!0;n.length;){var c=n;n=[],c.forEach(o)}n={push:function(l){return o(l)},filter:function(){return n}}},assignMedium:function(o){r=!0;var c=[];if(n.length){var l=n;n=[],l.forEach(o),c=n}var y=function(){var f=c;c=[],f.forEach(o)},d=function(){return Promise.resolve().then(y)};d(),n={push:function(f){c.push(f),d()},filter:function(f){return c=c.filter(f),n}}}};return a}function Rr(t){t===void 0&&(t={});var s=Mr(null);return s.options=K({async:!0,ssr:!1},t),s}var Kt=function(t){var s=t.sideCar,n=Vt(t,["sideCar"]);if(!s)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=s.read();if(!r)throw new Error("Sidecar medium not found");return i.createElement(r,K({},n))};Kt.isSideCarExport=!0;function Dr(t,s){return t.useMedium(s),Kt}var Wt=Rr(),Ve=function(){},Te=i.forwardRef(function(t,s){var n=i.useRef(null),r=i.useState({onScrollCapture:Ve,onWheelCapture:Ve,onTouchMoveCapture:Ve}),a=r[0],o=r[1],c=t.forwardProps,l=t.children,y=t.className,d=t.removeScrollBar,f=t.enabled,m=t.shards,g=t.sideCar,w=t.noRelative,S=t.noIsolation,u=t.inert,x=t.allowPinchZoom,N=t.as,h=N===void 0?"div":N,j=t.gapMode,v=Vt(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),p=g,k=Er([n,s]),C=K(K({},v),a);return i.createElement(i.Fragment,null,f&&i.createElement(p,{sideCar:Wt,removeScrollBar:d,shards:m,noRelative:w,noIsolation:S,inert:u,setCallbacks:o,allowPinchZoom:!!x,lockRef:n,gapMode:j}),c?i.cloneElement(i.Children.only(l),K(K({},C),{ref:k})):i.createElement(h,K({},C,{className:y,ref:k}),l))});Te.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Te.classNames={fullWidth:Me,zeroRight:Ie};var Tr=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Ar(){if(!document)return null;var t=document.createElement("style");t.type="text/css";var s=Tr();return s&&t.setAttribute("nonce",s),t}function _r(t,s){t.styleSheet?t.styleSheet.cssText=s:t.appendChild(document.createTextNode(s))}function Or(t){var s=document.head||document.getElementsByTagName("head")[0];s.appendChild(t)}var Pr=function(){var t=0,s=null;return{add:function(n){t==0&&(s=Ar())&&(_r(s,n),Or(s)),t++},remove:function(){t--,!t&&s&&(s.parentNode&&s.parentNode.removeChild(s),s=null)}}},Fr=function(){var t=Pr();return function(s,n){i.useEffect(function(){return t.add(s),function(){t.remove()}},[s&&n])}},Ut=function(){var t=Fr(),s=function(n){var r=n.styles,a=n.dynamic;return t(r,a),null};return s},Lr={left:0,top:0,right:0,gap:0},Ke=function(t){return parseInt(t||"",10)||0},zr=function(t){var s=window.getComputedStyle(document.body),n=s[t==="padding"?"paddingLeft":"marginLeft"],r=s[t==="padding"?"paddingTop":"marginTop"],a=s[t==="padding"?"paddingRight":"marginRight"];return[Ke(n),Ke(r),Ke(a)]},$r=function(t){if(t===void 0&&(t="margin"),typeof window>"u")return Lr;var s=zr(t),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:s[0],top:s[1],right:s[2],gap:Math.max(0,r-n+s[2]-s[0])}},Br=Ut(),ae="data-scroll-locked",Vr=function(t,s,n,r){var a=t.left,o=t.top,c=t.right,l=t.gap;return n===void 0&&(n="margin"),`
  .`.concat(wr,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(l,"px ").concat(r,`;
  }
  body[`).concat(ae,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([s&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(a,`px;
    padding-top: `).concat(o,`px;
    padding-right: `).concat(c,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(l,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ie,` {
    right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(Me,` {
    margin-right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(Ie," .").concat(Ie,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Me," .").concat(Me,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(ae,`] {
    `).concat(Cr,": ").concat(l,`px;
  }
`)},It=function(){var t=parseInt(document.body.getAttribute(ae)||"0",10);return isFinite(t)?t:0},Kr=function(){i.useEffect(function(){return document.body.setAttribute(ae,(It()+1).toString()),function(){var t=It()-1;t<=0?document.body.removeAttribute(ae):document.body.setAttribute(ae,t.toString())}},[])},Wr=function(t){var s=t.noRelative,n=t.noImportant,r=t.gapMode,a=r===void 0?"margin":r;Kr();var o=i.useMemo(function(){return $r(a)},[a]);return i.createElement(Br,{styles:Vr(o,!s,a,n?"":"!important")})},Qe=!1;if(typeof window<"u")try{var be=Object.defineProperty({},"passive",{get:function(){return Qe=!0,!0}});window.addEventListener("test",be,be),window.removeEventListener("test",be,be)}catch{Qe=!1}var Q=Qe?{passive:!1}:!1,Ur=function(t){return t.tagName==="TEXTAREA"},Ht=function(t,s){if(!(t instanceof Element))return!1;var n=window.getComputedStyle(t);return n[s]!=="hidden"&&!(n.overflowY===n.overflowX&&!Ur(t)&&n[s]==="visible")},Hr=function(t){return Ht(t,"overflowY")},Gr=function(t){return Ht(t,"overflowX")},Mt=function(t,s){var n=s.ownerDocument,r=s;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var a=Gt(t,r);if(a){var o=qt(t,r),c=o[1],l=o[2];if(c>l)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},qr=function(t){var s=t.scrollTop,n=t.scrollHeight,r=t.clientHeight;return[s,n,r]},Yr=function(t){var s=t.scrollLeft,n=t.scrollWidth,r=t.clientWidth;return[s,n,r]},Gt=function(t,s){return t==="v"?Hr(s):Gr(s)},qt=function(t,s){return t==="v"?qr(s):Yr(s)},Xr=function(t,s){return t==="h"&&s==="rtl"?-1:1},Qr=function(t,s,n,r,a){var o=Xr(t,window.getComputedStyle(s).direction),c=o*r,l=n.target,y=s.contains(l),d=!1,f=c>0,m=0,g=0;do{if(!l)break;var w=qt(t,l),S=w[0],u=w[1],x=w[2],N=u-x-o*S;(S||N)&&Gt(t,l)&&(m+=N,g+=S);var h=l.parentNode;l=h&&h.nodeType===Node.DOCUMENT_FRAGMENT_NODE?h.host:h}while(!y&&l!==document.body||y&&(s.contains(l)||s===l));return(f&&Math.abs(m)<1||!f&&Math.abs(g)<1)&&(d=!0),d},ye=function(t){return"changedTouches"in t?[t.changedTouches[0].clientX,t.changedTouches[0].clientY]:[0,0]},Rt=function(t){return[t.deltaX,t.deltaY]},Dt=function(t){return t&&"current"in t?t.current:t},Zr=function(t,s){return t[0]===s[0]&&t[1]===s[1]},Jr=function(t){return`
  .block-interactivity-`.concat(t,` {pointer-events: none;}
  .allow-interactivity-`).concat(t,` {pointer-events: all;}
`)},ea=0,Z=[];function ta(t){var s=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),a=i.useState(ea++)[0],o=i.useState(Ut)[0],c=i.useRef(t);i.useEffect(function(){c.current=t},[t]),i.useEffect(function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(a));var u=Nr([t.lockRef.current],(t.shards||[]).map(Dt),!0).filter(Boolean);return u.forEach(function(x){return x.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),u.forEach(function(x){return x.classList.remove("allow-interactivity-".concat(a))})}}},[t.inert,t.lockRef.current,t.shards]);var l=i.useCallback(function(u,x){if("touches"in u&&u.touches.length===2||u.type==="wheel"&&u.ctrlKey)return!c.current.allowPinchZoom;var N=ye(u),h=n.current,j="deltaX"in u?u.deltaX:h[0]-N[0],v="deltaY"in u?u.deltaY:h[1]-N[1],p,k=u.target,C=Math.abs(j)>Math.abs(v)?"h":"v";if("touches"in u&&C==="h"&&k.type==="range")return!1;var b=Mt(C,k);if(!b)return!0;if(b?p=C:(p=C==="v"?"h":"v",b=Mt(C,k)),!b)return!1;if(!r.current&&"changedTouches"in u&&(j||v)&&(r.current=p),!p)return!0;var D=r.current||p;return Qr(D,x,u,D==="h"?j:v)},[]),y=i.useCallback(function(u){var x=u;if(!(!Z.length||Z[Z.length-1]!==o)){var N="deltaY"in x?Rt(x):ye(x),h=s.current.filter(function(p){return p.name===x.type&&(p.target===x.target||x.target===p.shadowParent)&&Zr(p.delta,N)})[0];if(h&&h.should){x.cancelable&&x.preventDefault();return}if(!h){var j=(c.current.shards||[]).map(Dt).filter(Boolean).filter(function(p){return p.contains(x.target)}),v=j.length>0?l(x,j[0]):!c.current.noIsolation;v&&x.cancelable&&x.preventDefault()}}},[]),d=i.useCallback(function(u,x,N,h){var j={name:u,delta:x,target:N,should:h,shadowParent:sa(N)};s.current.push(j),setTimeout(function(){s.current=s.current.filter(function(v){return v!==j})},1)},[]),f=i.useCallback(function(u){n.current=ye(u),r.current=void 0},[]),m=i.useCallback(function(u){d(u.type,Rt(u),u.target,l(u,t.lockRef.current))},[]),g=i.useCallback(function(u){d(u.type,ye(u),u.target,l(u,t.lockRef.current))},[]);i.useEffect(function(){return Z.push(o),t.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:g}),document.addEventListener("wheel",y,Q),document.addEventListener("touchmove",y,Q),document.addEventListener("touchstart",f,Q),function(){Z=Z.filter(function(u){return u!==o}),document.removeEventListener("wheel",y,Q),document.removeEventListener("touchmove",y,Q),document.removeEventListener("touchstart",f,Q)}},[]);var w=t.removeScrollBar,S=t.inert;return i.createElement(i.Fragment,null,S?i.createElement(o,{styles:Jr(a)}):null,w?i.createElement(Wr,{noRelative:t.noRelative,gapMode:t.gapMode}):null)}function sa(t){for(var s=null;t!==null;)t instanceof ShadowRoot&&(s=t.host,t=t.host),t=t.parentNode;return s}const na=Dr(Wt,ta);var Yt=i.forwardRef(function(t,s){return i.createElement(Te,K({},t,{ref:s,sideCar:na}))});Yt.classNames=Te.classNames;var ra=function(t){if(typeof document>"u")return null;var s=Array.isArray(t)?t[0]:t;return s.ownerDocument.body},J=new WeakMap,je=new WeakMap,Ne={},We=0,Xt=function(t){return t&&(t.host||Xt(t.parentNode))},aa=function(t,s){return s.map(function(n){if(t.contains(n))return n;var r=Xt(n);return r&&t.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",t,". Doing nothing"),null)}).filter(function(n){return!!n})},oa=function(t,s,n,r){var a=aa(s,Array.isArray(t)?t:[t]);Ne[n]||(Ne[n]=new WeakMap);var o=Ne[n],c=[],l=new Set,y=new Set(a),d=function(m){!m||l.has(m)||(l.add(m),d(m.parentNode))};a.forEach(d);var f=function(m){!m||y.has(m)||Array.prototype.forEach.call(m.children,function(g){if(l.has(g))f(g);else try{var w=g.getAttribute(r),S=w!==null&&w!=="false",u=(J.get(g)||0)+1,x=(o.get(g)||0)+1;J.set(g,u),o.set(g,x),c.push(g),u===1&&S&&je.set(g,!0),x===1&&g.setAttribute(n,"true"),S||g.setAttribute(r,"true")}catch(N){console.error("aria-hidden: cannot operate on ",g,N)}})};return f(s),l.clear(),We++,function(){c.forEach(function(m){var g=J.get(m)-1,w=o.get(m)-1;J.set(m,g),o.set(m,w),g||(je.has(m)||m.removeAttribute(r),je.delete(m)),w||m.removeAttribute(n)}),We--,We||(J=new WeakMap,J=new WeakMap,je=new WeakMap,Ne={})}},ia=function(t,s,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(t)?t:[t]),a=ra(t);return a?(r.push.apply(r,Array.from(a.querySelectorAll("[aria-live], script"))),oa(r,a,n,"aria-hidden")):function(){return null}},Ae="Dialog",[Qt,Wo]=ie(Ae),[ca,V]=Qt(Ae),Zt=t=>{const{__scopeDialog:s,children:n,open:r,defaultOpen:a,onOpenChange:o,modal:c=!0}=t,l=i.useRef(null),y=i.useRef(null),[d,f]=pe({prop:r,defaultProp:a??!1,onChange:o,caller:Ae});return e.jsx(ca,{scope:s,triggerRef:l,contentRef:y,contentId:re(),titleId:re(),descriptionId:re(),open:d,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(m=>!m),[f]),modal:c,children:n})};Zt.displayName=Ae;var Jt="DialogTrigger",es=i.forwardRef((t,s)=>{const{__scopeDialog:n,...r}=t,a=V(Jt,n),o=P(s,a.triggerRef);return e.jsx(A.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":it(a.open),...r,ref:o,onClick:T(t.onClick,a.onOpenToggle)})});es.displayName=Jt;var at="DialogPortal",[la,ts]=Qt(at,{forceMount:void 0}),ss=t=>{const{__scopeDialog:s,forceMount:n,children:r,container:a}=t,o=V(at,s);return e.jsx(la,{scope:s,forceMount:n,children:i.Children.map(r,c=>e.jsx(ge,{present:n||o.open,children:e.jsx(Bt,{asChild:!0,container:a,children:c})}))})};ss.displayName=at;var Re="DialogOverlay",ns=i.forwardRef((t,s)=>{const n=ts(Re,t.__scopeDialog),{forceMount:r=n.forceMount,...a}=t,o=V(Re,t.__scopeDialog);return o.modal?e.jsx(ge,{present:r||o.open,children:e.jsx(ua,{...a,ref:s})}):null});ns.displayName=Re;var da=Ge("DialogOverlay.RemoveScroll"),ua=i.forwardRef((t,s)=>{const{__scopeDialog:n,...r}=t,a=V(Re,n);return e.jsx(Yt,{as:da,allowPinchZoom:!0,shards:[a.contentRef],children:e.jsx(A.div,{"data-state":it(a.open),...r,ref:s,style:{pointerEvents:"auto",...r.style}})})}),Y="DialogContent",rs=i.forwardRef((t,s)=>{const n=ts(Y,t.__scopeDialog),{forceMount:r=n.forceMount,...a}=t,o=V(Y,t.__scopeDialog);return e.jsx(ge,{present:r||o.open,children:o.modal?e.jsx(ma,{...a,ref:s}):e.jsx(fa,{...a,ref:s})})});rs.displayName=Y;var ma=i.forwardRef((t,s)=>{const n=V(Y,t.__scopeDialog),r=i.useRef(null),a=P(s,n.contentRef,r);return i.useEffect(()=>{const o=r.current;if(o)return ia(o)},[]),e.jsx(as,{...t,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:T(t.onCloseAutoFocus,o=>{o.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:T(t.onPointerDownOutside,o=>{const c=o.detail.originalEvent,l=c.button===0&&c.ctrlKey===!0;(c.button===2||l)&&o.preventDefault()}),onFocusOutside:T(t.onFocusOutside,o=>o.preventDefault())})}),fa=i.forwardRef((t,s)=>{const n=V(Y,t.__scopeDialog),r=i.useRef(!1),a=i.useRef(!1);return e.jsx(as,{...t,ref:s,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:o=>{t.onCloseAutoFocus?.(o),o.defaultPrevented||(r.current||n.triggerRef.current?.focus(),o.preventDefault()),r.current=!1,a.current=!1},onInteractOutside:o=>{t.onInteractOutside?.(o),o.defaultPrevented||(r.current=!0,o.detail.originalEvent.type==="pointerdown"&&(a.current=!0));const c=o.target;n.triggerRef.current?.contains(c)&&o.preventDefault(),o.detail.originalEvent.type==="focusin"&&a.current&&o.preventDefault()}})}),as=i.forwardRef((t,s)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:o,...c}=t,l=V(Y,n),y=i.useRef(null),d=P(s,y);return jr(),e.jsxs(e.Fragment,{children:[e.jsx(zt,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:o,children:e.jsx(Ft,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":it(l.open),...c,ref:d,onDismiss:()=>l.onOpenChange(!1)})}),e.jsxs(e.Fragment,{children:[e.jsx(xa,{titleId:l.titleId}),e.jsx(ga,{contentRef:y,descriptionId:l.descriptionId})]})]})}),ot="DialogTitle",os=i.forwardRef((t,s)=>{const{__scopeDialog:n,...r}=t,a=V(ot,n);return e.jsx(A.h2,{id:a.titleId,...r,ref:s})});os.displayName=ot;var is="DialogDescription",ha=i.forwardRef((t,s)=>{const{__scopeDialog:n,...r}=t,a=V(is,n);return e.jsx(A.p,{id:a.descriptionId,...r,ref:s})});ha.displayName=is;var cs="DialogClose",ls=i.forwardRef((t,s)=>{const{__scopeDialog:n,...r}=t,a=V(cs,n);return e.jsx(A.button,{type:"button",...r,ref:s,onClick:T(t.onClick,()=>a.onOpenChange(!1))})});ls.displayName=cs;function it(t){return t?"open":"closed"}var ds="DialogTitleWarning",[Uo,us]=en(ds,{contentName:Y,titleName:ot,docsSlug:"dialog"}),xa=({titleId:t})=>{const s=us(ds),n=`\`${s.contentName}\` requires a \`${s.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${s.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${s.docsSlug}`;return i.useEffect(()=>{t&&(document.getElementById(t)||console.error(n))},[n,t]),null},pa="DialogDescriptionWarning",ga=({contentRef:t,descriptionId:s})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${us(pa).contentName}}.`;return i.useEffect(()=>{const a=t.current?.getAttribute("aria-describedby");s&&a&&(document.getElementById(s)||console.warn(r))},[r,t,s]),null},va=Zt,ba=es,ya=ss,ja=ns,Na=rs,wa=os,Ca=ls;function xe({...t}){return e.jsx(va,{"data-slot":"dialog",...t})}function Sa({...t}){return e.jsx(ba,{"data-slot":"dialog-trigger",...t})}function ka({...t}){return e.jsx(ya,{"data-slot":"dialog-portal",...t})}function Ea({className:t,...s}){return e.jsx(ja,{"data-slot":"dialog-overlay",className:R("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s})}function De({className:t,children:s,showCloseButton:n=!0,...r}){return e.jsxs(ka,{"data-slot":"dialog-portal",children:[e.jsx(Ea,{}),e.jsxs(Na,{"data-slot":"dialog-content",className:R("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...r,children:[s,n&&e.jsxs(Ca,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[e.jsx(rt,{}),e.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function ct({className:t,...s}){return e.jsx("div",{"data-slot":"dialog-header",className:R("flex flex-col gap-2 text-center sm:text-left",t),...s})}function lt({className:t,...s}){return e.jsx(wa,{"data-slot":"dialog-title",className:R("text-lg leading-none font-semibold",t),...s})}function ms(t){const s=t+"CollectionProvider",[n,r]=ie(s),[a,o]=n(s,{collectionRef:{current:null},itemMap:new Map}),c=u=>{const{scope:x,children:N}=u,h=G.useRef(null),j=G.useRef(new Map).current;return e.jsx(a,{scope:x,itemMap:j,collectionRef:h,children:N})};c.displayName=s;const l=t+"CollectionSlot",y=Ge(l),d=G.forwardRef((u,x)=>{const{scope:N,children:h}=u,j=o(l,N),v=P(x,j.collectionRef);return e.jsx(y,{ref:v,children:h})});d.displayName=l;const f=t+"CollectionItemSlot",m="data-radix-collection-item",g=Ge(f),w=G.forwardRef((u,x)=>{const{scope:N,children:h,...j}=u,v=G.useRef(null),p=P(x,v),k=o(f,N);return G.useEffect(()=>(k.itemMap.set(v,{ref:v,...j}),()=>void k.itemMap.delete(v))),e.jsx(g,{[m]:"",ref:p,children:h})});w.displayName=f;function S(u){const x=o(t+"CollectionConsumer",u);return G.useCallback(()=>{const h=x.collectionRef.current;if(!h)return[];const j=Array.from(h.querySelectorAll(`[${m}]`));return Array.from(x.itemMap.values()).sort((k,C)=>j.indexOf(k.ref.current)-j.indexOf(C.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:c,Slot:d,ItemSlot:w},S,r]}var Ia=i.createContext(void 0);function dt(t){const s=i.useContext(Ia);return t||s||"ltr"}var Ue="rovingFocusGroup.onEntryFocus",Ma={bubbles:!1,cancelable:!0},ve="RovingFocusGroup",[Ze,fs,Ra]=ms(ve),[Da,hs]=ie(ve,[Ra]),[Ta,Aa]=Da(ve),xs=i.forwardRef((t,s)=>e.jsx(Ze.Provider,{scope:t.__scopeRovingFocusGroup,children:e.jsx(Ze.Slot,{scope:t.__scopeRovingFocusGroup,children:e.jsx(_a,{...t,ref:s})})}));xs.displayName=ve;var _a=i.forwardRef((t,s)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:a=!1,dir:o,currentTabStopId:c,defaultCurrentTabStopId:l,onCurrentTabStopIdChange:y,onEntryFocus:d,preventScrollOnEntryFocus:f=!1,...m}=t,g=i.useRef(null),w=P(s,g),S=dt(o),[u,x]=pe({prop:c,defaultProp:l??null,onChange:y,caller:ve}),[N,h]=i.useState(!1),j=oe(d),v=fs(n),p=i.useRef(!1),[k,C]=i.useState(0);return i.useEffect(()=>{const b=g.current;if(b)return b.addEventListener(Ue,j),()=>b.removeEventListener(Ue,j)},[j]),e.jsx(Ta,{scope:n,orientation:r,dir:S,loop:a,currentTabStopId:u,onItemFocus:i.useCallback(b=>x(b),[x]),onItemShiftTab:i.useCallback(()=>h(!0),[]),onFocusableItemAdd:i.useCallback(()=>C(b=>b+1),[]),onFocusableItemRemove:i.useCallback(()=>C(b=>b-1),[]),children:e.jsx(A.div,{tabIndex:N||k===0?-1:0,"data-orientation":r,...m,ref:w,style:{outline:"none",...t.style},onMouseDown:T(t.onMouseDown,()=>{p.current=!0}),onFocus:T(t.onFocus,b=>{const D=!p.current;if(b.target===b.currentTarget&&D&&!N){const F=new CustomEvent(Ue,Ma);if(b.currentTarget.dispatchEvent(F),!F.defaultPrevented){const _=v().filter(z=>z.focusable),W=_.find(z=>z.active),le=_.find(z=>z.id===u),de=[W,le,..._].filter(Boolean).map(z=>z.ref.current);vs(de,f)}}p.current=!1}),onBlur:T(t.onBlur,()=>h(!1))})})}),ps="RovingFocusGroupItem",gs=i.forwardRef((t,s)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:a=!1,tabStopId:o,children:c,...l}=t,y=re(),d=o||y,f=Aa(ps,n),m=f.currentTabStopId===d,g=fs(n),{onFocusableItemAdd:w,onFocusableItemRemove:S,currentTabStopId:u}=f;return i.useEffect(()=>{if(r)return w(),()=>S()},[r,w,S]),e.jsx(Ze.ItemSlot,{scope:n,id:d,focusable:r,active:a,children:e.jsx(A.span,{tabIndex:m?0:-1,"data-orientation":f.orientation,...l,ref:s,onMouseDown:T(t.onMouseDown,x=>{r?f.onItemFocus(d):x.preventDefault()}),onFocus:T(t.onFocus,()=>f.onItemFocus(d)),onKeyDown:T(t.onKeyDown,x=>{if(x.key==="Tab"&&x.shiftKey){f.onItemShiftTab();return}if(x.target!==x.currentTarget)return;const N=Fa(x,f.orientation,f.dir);if(N!==void 0){if(x.metaKey||x.ctrlKey||x.altKey||x.shiftKey)return;x.preventDefault();let j=g().filter(v=>v.focusable).map(v=>v.ref.current);if(N==="last")j.reverse();else if(N==="prev"||N==="next"){N==="prev"&&j.reverse();const v=j.indexOf(x.currentTarget);j=f.loop?La(j,v+1):j.slice(v+1)}setTimeout(()=>vs(j))}}),children:typeof c=="function"?c({isCurrentTabStop:m,hasTabStop:u!=null}):c})})});gs.displayName=ps;var Oa={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Pa(t,s){return s!=="rtl"?t:t==="ArrowLeft"?"ArrowRight":t==="ArrowRight"?"ArrowLeft":t}function Fa(t,s,n){const r=Pa(t.key,n);if(!(s==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(s==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return Oa[r]}function vs(t,s=!1){const n=document.activeElement;for(const r of t)if(r===n||(r.focus({preventScroll:s}),document.activeElement!==n))return}function La(t,s){return t.map((n,r)=>t[(s+r)%t.length])}var za=xs,$a=gs,_e="Tabs",[Ba,Ho]=ie(_e,[hs]),bs=hs(),[Va,ut]=Ba(_e),ys=i.forwardRef((t,s)=>{const{__scopeTabs:n,value:r,onValueChange:a,defaultValue:o,orientation:c="horizontal",dir:l,activationMode:y="automatic",...d}=t,f=dt(l),[m,g]=pe({prop:r,onChange:a,defaultProp:o??"",caller:_e});return e.jsx(Va,{scope:n,baseId:re(),value:m,onValueChange:g,orientation:c,dir:f,activationMode:y,children:e.jsx(A.div,{dir:f,"data-orientation":c,...d,ref:s})})});ys.displayName=_e;var js="TabsList",Ns=i.forwardRef((t,s)=>{const{__scopeTabs:n,loop:r=!0,...a}=t,o=ut(js,n),c=bs(n);return e.jsx(za,{asChild:!0,...c,orientation:o.orientation,dir:o.dir,loop:r,children:e.jsx(A.div,{role:"tablist","aria-orientation":o.orientation,...a,ref:s})})});Ns.displayName=js;var ws="TabsTrigger",Cs=i.forwardRef((t,s)=>{const{__scopeTabs:n,value:r,disabled:a=!1,...o}=t,c=ut(ws,n),l=bs(n),y=Es(c.baseId,r),d=Is(c.baseId,r),f=r===c.value;return e.jsx($a,{asChild:!0,...l,focusable:!a,active:f,children:e.jsx(A.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:y,...o,ref:s,onMouseDown:T(t.onMouseDown,m=>{!a&&m.button===0&&m.ctrlKey===!1?c.onValueChange(r):m.preventDefault()}),onKeyDown:T(t.onKeyDown,m=>{[" ","Enter"].includes(m.key)&&c.onValueChange(r)}),onFocus:T(t.onFocus,()=>{const m=c.activationMode!=="manual";!f&&!a&&m&&c.onValueChange(r)})})})});Cs.displayName=ws;var Ss="TabsContent",ks=i.forwardRef((t,s)=>{const{__scopeTabs:n,value:r,forceMount:a,children:o,...c}=t,l=ut(Ss,n),y=Es(l.baseId,r),d=Is(l.baseId,r),f=r===l.value,m=i.useRef(f);return i.useEffect(()=>{const g=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(g)},[]),e.jsx(ge,{present:a||f,children:({present:g})=>e.jsx(A.div,{"data-state":f?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":y,hidden:!g,id:d,tabIndex:0,...c,ref:s,style:{...t.style,animationDuration:m.current?"0s":void 0},children:g&&o})})});ks.displayName=Ss;function Es(t,s){return`${t}-trigger-${s}`}function Is(t,s){return`${t}-content-${s}`}var Ka=ys,Wa=Ns,Ua=Cs,Ha=ks;function Ga({className:t,...s}){return e.jsx(Ka,{"data-slot":"tabs",className:R("flex flex-col gap-2",t),...s})}function qa({className:t,...s}){return e.jsx(Wa,{"data-slot":"tabs-list",className:R("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...s})}function ue({className:t,...s}){return e.jsx(Ua,{"data-slot":"tabs-trigger",className:R("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function me({className:t,...s}){return e.jsx(Ha,{"data-slot":"tabs-content",className:R("flex-1 outline-none",t),...s})}const q=i.forwardRef(({className:t,type:s,variant:n="default",...r},a)=>e.jsx("input",{type:s,className:R("flex h-11 w-full rounded-lg border border-input bg-input px-4 py-2 text-sm transition-all duration-200","file:border-0 file:bg-transparent file:text-sm file:font-medium","placeholder:text-muted-foreground","focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-0","focus-visible:border-primary/50 focus-visible:bg-background/50","disabled:cursor-not-allowed disabled:opacity-50","hover:border-primary/30 hover:bg-background/30",{"border-transparent bg-muted/50":n==="ghost","bg-muted border-muted":n==="filled"},t),ref:a,...r}));q.displayName="ModernInput";function Ya({totalCount:t,filteredCount:s,searchQuery:n,onSearchChange:r,onOpenAI:a}){return e.jsx("header",{className:"sticky top-0 z-50 w-full border-b border-border/40 bg-background/80 backdrop-blur-xl",children:e.jsxs("div",{className:"container flex h-16 max-w-screen-2xl items-center justify-between px-6",children:[e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex bg-zinc-900 h-10 w-10 items-center justify-center rounded-xl shadow-lg",children:e.jsx(un,{className:"h-5 w-5 text-primary-foreground"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-bold tracking-tight",children:"Каталог"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Профессиональная система каталогизации"})]})]})}),e.jsx("div",{className:"flex flex-1 items-center justify-center px-8",children:e.jsxs("div",{className:"relative w-full max-w-lg",children:[e.jsx(ne,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),e.jsx(q,{placeholder:"Поиск по артикулу, описанию, бренду...",value:n,onChange:o=>r(o.target.value),className:"pl-10 pr-4",variant:"ghost"})]})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(I,{variant:"outline",className:"hidden sm:flex",children:[s," / ",t]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(E,{variant:"ghost",size:"icon",className:"relative",children:[e.jsx(ln,{className:"h-4 w-4"}),e.jsx("span",{className:"absolute -top-1 -right-1 h-2 w-2 rounded-full bg-destructive"})]}),e.jsxs(E,{variant:"outline",size:"sm",children:[e.jsx(Kn,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Сохранить"})]}),e.jsxs(E,{variant:"outline",size:"sm",children:[e.jsx(pn,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Калькулятор"})]}),e.jsxs(E,{variant:"outline",size:"sm",children:[e.jsx(Ye,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Экспорт"})]}),e.jsxs(E,{variant:"gradient",size:"sm",onClick:a,className:"gap-2",children:[e.jsx(Ee,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"AI Помощник"})]})]})]})]})})}var Oe="Collapsible",[Xa,Go]=ie(Oe),[Qa,mt]=Xa(Oe),Ms=i.forwardRef((t,s)=>{const{__scopeCollapsible:n,open:r,defaultOpen:a,disabled:o,onOpenChange:c,...l}=t,[y,d]=pe({prop:r,defaultProp:a??!1,onChange:c,caller:Oe});return e.jsx(Qa,{scope:n,disabled:o,contentId:re(),open:y,onOpenToggle:i.useCallback(()=>d(f=>!f),[d]),children:e.jsx(A.div,{"data-state":ht(y),"data-disabled":o?"":void 0,...l,ref:s})})});Ms.displayName=Oe;var Rs="CollapsibleTrigger",Ds=i.forwardRef((t,s)=>{const{__scopeCollapsible:n,...r}=t,a=mt(Rs,n);return e.jsx(A.button,{type:"button","aria-controls":a.contentId,"aria-expanded":a.open||!1,"data-state":ht(a.open),"data-disabled":a.disabled?"":void 0,disabled:a.disabled,...r,ref:s,onClick:T(t.onClick,a.onOpenToggle)})});Ds.displayName=Rs;var ft="CollapsibleContent",Ts=i.forwardRef((t,s)=>{const{forceMount:n,...r}=t,a=mt(ft,t.__scopeCollapsible);return e.jsx(ge,{present:n||a.open,children:({present:o})=>e.jsx(Za,{...r,ref:s,present:o})})});Ts.displayName=ft;var Za=i.forwardRef((t,s)=>{const{__scopeCollapsible:n,present:r,children:a,...o}=t,c=mt(ft,n),[l,y]=i.useState(r),d=i.useRef(null),f=P(s,d),m=i.useRef(0),g=m.current,w=i.useRef(0),S=w.current,u=c.open||l,x=i.useRef(u),N=i.useRef(void 0);return i.useEffect(()=>{const h=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(h)},[]),st(()=>{const h=d.current;if(h){N.current=N.current||{transitionDuration:h.style.transitionDuration,animationName:h.style.animationName},h.style.transitionDuration="0s",h.style.animationName="none";const j=h.getBoundingClientRect();m.current=j.height,w.current=j.width,x.current||(h.style.transitionDuration=N.current.transitionDuration,h.style.animationName=N.current.animationName),y(r)}},[c.open,r]),e.jsx(A.div,{"data-state":ht(c.open),"data-disabled":c.disabled?"":void 0,id:c.contentId,hidden:!u,...o,ref:f,style:{"--radix-collapsible-content-height":g?`${g}px`:void 0,"--radix-collapsible-content-width":S?`${S}px`:void 0,...t.style},children:u&&a})});function ht(t){return t?"open":"closed"}var Ja=Ms;function we({...t}){return e.jsx(Ja,{"data-slot":"collapsible",...t})}function Ce({...t}){return e.jsx(Ds,{"data-slot":"collapsible-trigger",...t})}function Se({...t}){return e.jsx(Ts,{"data-slot":"collapsible-content",...t})}function As(t,[s,n]){return Math.min(n,Math.max(s,t))}var _s=["PageUp","PageDown"],Os=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],Ps={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},ce="Slider",[Je,eo,to]=ms(ce),[Fs,qo]=ie(ce,[to]),[so,Pe]=Fs(ce),Ls=i.forwardRef((t,s)=>{const{name:n,min:r=0,max:a=100,step:o=1,orientation:c="horizontal",disabled:l=!1,minStepsBetweenThumbs:y=0,defaultValue:d=[r],value:f,onValueChange:m=()=>{},onValueCommit:g=()=>{},inverted:w=!1,form:S,...u}=t,x=i.useRef(new Set),N=i.useRef(0),j=c==="horizontal"?no:ro,[v=[],p]=pe({prop:f,defaultProp:d,onChange:_=>{[...x.current][N.current]?.focus(),m(_)}}),k=i.useRef(v);function C(_){const W=lo(v,_);F(_,W)}function b(_){F(_,N.current)}function D(){const _=k.current[N.current];v[N.current]!==_&&g(v)}function F(_,W,{commit:le}={commit:!1}){const Fe=ho(o),de=xo(Math.round((_-r)/o)*o+r,Fe),z=As(de,[r,a]);p((X=[])=>{const H=io(X,z,W);if(fo(H,y*o)){N.current=H.indexOf(z);const pt=String(H)!==String(X);return pt&&le&&g(H),pt?H:X}else return X})}return e.jsx(so,{scope:t.__scopeSlider,name:n,disabled:l,min:r,max:a,valueIndexToChangeRef:N,thumbs:x.current,values:v,orientation:c,form:S,children:e.jsx(Je.Provider,{scope:t.__scopeSlider,children:e.jsx(Je.Slot,{scope:t.__scopeSlider,children:e.jsx(j,{"aria-disabled":l,"data-disabled":l?"":void 0,...u,ref:s,onPointerDown:T(u.onPointerDown,()=>{l||(k.current=v)}),min:r,max:a,inverted:w,onSlideStart:l?void 0:C,onSlideMove:l?void 0:b,onSlideEnd:l?void 0:D,onHomeKeyDown:()=>!l&&F(r,0,{commit:!0}),onEndKeyDown:()=>!l&&F(a,v.length-1,{commit:!0}),onStepKeyDown:({event:_,direction:W})=>{if(!l){const de=_s.includes(_.key)||_.shiftKey&&Os.includes(_.key)?10:1,z=N.current,X=v[z],H=o*de*W;F(X+H,z,{commit:!0})}}})})})})});Ls.displayName=ce;var[zs,$s]=Fs(ce,{startEdge:"left",endEdge:"right",size:"width",direction:1}),no=i.forwardRef((t,s)=>{const{min:n,max:r,dir:a,inverted:o,onSlideStart:c,onSlideMove:l,onSlideEnd:y,onStepKeyDown:d,...f}=t,[m,g]=i.useState(null),w=P(s,j=>g(j)),S=i.useRef(void 0),u=dt(a),x=u==="ltr",N=x&&!o||!x&&o;function h(j){const v=S.current||m.getBoundingClientRect(),p=[0,v.width],C=xt(p,N?[n,r]:[r,n]);return S.current=v,C(j-v.left)}return e.jsx(zs,{scope:t.__scopeSlider,startEdge:N?"left":"right",endEdge:N?"right":"left",direction:N?1:-1,size:"width",children:e.jsx(Bs,{dir:u,"data-orientation":"horizontal",...f,ref:w,style:{...f.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:j=>{const v=h(j.clientX);c?.(v)},onSlideMove:j=>{const v=h(j.clientX);l?.(v)},onSlideEnd:()=>{S.current=void 0,y?.()},onStepKeyDown:j=>{const p=Ps[N?"from-left":"from-right"].includes(j.key);d?.({event:j,direction:p?-1:1})}})})}),ro=i.forwardRef((t,s)=>{const{min:n,max:r,inverted:a,onSlideStart:o,onSlideMove:c,onSlideEnd:l,onStepKeyDown:y,...d}=t,f=i.useRef(null),m=P(s,f),g=i.useRef(void 0),w=!a;function S(u){const x=g.current||f.current.getBoundingClientRect(),N=[0,x.height],j=xt(N,w?[r,n]:[n,r]);return g.current=x,j(u-x.top)}return e.jsx(zs,{scope:t.__scopeSlider,startEdge:w?"bottom":"top",endEdge:w?"top":"bottom",size:"height",direction:w?1:-1,children:e.jsx(Bs,{"data-orientation":"vertical",...d,ref:m,style:{...d.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:u=>{const x=S(u.clientY);o?.(x)},onSlideMove:u=>{const x=S(u.clientY);c?.(x)},onSlideEnd:()=>{g.current=void 0,l?.()},onStepKeyDown:u=>{const N=Ps[w?"from-bottom":"from-top"].includes(u.key);y?.({event:u,direction:N?-1:1})}})})}),Bs=i.forwardRef((t,s)=>{const{__scopeSlider:n,onSlideStart:r,onSlideMove:a,onSlideEnd:o,onHomeKeyDown:c,onEndKeyDown:l,onStepKeyDown:y,...d}=t,f=Pe(ce,n);return e.jsx(A.span,{...d,ref:s,onKeyDown:T(t.onKeyDown,m=>{m.key==="Home"?(c(m),m.preventDefault()):m.key==="End"?(l(m),m.preventDefault()):_s.concat(Os).includes(m.key)&&(y(m),m.preventDefault())}),onPointerDown:T(t.onPointerDown,m=>{const g=m.target;g.setPointerCapture(m.pointerId),m.preventDefault(),f.thumbs.has(g)?g.focus():r(m)}),onPointerMove:T(t.onPointerMove,m=>{m.target.hasPointerCapture(m.pointerId)&&a(m)}),onPointerUp:T(t.onPointerUp,m=>{const g=m.target;g.hasPointerCapture(m.pointerId)&&(g.releasePointerCapture(m.pointerId),o(m))})})}),Vs="SliderTrack",Ks=i.forwardRef((t,s)=>{const{__scopeSlider:n,...r}=t,a=Pe(Vs,n);return e.jsx(A.span,{"data-disabled":a.disabled?"":void 0,"data-orientation":a.orientation,...r,ref:s})});Ks.displayName=Vs;var et="SliderRange",Ws=i.forwardRef((t,s)=>{const{__scopeSlider:n,...r}=t,a=Pe(et,n),o=$s(et,n),c=i.useRef(null),l=P(s,c),y=a.values.length,d=a.values.map(g=>Gs(g,a.min,a.max)),f=y>1?Math.min(...d):0,m=100-Math.max(...d);return e.jsx(A.span,{"data-orientation":a.orientation,"data-disabled":a.disabled?"":void 0,...r,ref:l,style:{...t.style,[o.startEdge]:f+"%",[o.endEdge]:m+"%"}})});Ws.displayName=et;var tt="SliderThumb",Us=i.forwardRef((t,s)=>{const n=eo(t.__scopeSlider),[r,a]=i.useState(null),o=P(s,l=>a(l)),c=i.useMemo(()=>r?n().findIndex(l=>l.ref.current===r):-1,[n,r]);return e.jsx(ao,{...t,ref:o,index:c})}),ao=i.forwardRef((t,s)=>{const{__scopeSlider:n,index:r,name:a,...o}=t,c=Pe(tt,n),l=$s(tt,n),[y,d]=i.useState(null),f=P(s,h=>d(h)),m=y?c.form||!!y.closest("form"):!0,g=tn(y),w=c.values[r],S=w===void 0?0:Gs(w,c.min,c.max),u=co(r,c.values.length),x=g?.[l.size],N=x?uo(x,S,l.direction):0;return i.useEffect(()=>{if(y)return c.thumbs.add(y),()=>{c.thumbs.delete(y)}},[y,c.thumbs]),e.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[l.startEdge]:`calc(${S}% + ${N}px)`},children:[e.jsx(Je.ItemSlot,{scope:t.__scopeSlider,children:e.jsx(A.span,{role:"slider","aria-label":t["aria-label"]||u,"aria-valuemin":c.min,"aria-valuenow":w,"aria-valuemax":c.max,"aria-orientation":c.orientation,"data-orientation":c.orientation,"data-disabled":c.disabled?"":void 0,tabIndex:c.disabled?void 0:0,...o,ref:f,style:w===void 0?{display:"none"}:t.style,onFocus:T(t.onFocus,()=>{c.valueIndexToChangeRef.current=r})})}),m&&e.jsx(Hs,{name:a??(c.name?c.name+(c.values.length>1?"[]":""):void 0),form:c.form,value:w},r)]})});Us.displayName=tt;var oo="RadioBubbleInput",Hs=i.forwardRef(({__scopeSlider:t,value:s,...n},r)=>{const a=i.useRef(null),o=P(a,r),c=sn(s);return i.useEffect(()=>{const l=a.current;if(!l)return;const y=window.HTMLInputElement.prototype,f=Object.getOwnPropertyDescriptor(y,"value").set;if(c!==s&&f){const m=new Event("input",{bubbles:!0});f.call(l,s),l.dispatchEvent(m)}},[c,s]),e.jsx(A.input,{style:{display:"none"},...n,ref:o,defaultValue:s})});Hs.displayName=oo;function io(t=[],s,n){const r=[...t];return r[n]=s,r.sort((a,o)=>a-o)}function Gs(t,s,n){const o=100/(n-s)*(t-s);return As(o,[0,100])}function co(t,s){return s>2?`Value ${t+1} of ${s}`:s===2?["Minimum","Maximum"][t]:void 0}function lo(t,s){if(t.length===1)return 0;const n=t.map(a=>Math.abs(a-s)),r=Math.min(...n);return n.indexOf(r)}function uo(t,s,n){const r=t/2,o=xt([0,50],[0,r]);return(r-o(s)*n)*n}function mo(t){return t.slice(0,-1).map((s,n)=>t[n+1]-s)}function fo(t,s){if(s>0){const n=mo(t);return Math.min(...n)>=s}return!0}function xt(t,s){return n=>{if(t[0]===t[1]||s[0]===s[1])return s[0];const r=(s[1]-s[0])/(t[1]-t[0]);return s[0]+r*(n-t[0])}}function ho(t){return(String(t).split(".")[1]||"").length}function xo(t,s){const n=Math.pow(10,s);return Math.round(t*n)/n}var po=Ls,go=Ks,vo=Ws,bo=Us;function yo({className:t,defaultValue:s,value:n,min:r=0,max:a=100,...o}){const c=i.useMemo(()=>Array.isArray(n)?n:Array.isArray(s)?s:[r,a],[n,s,r,a]);return e.jsxs(po,{"data-slot":"slider",defaultValue:s,value:n,min:r,max:a,className:R("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",t),...o,children:[e.jsx(go,{"data-slot":"slider-track",className:R("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:e.jsx(vo,{"data-slot":"slider-range",className:R("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:c.length},(l,y)=>e.jsx(bo,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},y))]})}function jo({template:t,selectedValues:s,numericRange:n,onValuesChange:r,onRangeChange:a,availableValues:o,numericStats:c}){const[l,y]=i.useState(!1),[d,f]=i.useState(""),m=u=>{s.includes(u)?r(s.filter(x=>x!==u)):r([...s,u])},g=t.dataType==="NUMBER",w=s.length>0||n&&c&&(n[0]>c.min||n[1]<c.max),S=o.filter(u=>u.toLowerCase().includes(d.toLowerCase()));return e.jsxs("div",{className:"border-2 border-border-strong rounded-lg bg-card animate-theme-transition",children:[e.jsxs(Xs,{variant:"ghost",onClick:()=>y(!l),className:"w-full justify-between p-4 text-foreground hover:bg-accent/50 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:t.title}),t.unit&&e.jsx(I,{variant:"outline",className:"text-xs border-border text-muted-foreground",children:t.unit}),w&&e.jsx(I,{variant:"default",className:"text-xs bg-primary text-primary-foreground",children:s.length||"range"})]}),l?e.jsx(he,{className:"w-4 h-4"}):e.jsx(fe,{className:"w-4 h-4"})]}),l&&e.jsxs("div",{className:"p-4 pt-0 space-y-3 border-t border-border",children:[t.description&&e.jsx("p",{className:"text-xs text-muted-foreground",children:t.description}),g&&c?e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between text-sm text-muted-foreground",children:[e.jsxs("span",{children:["Диапазон: ",c.min," - ",c.max]}),e.jsxs("span",{children:["Среднее: ",c.avg.toFixed(1)]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(ee,{className:"text-sm text-foreground",children:["Выбранный диапазон: ",n?.[0]||c.min," - ",n?.[1]||c.max]}),e.jsx(yo,{value:n||[c.min,c.max],onValueChange:u=>a([u[0],u[1]]),min:c.min,max:c.max,step:t.tolerance||.1,className:"w-full"})]}),t.tolerance&&e.jsxs("p",{className:"text-xs text-muted-foreground",children:["Допуск: ±",t.tolerance," ",t.unit]})]}):e.jsxs("div",{className:"space-y-3",children:[o.length>5&&e.jsxs("div",{className:"relative",children:[e.jsx(ne,{className:"absolute left-3 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground"}),e.jsx(q,{placeholder:"Поиск значений...",value:d,onChange:u=>f(u.target.value),className:"pl-9 h-8 text-sm",variant:"ghost"})]}),e.jsxs("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:[S.map(u=>e.jsxs("div",{className:"flex items-center space-x-2 group",children:[e.jsx(te,{id:`${t.id}-${u}`,checked:s.includes(u),onCheckedChange:()=>m(u),className:"border-border data-[state=checked]:bg-primary data-[state=checked]:border-primary"}),e.jsx(ee,{htmlFor:`${t.id}-${u}`,className:"text-sm text-foreground cursor-pointer flex-1 group-hover:text-primary transition-colors",children:u})]},u)),S.length===0&&d&&e.jsxs("p",{className:"text-sm text-muted-foreground text-center py-2",children:['Ничего не найдено по запросу "',d,'"']}),o.length===0&&e.jsx("p",{className:"text-sm text-muted-foreground text-center py-2",children:"Нет доступных значений"})]})]})]})]})}function No({filters:t,setFilters:s,activeFiltersCount:n,onClearAll:r,mockCategories:a,mockBrands:o,mockAttributeTemplates:c,availableAttributeValues:l}){const[y,d]=i.useState(!1),[f,m]=i.useState({categories:!0,brands:!0,accuracy:!0}),[g,w]=i.useState(""),[S,u]=i.useState(""),[x,N]=i.useState(""),h=(b,D,F)=>{D.includes(b)?F(D.filter(_=>_!==b)):F([...D,b])},j=b=>m(D=>({...D,[b]:!D[b]})),v=[{value:"EXACT_MATCH",label:"Точное совпадение"},{value:"MATCH_WITH_NOTES",label:"С примечаниями"},{value:"REQUIRES_MODIFICATION",label:"Требует доработки"},{value:"PARTIAL_MATCH",label:"Частичное совпадение"}],p=a.filter(b=>b.name.toLowerCase().includes(g.toLowerCase())),k=o.filter(b=>b.name.toLowerCase().includes(S.toLowerCase())),C=v.filter(b=>b.label.toLowerCase().includes(x.toLowerCase()));return e.jsx("div",{className:"w-72 border-r border-border-strong bg-surface/50",children:e.jsx("div",{className:"sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto",children:e.jsxs("div",{className:"p-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx(Tt,{className:"h-4 w-4 text-primary"}),n>0&&e.jsx("div",{className:"absolute -top-1 -right-1 h-3 w-3 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold",children:n})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"font-semibold text-sm",children:"Фильтры"}),n>0&&e.jsxs("p",{className:"text-xs text-muted-foreground",children:[n," активных"]})]})]}),n>0&&e.jsxs(E,{variant:"outline",size:"sm",onClick:r,className:"h-7 px-2 text-xs gap-1 hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30",children:[e.jsx(rt,{className:"h-3 w-3"}),"Очистить"]})]}),n>0&&e.jsx(O,{variant:"glass",className:"border-primary/20 bg-primary/5",children:e.jsxs(L,{className:"p-3",children:[e.jsx("div",{className:"flex items-center gap-2 mb-2",children:e.jsx("span",{className:"text-xs font-medium",children:"Активные фильтры"})}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[t.categoryIds.length>0&&e.jsxs(I,{variant:"secondary",className:"text-xs h-5",children:["Категории: ",t.categoryIds.length]}),t.brandIds.length>0&&e.jsxs(I,{variant:"secondary",className:"text-xs h-5",children:["Бренды: ",t.brandIds.length]}),t.accuracyLevels&&t.accuracyLevels.length>0&&e.jsxs(I,{variant:"secondary",className:"text-xs h-5",children:["Точность: ",t.accuracyLevels.length]}),t.attributeFilters&&Object.keys(t.attributeFilters).length>0&&e.jsxs(I,{variant:"secondary",className:"text-xs h-5",children:["Параметры: ",Object.keys(t.attributeFilters).length]}),t.isOemOnly&&e.jsx(I,{variant:"secondary",className:"text-xs h-5",children:"Только OEM"})]})]})}),e.jsx(O,{variant:"default",children:e.jsxs(we,{open:f.categories,onOpenChange:()=>j("categories"),children:[e.jsx(Ce,{asChild:!0,children:e.jsx($,{className:"cursor-pointer p-3 hover:bg-accent/5 transition-colors",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(B,{className:"text-sm",children:"Категории"}),t.categoryIds.length>0&&e.jsx(I,{variant:"default",className:"h-4 w-4 rounded-full p-0 text-xs",children:t.categoryIds.length})]}),f.categories?e.jsx(he,{className:"h-3 w-3 text-muted-foreground"}):e.jsx(fe,{className:"h-3 w-3 text-muted-foreground"})]})})}),e.jsx(Se,{children:e.jsxs(L,{className:"p-3 pt-0 space-y-2",children:[a.length>5&&e.jsxs("div",{className:"relative",children:[e.jsx(ne,{className:"absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground"}),e.jsx(q,{placeholder:"Поиск категорий...",value:g,onChange:b=>w(b.target.value),className:"pl-7 h-7 text-xs",variant:"ghost"})]}),p.map(b=>e.jsxs("div",{className:"flex items-center space-x-2 group",children:[e.jsx(te,{id:`category-${b.id}`,checked:t.categoryIds.includes(b.id),onCheckedChange:()=>h(b.id,t.categoryIds,D=>s({...t,categoryIds:D})),className:"data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"}),e.jsx(ee,{htmlFor:`category-${b.id}`,className:"text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors",children:b.name})]},b.id))]})})]})}),e.jsx(O,{variant:"default",children:e.jsxs(we,{open:f.brands,onOpenChange:()=>j("brands"),children:[e.jsx(Ce,{asChild:!0,children:e.jsx($,{className:"cursor-pointer p-3 hover:bg-accent/5 transition-colors",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(B,{className:"text-sm",children:"Производители"}),(t.brandIds.length>0||t.isOemOnly)&&e.jsx(I,{variant:"default",className:"h-4 w-4 rounded-full p-0 text-xs",children:t.brandIds.length+(t.isOemOnly?1:0)})]}),f.brands?e.jsx(he,{className:"h-3 w-3 text-muted-foreground"}):e.jsx(fe,{className:"h-3 w-3 text-muted-foreground"})]})})}),e.jsx(Se,{children:e.jsxs(L,{className:"p-3 pt-0 space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-2 p-2 rounded bg-accent/5 border border-border/30",children:[e.jsx(te,{id:"oem-only",checked:t.isOemOnly,onCheckedChange:b=>s({...t,isOemOnly:!!b}),className:"data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"}),e.jsx(ee,{htmlFor:"oem-only",className:"text-xs cursor-pointer font-medium",children:"Только OEM производители"})]}),e.jsx(At,{className:"bg-border/30"}),o.length>5&&e.jsxs("div",{className:"relative",children:[e.jsx(ne,{className:"absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground"}),e.jsx(q,{placeholder:"Поиск брендов...",value:S,onChange:b=>u(b.target.value),className:"pl-7 h-7 text-xs",variant:"ghost"})]}),k.map(b=>e.jsxs("div",{className:"flex items-center space-x-2 group",children:[e.jsx(te,{id:`brand-${b.id}`,checked:t.brandIds.includes(b.id),onCheckedChange:()=>h(b.id,t.brandIds,D=>s({...t,brandIds:D})),className:"data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"}),e.jsxs(ee,{htmlFor:`brand-${b.id}`,className:"text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors flex items-center gap-1",children:[b.name,b.isOem&&e.jsx(I,{variant:"outline",className:"text-xs h-4",children:"OEM"})]})]},b.id))]})})]})}),e.jsx(O,{variant:"default",children:e.jsxs(we,{open:f.accuracy,onOpenChange:()=>j("accuracy"),children:[e.jsx(Ce,{asChild:!0,children:e.jsx($,{className:"cursor-pointer p-3 hover:bg-accent/5 transition-colors",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(B,{className:"text-sm",children:"Точность применимости"}),t.accuracyLevels&&t.accuracyLevels.length>0&&e.jsx(I,{variant:"default",className:"h-4 w-4 rounded-full p-0 text-xs",children:t.accuracyLevels.length})]}),f.accuracy?e.jsx(he,{className:"h-3 w-3 text-muted-foreground"}):e.jsx(fe,{className:"h-3 w-3 text-muted-foreground"})]})})}),e.jsx(Se,{children:e.jsxs(L,{className:"p-3 pt-0 space-y-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx(ne,{className:"absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground"}),e.jsx(q,{placeholder:"Поиск по точности...",value:x,onChange:b=>N(b.target.value),className:"pl-7 h-7 text-xs",variant:"ghost"})]}),C.map(b=>e.jsxs("div",{className:"flex items-center space-x-2 group",children:[e.jsx(te,{id:`accuracy-${b.value}`,checked:!!t.accuracyLevels?.includes(b.value),onCheckedChange:()=>h(b.value,t.accuracyLevels||[],D=>s({...t,accuracyLevels:D})),className:"data-[state=checked]:bg-primary data-[state=checked]:border-primary h-3 w-3"}),e.jsx(ee,{htmlFor:`accuracy-${b.value}`,className:"text-xs cursor-pointer flex-1 group-hover:text-primary transition-colors",children:e.jsx(ke,{status:b.value,size:"sm",className:"border-0 bg-transparent p-0 text-xs font-normal"})})]},b.value))]})})]})}),e.jsx(O,{variant:"default",children:e.jsxs(we,{open:y,onOpenChange:d,children:[e.jsx(Ce,{asChild:!0,children:e.jsx($,{className:"cursor-pointer p-3 hover:bg-accent/5 transition-colors",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Gn,{className:"h-3 w-3 text-primary"}),e.jsx(B,{className:"text-sm",children:"Технические параметры"}),t.attributeFilters&&Object.keys(t.attributeFilters).length>0&&e.jsx(I,{variant:"default",className:"h-4 w-4 rounded-full p-0 text-xs",children:Object.keys(t.attributeFilters).length})]}),y?e.jsx(he,{className:"h-3 w-3 text-muted-foreground"}):e.jsx(fe,{className:"h-3 w-3 text-muted-foreground"})]})})}),e.jsx(Se,{children:e.jsx(L,{className:"p-3 pt-0 space-y-3",children:c.map(b=>e.jsx(jo,{template:b,selectedValues:t.attributeFilters?.[b.id]?.values||[],numericRange:t.attributeFilters?.[b.id]?.numericRange,onValuesChange:D=>{s({...t,attributeFilters:{...t.attributeFilters||{},[b.id]:{...t.attributeFilters?.[b.id]||{},values:D}}})},onRangeChange:D=>{s({...t,attributeFilters:{...t.attributeFilters||{},[b.id]:{...t.attributeFilters?.[b.id]||{},numericRange:D}}})},availableValues:l.values[b.id]||[],numericStats:l.numericStats[b.id]},b.id))})})]})})]})})})}const wo=[{id:"1",type:"assistant",content:"Привет! Я ваш AI помощник по поиску уплотнений. Опишите что ищете или задайте вопрос о технических характеристиках.",timestamp:new Date,suggestions:["Найти сальник для двигателя","Подобрать уплотнение по размерам","Показать все NBR материалы","Что такое точность применимости?"]}],Co=[{icon:ne,label:"Поиск по описанию",description:"Опишите деталь своими словами",prompt:"Помогите найти уплотнение по описанию"},{icon:Tt,label:"Умные фильтры",description:"Подберу фильтры автоматически",prompt:"Настройте фильтры для моего поиска"},{icon:An,label:"Техническая помощь",description:"Объясню параметры и характеристики",prompt:"Объясните технические характеристики"}];function So({isOpen:t,onToggle:s,onApplyFilters:n,currentFilters:r,resultsCount:a}){const[o,c]=i.useState(wo),[l,y]=i.useState(""),[d,f]=i.useState(!1),[m,g]=i.useState(!1),w=i.useRef(null),S=i.useRef(null),u=()=>w.current?.scrollIntoView({behavior:"smooth"});i.useEffect(()=>{u()},[o]),i.useEffect(()=>{t&&!m&&setTimeout(()=>S.current?.focus(),100)},[t,m]);const x=async()=>{if(!l.trim())return;const p={id:Date.now().toString(),type:"user",content:l,timestamp:new Date};c(k=>[...k,p]),y(""),f(!0),setTimeout(()=>{const k=v(l,r,a);c(C=>[...C,k]),f(!1)},800)},N=p=>{y(p),setTimeout(()=>x(),100)},h=p=>{y(p),setTimeout(()=>x(),100)},j=p=>n(p),v=(p,k,C)=>{const b=p.toLowerCase();return b.includes("сальник")&&b.includes("двигател")?{id:Date.now().toString(),type:"assistant",content:"Отлично! Ищете сальники для двигателя. Я настрою фильтры для более точного поиска:",timestamp:new Date,suggestions:["Какая марка техники?","Нужны размеры сальника?","Какой тип двигателя?"],filterSuggestion:{categoryIds:[1],query:"сальник двигатель"}}:b.includes("размер")||b.includes("диаметр")?{id:Date.now().toString(),type:"assistant",content:"Для точного поиска по размерам укажите внутренний и наружный диаметр. Например: 'внутренний 25мм, наружный 35мм'",timestamp:new Date,suggestions:["25x35x7 мм","30x42x8 мм","Показать популярные размеры"]}:b.includes("материал")||b.includes("nbr")||b.includes("fkm")?{id:Date.now().toString(),type:"assistant",content:"Материал критически важен! NBR подходит для стандартных применений (-40°C до +100°C), FKM - для высоких температур (до +200°C).",timestamp:new Date,filterSuggestion:{attributeFilters:{3:{values:b.includes("nbr")?["NBR"]:b.includes("fkm")?["FKM"]:[]}}}}:{id:Date.now().toString(),type:"assistant",content:`Понял ваш запрос "${p}". Сейчас найдено ${C} позиций. Могу помочь уточнить поиск или объяснить характеристики.`,timestamp:new Date,suggestions:["Уточнить поиск","Объяснить результаты","Показать аналоги"]}};return e.jsx(xe,{open:t,onOpenChange:s,children:e.jsx(De,{className:"w-[480px] sm:w-[540px] p-0 border-l-2 border-border-strong right-0 top-0 fixed h-full m-0 rounded-none",children:e.jsxs("div",{className:"flex flex-col h-full",children:[e.jsx(ct,{className:"px-6 py-4 border-b border-border-strong bg-surface/50",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"h-10 w-10 rounded-xl gradient-ai flex items-center justify-center shadow-lg",children:e.jsx(Ee,{className:"h-5 w-5 text-white"})}),e.jsxs("div",{children:[e.jsx(lt,{className:"text-lg font-bold",children:"AI Ассистент"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Умный помощник по поиску"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(I,{variant:"outline",className:"text-xs",children:[a," результатов"]}),e.jsx(E,{variant:"ghost",size:"icon",onClick:()=>g(!m),className:"h-8 w-8",children:m?e.jsx(Fn,{className:"h-4 w-4"}):e.jsx(Bn,{className:"h-4 w-4"})})]})]})}),m?e.jsx("div",{className:"p-6 flex items-center justify-center h-full",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"h-16 w-16 rounded-full gradient-ai flex items-center justify-center mx-auto mb-4 shadow-lg",children:e.jsx(zn,{className:"h-8 w-8 text-white"})}),e.jsx("h3",{className:"font-semibold mb-2",children:"Чат свернут"}),e.jsxs("p",{className:"text-sm text-muted-foreground mb-4",children:[o.length-1," сообщений в истории"]}),e.jsx(E,{variant:"outline",onClick:()=>g(!1),children:"Развернуть чат"})]})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"px-6 py-4 bg-surface/30 border-b border-border",children:[e.jsx("h3",{className:"text-sm font-semibold mb-3 text-muted-foreground uppercase tracking-wide",children:"Быстрые действия"}),e.jsx("div",{className:"grid grid-cols-1 gap-2",children:Co.map((p,k)=>e.jsxs(E,{variant:"ghost",className:"h-auto p-3 justify-start text-left hover:bg-surface-hover transition-colors",onClick:()=>h(p.prompt),children:[e.jsx("div",{className:"h-8 w-8 rounded-lg bg-ai-primary/10 flex items-center justify-center mr-3",children:e.jsx(p.icon,{className:"h-4 w-4 text-ai-primary"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium text-sm",children:p.label}),e.jsx("div",{className:"text-xs text-muted-foreground",children:p.description})]}),e.jsx(qe,{className:"h-4 w-4 text-muted-foreground"})]},k))})]}),e.jsx("div",{className:"flex-1 px-6 py-4 overflow-y-auto",children:e.jsxs("div",{className:"space-y-6",children:[o.map(p=>e.jsxs("div",{className:"space-y-3 animate-slide-in-right",children:[e.jsxs("div",{className:R("flex gap-3",p.type==="user"?"justify-end":"justify-start"),children:[p.type==="assistant"&&e.jsx("div",{className:"h-8 w-8 rounded-full gradient-ai flex items-center justify-center flex-shrink-0 shadow-md",children:e.jsx(Ee,{className:"h-4 w-4 text-white"})}),e.jsx("div",{className:R("max-w-[85%] rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm",p.type==="user"?"ai-message-user ml-auto":"ai-message-assistant"),children:p.content})]}),p.suggestions&&e.jsx("div",{className:"flex flex-wrap gap-2 ml-11",children:p.suggestions.map((k,C)=>e.jsx(E,{variant:"outline",size:"sm",className:"h-8 text-xs ai-suggestion",onClick:()=>N(k),children:k},C))}),p.filterSuggestion&&e.jsx("div",{className:"ml-11",children:e.jsx(O,{variant:"default",className:"p-4 border-ai-primary/20 bg-ai-primary/5",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"h-8 w-8 rounded-lg bg-ai-primary/20 flex items-center justify-center",children:e.jsx(Ot,{className:"h-4 w-4 text-ai-primary"})}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium",children:"Применить умные фильтры"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Настроено на основе вашего запроса"})]})]}),e.jsx(E,{size:"sm",className:"gradient-ai text-white shadow-md",onClick:()=>j(p.filterSuggestion),children:"Применить"})]})})})]},p.id)),d&&e.jsxs("div",{className:"flex gap-3 animate-slide-in-right",children:[e.jsx("div",{className:"h-8 w-8 rounded-full gradient-ai flex items-center justify-center flex-shrink-0 shadow-md",children:e.jsx(Ee,{className:"h-4 w-4 text-white"})}),e.jsx("div",{className:"ai-message-assistant rounded-2xl px-4 py-3 text-sm",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex gap-1",children:[e.jsx("div",{className:"w-2 h-2 bg-ai-primary rounded-full animate-bounce",style:{animationDelay:"0ms"}}),e.jsx("div",{className:"w-2 h-2 bg-ai-primary rounded-full animate-bounce",style:{animationDelay:"150ms"}}),e.jsx("div",{className:"w-2 h-2 bg-ai-primary rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),e.jsx("span",{className:"text-muted-foreground",children:"Анализирую запрос..."})]})})]}),e.jsx("div",{ref:w})]})}),e.jsxs("div",{className:"p-6 border-t border-border-strong bg-surface/30",children:[e.jsxs("div",{className:"flex gap-3",children:[e.jsx(q,{ref:S,placeholder:"Опишите что ищете или задайте вопрос...",value:l,onChange:p=>y(p.target.value),onKeyDown:p=>{p.key==="Enter"&&!p.shiftKey&&(p.preventDefault(),x())},className:"flex-1 h-12",variant:"filled"}),e.jsx(E,{onClick:x,disabled:!l.trim()||d,size:"icon",className:"h-12 w-12 gradient-ai text-white shadow-md",children:e.jsx(Un,{className:"h-5 w-5"})})]}),e.jsxs("div",{className:"flex items-center gap-2 mt-3 text-xs text-muted-foreground",children:[e.jsx("span",{className:"text-ai-primary",children:"AI"}),e.jsx("span",{children:"поможет найти нужные детали и настроить фильтры автоматически"})]})]})]})]})})})}function ko({query:t,resultsCount:s,onApplyFilters:n,onUpdateQuery:r}){const[a,o]=i.useState([]),[c,l]=i.useState(!1);i.useEffect(()=>{const f=setTimeout(()=>{const m=[];s===0&&t&&(m.push({id:"no-results-1",type:"search",title:"Попробуйте упростить запрос",description:"Используйте более общие термины",action:"Упростить"}),m.push({id:"no-results-2",type:"filter",title:"Расширить поиск по категориям",description:"Включить все категории уплотнений",action:"Расширить",filters:{categoryIds:[]}})),s>0&&s<5&&t&&m.push({id:"few-results-1",type:"search",title:"Найти похожие детали",description:"Поиск по синонимам и аналогам",action:"Найти похожие"}),s>50&&(m.push({id:"many-results-1",type:"filter",title:"Уточнить по бренду",description:"Выберите предпочтительного производителя",action:"Показать бренды"}),m.push({id:"many-results-2",type:"tip",title:"Добавьте технические параметры",description:"Укажите размеры или материал для точного поиска",action:"Добавить параметры"})),t.toLowerCase().includes("сальник")&&!t.toLowerCase().includes("двигател")&&m.push({id:"smart-1",type:"filter",title:"Сальники двигателя",description:"Самая популярная категория",action:"Показать",filters:{categoryIds:[1]}}),o(m),l(m.length>0)},300);return()=>clearTimeout(f)},[t,s]);const y=d=>{if(d.filters)n(d.filters);else if(d.type==="search"&&d.id==="no-results-1"){const f=t.split(" ").slice(0,2).join(" ");r(f)}};return c?e.jsx(O,{variant:"glass",className:"mb-4 border-primary/20 bg-primary/5",children:e.jsx(L,{className:"p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"h-8 w-8 rounded-full bg-gradient-to-br from-primary to-accent flex items-center justify-center flex-shrink-0",children:e.jsx(bt,{className:"h-4 w-4 text-primary-foreground"})}),e.jsxs("div",{className:"flex-1 space-y-3",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-sm mb-1",children:"AI предложения"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Основано на вашем запросе и результатах поиска"})]}),e.jsx("div",{className:"space-y-2",children:a.map(d=>e.jsxs("div",{className:"flex items-center justify-between p-3 rounded-lg bg-background/50 border border-border/30 hover:border-primary/30 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"h-6 w-6 rounded-full bg-muted flex items-center justify-center",children:[d.type==="search"&&e.jsx(_t,{className:"h-3 w-3"}),d.type==="filter"&&e.jsx(Ot,{className:"h-3 w-3"}),d.type==="tip"&&e.jsx(bt,{className:"h-3 w-3"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-sm",children:d.title}),e.jsx("div",{className:"text-xs text-muted-foreground",children:d.description})]})]}),e.jsx(E,{variant:"outline",size:"sm",onClick:()=>y(d),className:"h-7 text-xs",children:d.action})]},d.id))})]})]})})}):null}function He({mediaAssets:t,primaryImage:s,title:n,className:r}){const[a,o]=i.useState(0),[c,l]=i.useState(!1),[y,d]=i.useState(!1),[f,m]=i.useState(null),g=[...s?[s]:[],...t.filter(v=>v.id!==s?.id)];if(g.length===0)return e.jsx("div",{className:R("flex items-center justify-center h-48 bg-muted/30 rounded-lg border-2 border-dashed border-border",r),children:e.jsxs("div",{className:"text-center",children:[e.jsx(nt,{className:"h-12 w-12 text-muted-foreground mx-auto mb-2"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Изображения отсутствуют"})]})});const w=g[a],S=w?.mimeType.startsWith("image/"),u=w?.mimeType==="application/pdf",x=v=>{if(!v)return"";const p=["B","KB","MB","GB"],k=Math.floor(Math.log(v)/Math.log(1024));return`${Math.round(v/Math.pow(1024,k)*100)/100} ${p[k]}`},N=(v,p)=>{o(p),m(v),v.mimeType.startsWith("image/")?l(!0):v.mimeType==="application/pdf"&&d(!0)},h=()=>o(v=>(v+1)%g.length),j=()=>o(v=>(v-1+g.length)%g.length);return e.jsxs("div",{className:R("space-y-4",r),children:[e.jsxs("div",{className:"relative group",children:[e.jsx("div",{className:"aspect-video bg-muted/30 rounded-lg overflow-hidden border-2 border-border-strong",children:S?e.jsx("img",{src:w.url||"/placeholder.svg",alt:w.fileName,className:"w-full h-full object-contain cursor-zoom-in hover:scale-105 transition-transform duration-300",onClick:()=>N(w,a),loading:"lazy"}):u?e.jsx("div",{className:"w-full h-full flex items-center justify-center cursor-pointer hover:bg-muted/50 transition-colors",onClick:()=>N(w,a),children:e.jsxs("div",{className:"text-center",children:[e.jsx(se,{className:"h-16 w-16 text-primary mx-auto mb-4"}),e.jsx("h3",{className:"font-semibold mb-2",children:w.fileName}),e.jsx(I,{variant:"outline",className:"mb-2",children:"PDF"}),w.fileSize&&e.jsx("p",{className:"text-sm text-muted-foreground",children:x(w.fileSize)})]})}):e.jsx("div",{className:"w-full h-full flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(se,{className:"h-16 w-16 text-muted-foreground mx-auto mb-4"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Неподдерживаемый формат"})]})})}),g.length>1&&e.jsxs(e.Fragment,{children:[e.jsx(E,{variant:"ghost",size:"icon",className:"absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-background/80 backdrop-blur-sm",onClick:j,children:e.jsx(vt,{className:"h-4 w-4"})}),e.jsx(E,{variant:"ghost",size:"icon",className:"absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-background/80 backdrop-blur-sm",onClick:h,children:e.jsx(qe,{className:"h-4 w-4"})})]}),e.jsxs("div",{className:"absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[S&&e.jsx(E,{variant:"ghost",size:"icon",className:"bg-background/80 backdrop-blur-sm",onClick:()=>N(w,a),children:e.jsx(Jn,{className:"h-4 w-4"})}),e.jsx(E,{variant:"ghost",size:"icon",className:"bg-background/80 backdrop-blur-sm",onClick:()=>window.open(w.url,"_blank"),children:e.jsx(Ye,{className:"h-4 w-4"})})]}),e.jsxs("div",{className:"absolute bottom-2 left-2 flex gap-2",children:[e.jsxs(I,{variant:"secondary",className:"bg-background/80 backdrop-blur-sm",children:[a+1," / ",g.length]}),e.jsx(I,{variant:"outline",className:"bg-background/80 backdrop-blur-sm",children:w.mimeType.split("/")[1].toUpperCase()})]})]}),g.length>1&&e.jsx("div",{className:"flex gap-2 overflow-x-auto pb-2",children:g.map((v,p)=>e.jsx("button",{onClick:()=>o(p),className:R("flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all",a===p?"border-primary shadow-md":"border-border hover:border-border-strong"),children:v.mimeType.startsWith("image/")?e.jsx("img",{src:v.url||"/placeholder.svg",alt:v.fileName,className:"w-full h-full object-cover",loading:"lazy"}):e.jsx("div",{className:"w-full h-full flex items-center justify-center bg-muted/50",children:e.jsx(se,{className:"h-6 w-6 text-muted-foreground"})})},v.id))}),e.jsx(xe,{open:c,onOpenChange:l,children:e.jsx(De,{className:"max-w-7xl max-h-[95vh] p-0 bg-black/95",children:e.jsxs("div",{className:"relative w-full h-[90vh] flex items-center justify-center",children:[e.jsx("img",{src:f?.url||"/placeholder.svg",alt:f?.fileName||"image",className:"max-w-full max-h-full object-contain"}),e.jsx(E,{variant:"ghost",size:"icon",className:"absolute top-4 right-4 text-white hover:bg-white/20",onClick:()=>l(!1),children:e.jsx(rt,{className:"h-6 w-6"})}),g.length>1&&e.jsxs(e.Fragment,{children:[e.jsx(E,{variant:"ghost",size:"icon",className:"absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20",onClick:j,children:e.jsx(vt,{className:"h-6 w-6"})}),e.jsx(E,{variant:"ghost",size:"icon",className:"absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20",onClick:h,children:e.jsx(qe,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"absolute bottom-4 left-1/2 -translate-x-1/2 flex items-center gap-4 bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2 text-white",children:[e.jsx("span",{className:"text-sm",children:f?.fileName}),e.jsxs(I,{variant:"outline",className:"border-white/30 text-white",children:[a+1," / ",g.length]}),f?.fileSize&&e.jsx("span",{className:"text-sm opacity-70",children:x(f.fileSize)})]})]})})}),e.jsx(xe,{open:y,onOpenChange:d,children:e.jsxs(De,{className:"max-w-6xl max-h-[95vh]",children:[e.jsx(ct,{children:e.jsxs(lt,{className:"flex items-center justify-between",children:[e.jsx("span",{children:f?.fileName}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(E,{variant:"outline",size:"sm",onClick:()=>window.open(f?.url||"","_blank"),children:[" ",e.jsx(Ye,{className:"h-4 w-4 mr-2"})," Скачать "]}),e.jsxs(E,{variant:"outline",size:"sm",onClick:()=>window.print(),children:[" ",e.jsx(se,{className:"h-4 w-4 mr-2"})," Печать "]})]})]})}),e.jsx("div",{className:"w-full h-[70vh] border rounded-lg overflow-hidden",children:e.jsx("iframe",{src:f?.url,className:"w-full h-full",title:f?.fileName||"pdf"})})]})})]})}function Eo({mediaAsset:t,className:s,size:n="md",showBadge:r=!0}){const a={sm:"w-12 h-12",md:"w-16 h-16",lg:"w-24 h-24"};if(!t)return e.jsx("div",{className:R("flex items-center justify-center bg-muted/30 rounded-lg border border-border",a[n],s),children:e.jsx(nt,{className:"h-1/2 w-1/2 text-muted-foreground"})});const o=t.mimeType.startsWith("image/"),c=t.mimeType==="application/pdf";return e.jsxs("div",{className:R("relative",s),children:[e.jsx("div",{className:R("rounded-lg overflow-hidden border border-border-strong",a[n]),children:o?e.jsx("img",{src:t.url||"/placeholder.svg",alt:t.fileName,className:"w-full h-full object-cover",loading:"lazy"}):e.jsx("div",{className:"w-full h-full flex items-center justify-center bg-muted/50",children:e.jsx(se,{className:"h-1/2 w-1/2 text-muted-foreground"})})}),r&&e.jsx(I,{variant:"secondary",className:"absolute -top-1 -right-1 text-xs h-5 px-1",children:c?"PDF":t.mimeType.split("/")[1].toUpperCase()})]})}function Io(t){const s={};if(t.query&&t.query.trim()){const n=t.query.trim();s.OR=[{sku:{contains:n,mode:"insensitive"}},{description:{contains:n,mode:"insensitive"}}]}if(t.brandIds&&t.brandIds.length>0&&(s.brandId={in:t.brandIds}),t.isOemOnly&&(s.brand={isOem:!0}),t.categoryIds&&t.categoryIds.length>0&&(s.applicabilities={some:{part:{partCategoryId:{in:t.categoryIds}}}}),t.accuracyLevels&&t.accuracyLevels.length>0&&(s.applicabilities={...s.applicabilities||{},some:{...s.applicabilities?.some||{},accuracy:{in:t.accuracyLevels}}}),t.attributeFilters&&Object.keys(t.attributeFilters).length>0){const n=[];for(const[r,a]of Object.entries(t.attributeFilters)){const c={templateId:Number.parseInt(r)};if(a.values&&a.values.length>0&&(c.value={in:a.values}),a.numericRange&&a.numericRange.length===2){const[l,y]=a.numericRange;c.numericValue={gte:l,lte:y}}n.push(c)}n.length>0&&(s.attributes={some:{AND:n}})}return s}function Mo(){const[t,s]=i.useState({query:"",categoryIds:[],brandIds:[],attributeFilters:{},accuracyLevels:[],isOemOnly:!1});i.useMemo(()=>Io(t),[t]);const{data:n,isLoading:r}=nn.site.search.catalogItems.useQuery({search:t.query||void 0,brandIds:t.brandIds,categoryIds:t.categoryIds,isOemOnly:t.isOemOnly,accuracy:t.accuracyLevels||void 0,attributeFilters:Object.entries(t.attributeFilters||{}).map(([o,c])=>({templateId:Number(o),values:c.values?.length?c.values:void 0,minValue:c.numericRange?.[0],maxValue:c.numericRange?.[1]})),limit:50,offset:0,sortBy:"updatedAt",sortDir:"desc"}),a=i.useMemo(()=>{if(!n?.items)return[];const o=[];for(const c of n.items)if(c.applicabilities)for(const l of c.applicabilities)o.push({id:l.id,partId:l.partId,catalogItemId:l.catalogItemId,accuracy:l.accuracy,notes:l.notes,part:l.part,catalogItem:{id:c.id,sku:c.sku,description:c.description,brandId:c.brandId,brand:c.brand,isPublic:!0,attributes:c.attributes,image:c.image??null,mediaAssets:c.mediaAssets??[]}});return o},[n]);return{results:a,filters:t,setFilters:s,totalCount:n?.total||0,filteredCount:a.length,isLoading:r}}const Ro=[{id:1,name:"Сальники двигателя",slug:"engine-seals",level:1,path:"01/01"},{id:2,name:"Сальники трансмиссии",slug:"transmission-seals",level:1,path:"01/02"},{id:3,name:"Уплотнения гидравлики",slug:"hydraulic-seals",level:1,path:"01/03"}],Do=[{id:1,name:"SKF",slug:"skf",isOem:!1},{id:2,name:"Freudenberg",slug:"freudenberg",isOem:!1},{id:3,name:"Caterpillar",slug:"caterpillar",isOem:!0},{id:4,name:"Komatsu",slug:"komatsu",isOem:!0}],To=[{id:1,name:"inner_diameter",title:"Внутренний диаметр",dataType:"NUMBER",unit:"MM",isRequired:!0,allowedValues:[],tolerance:.1},{id:2,name:"outer_diameter",title:"Наружный диаметр",dataType:"NUMBER",unit:"MM",isRequired:!0,allowedValues:[],tolerance:.1},{id:3,name:"material",title:"Материал",dataType:"STRING",isRequired:!0,allowedValues:["NBR","FKM","PTFE","EPDM"]}];function Ao(){const{results:t,filters:s,setFilters:n,totalCount:r,filteredCount:a}=Mo(),[o,c]=i.useState("detailed"),[l,y]=i.useState([]),[d,f]=i.useState(null),[m,g]=i.useState(!1),w=i.useMemo(()=>{const h={},j={};return t.forEach(v=>{v.catalogItem.attributes.forEach(p=>{p.template.dataType==="NUMBER"&&p.numericValue!==void 0&&p.numericValue!==null?j[p.templateId]?(j[p.templateId].min=Math.min(j[p.templateId].min,p.numericValue),j[p.templateId].max=Math.max(j[p.templateId].max,p.numericValue)):j[p.templateId]={min:p.numericValue,max:p.numericValue,avg:0}:(h[p.templateId]||(h[p.templateId]=[]),h[p.templateId].includes(p.value)||h[p.templateId].push(p.value))})}),Object.keys(j).forEach(v=>{const p=Number.parseInt(v),k=t.flatMap(C=>C.catalogItem.attributes).filter(C=>C.templateId===p&&C.numericValue!==void 0&&C.numericValue!==null).map(C=>C.numericValue);k.length>0&&(j[p].avg=k.reduce((C,b)=>C+b,0)/k.length)}),{values:h,numericStats:j}},[t]),S=()=>{n({query:"",categoryIds:[],brandIds:[],attributeFilters:{},accuracyLevels:[],isOemOnly:!1})},u=s.categoryIds.length+s.brandIds.length+Object.keys(s.attributeFilters).length+s.accuracyLevels.length+(s.isOemOnly?1:0),x=h=>{n({...s,...h})},N=h=>n({...s,query:h});return e.jsxs("div",{className:"min-h-screen bg-background",children:[e.jsx(Ya,{totalCount:r,filteredCount:a,searchQuery:s.query,onSearchChange:h=>n({...s,query:h}),onOpenAI:()=>g(!0)}),e.jsxs("div",{className:"flex",children:[e.jsx(No,{filters:s,setFilters:n,activeFiltersCount:u,onClearAll:S,mockCategories:Ro,mockBrands:Do,mockAttributeTemplates:To,availableAttributeValues:w}),e.jsx("div",{className:"flex-1 overflow-y-auto",children:e.jsxs("div",{className:"container max-w-none p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("h2",{className:"text-xl font-bold tracking-tight",children:"Результаты поиска"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(I,{variant:"secondary",className:"px-2 py-1 font-medium text-xs",children:[e.jsx(gt,{className:"h-3 w-3 mr-1"}),a," позиций"]}),a!==r&&e.jsxs(I,{variant:"outline",className:"px-2 py-1 text-xs",children:["из ",r," общих"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-1 p-1 bg-muted/50 rounded border border-border/40",children:[e.jsx(E,{variant:o==="detailed"?"default":"ghost",size:"sm",onClick:()=>c("detailed"),className:"h-7 w-7 p-0",children:e.jsx(On,{className:"h-3 w-3"})}),e.jsx(E,{variant:o==="grid"?"default":"ghost",size:"sm",onClick:()=>c("grid"),className:"h-7 w-7 p-0",children:e.jsx(Rn,{className:"h-3 w-3"})}),e.jsx(E,{variant:o==="table"?"default":"ghost",size:"sm",onClick:()=>c("table"),className:"h-7 w-7 p-0",children:e.jsx(vn,{className:"h-3 w-3"})})]}),e.jsxs(E,{variant:"outline",size:"sm",className:"gap-1 h-7 px-2 text-xs",children:[e.jsx(on,{className:"h-3 w-3"}),"Сравнить",l.length>0&&e.jsx(I,{variant:"secondary",className:"ml-1 h-4 w-4 rounded-full p-0 text-xs",children:l.length})]})]})]}),e.jsx(ko,{query:s.query,resultsCount:a,onApplyFilters:x,onUpdateQuery:N}),t.length===0?e.jsx(O,{variant:"elevated",className:"text-center py-12 border-2 border-dashed border-border-strong",children:e.jsx(L,{children:e.jsxs("div",{className:"flex flex-col items-center gap-3",children:[e.jsx("div",{className:"h-12 w-12 rounded-full bg-muted/50 flex items-center justify-center",children:e.jsx(gt,{className:"h-6 w-6 text-muted-foreground"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-1",children:"Ничего не найдено"}),e.jsx("p",{className:"text-muted-foreground text-sm max-w-md",children:"Попробуйте изменить критерии поиска или воспользуйтесь AI ассистентом для помощи"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(E,{variant:"outline",onClick:S,size:"sm",children:"Очистить фильтры"}),e.jsx(E,{variant:"gradient",onClick:()=>g(!0),size:"sm",children:"Спросить AI"})]})]})})}):e.jsx("div",{className:"space-y-3 animate-fade-in",children:t.map((h,j)=>e.jsx(O,{variant:"elevated",className:"group hover:shadow-strong transition-all duration-200 animate-slide-up border hover:border-primary/20",style:{animationDelay:`${j*30}ms`},children:e.jsx(L,{className:"p-4",children:e.jsxs("div",{className:"flex gap-4",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(Eo,{mediaAsset:h.catalogItem.image??void 0,size:"md",className:"cursor-pointer hover:scale-105 transition-transform"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(te,{checked:l.includes(h.id),onCheckedChange:v=>{y(v?[...l,h.id]:l.filter(p=>p!==h.id))},className:"data-[state=checked]:bg-primary data-[state=checked]:border-primary h-4 w-4"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("h3",{className:"text-lg font-bold text-primary font-mono tracking-tight truncate",children:h.catalogItem.sku}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[e.jsxs(I,{variant:"outline",className:"font-medium text-xs h-5",children:[e.jsx(hn,{className:"h-3 w-3 mr-1"}),h.catalogItem.brand.name]}),h.catalogItem.brand.isOem&&e.jsx(I,{variant:"secondary",className:"text-xs h-5",children:"OEM"}),h.catalogItem.mediaAssets.length>0&&e.jsxs(I,{variant:"outline",className:"text-xs h-5",children:[e.jsx(nt,{className:"h-3 w-3 mr-1"}),h.catalogItem.mediaAssets.length]})]})]})]}),e.jsxs("div",{className:"space-y-1 mb-3",children:[e.jsx("h4",{className:"font-semibold text-sm",children:h.part.name}),e.jsx("p",{className:"text-muted-foreground text-sm leading-relaxed line-clamp-2",children:h.catalogItem.description})]}),h.notes&&e.jsx("div",{className:"p-2 rounded bg-info/10 border border-info/20 mb-3",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("div",{className:"h-4 w-4 rounded-full bg-info/20 flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("div",{className:"h-1.5 w-1.5 rounded-full bg-info"})}),e.jsx("p",{className:"text-xs text-info-foreground leading-relaxed",children:h.notes})]})})]}),e.jsxs("div",{className:"flex flex-col items-end gap-2 ml-4",children:[e.jsx(ke,{status:h.accuracy,size:"sm"}),e.jsx(I,{variant:"outline",className:"text-xs",children:h.part.partCategory.name})]})]}),e.jsxs("div",{className:"flex flex-wrap gap-1 mb-3",children:[h.catalogItem.attributes.slice(0,6).map(v=>e.jsxs("div",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full bg-card border border-border-strong hover:border-primary/40 transition-colors text-xs",children:[e.jsxs("span",{className:"text-muted-foreground font-medium truncate max-w-[80px]",title:v.template.title,children:[v.template.title.split(" ")[0],":"]}),e.jsx("span",{className:"font-mono font-semibold",children:v.value}),v.template.unit&&e.jsx("span",{className:"text-muted-foreground",children:v.template.unit})]},v.id)),h.catalogItem.attributes.length>6&&e.jsxs("div",{className:"inline-flex items-center px-2 py-1 rounded-full bg-muted/50 border border-border text-xs text-muted-foreground",children:["+",h.catalogItem.attributes.length-6," еще"]})]}),e.jsxs("div",{className:"flex items-center justify-between pt-2 border-t border-border",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx(Cn,{className:"h-3 w-3"}),e.jsx("span",{children:new Date(h.part.updatedAt).toLocaleDateString("ru-RU")})]}),e.jsx(At,{orientation:"vertical",className:"h-3"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsxs(I,{variant:"outline",className:"text-xs font-mono h-4 px-1",children:["#",h.partId]}),e.jsxs(I,{variant:"outline",className:"text-xs font-mono h-4 px-1",children:["#",h.catalogItemId]})]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(xe,{children:e.jsx(Sa,{asChild:!0,children:e.jsxs(E,{variant:"outline",size:"sm",onClick:()=>setSelectedApplicibility(h),className:"gap-1 h-7 px-2 text-xs",children:[e.jsx(En,{className:"h-3 w-3"}),"Подробности"]})})}),e.jsxs(E,{variant:"outline",size:"sm",className:"gap-1 h-7 px-2 text-xs",children:[e.jsx(se,{className:"h-3 w-3"}),"Техданные"]}),e.jsxs(E,{variant:"gradient",size:"sm",className:"gap-1 h-7 px-2 text-xs",children:[e.jsx(_t,{className:"h-3 w-3"}),"В корзину"]})]})]})]})]})})},h.id))})]})})]}),e.jsx(So,{isOpen:m,onToggle:()=>g(!m),onApplyFilters:x,currentFilters:s,resultsCount:a}),d&&e.jsx(xe,{open:!!d,onOpenChange:()=>f(null),children:e.jsxs(De,{className:"max-w-6xl max-h-[90vh] overflow-y-auto bg-card border-border/40",children:[e.jsxs(ct,{children:[e.jsxs(lt,{className:"text-xl font-bold flex items-center justify-between",children:[e.jsx("span",{className:"font-mono",children:d.catalogItem.sku}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(I,{variant:"outline",children:d.catalogItem.brand.name}),e.jsx(ke,{status:d.accuracy})]})]}),e.jsx("p",{className:"text-muted-foreground leading-relaxed",children:d.catalogItem.description})]}),e.jsxs(Ga,{defaultValue:"overview",className:"mt-4",children:[e.jsxs(qa,{className:"grid w-full grid-cols-5 bg-muted/50",children:[e.jsx(ue,{value:"overview",children:"Обзор"}),e.jsx(ue,{value:"media",children:"Медиа"}),e.jsx(ue,{value:"part",children:"Эталонная группа"}),e.jsx(ue,{value:"attributes",children:"Атрибуты"}),e.jsx(ue,{value:"applicability",children:"Применимость"})]}),e.jsxs(me,{value:"overview",className:"space-y-4 mt-4",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-3",children:"Изображения и документы"}),e.jsx(He,{mediaAssets:d.catalogItem.mediaAssets,primaryImage:d.catalogItem.image??null,title:d.catalogItem.sku})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs(O,{variant:"glass",children:[e.jsx($,{children:e.jsx(B,{className:"text-sm",children:"Информация о товаре"})}),e.jsx(L,{className:"space-y-3",children:e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Артикул"}),e.jsx("div",{className:"font-mono font-semibold text-sm",children:d.catalogItem.sku})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Бренд"}),e.jsx("div",{className:"font-semibold text-sm",children:d.catalogItem.brand.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Страна"}),e.jsx("div",{className:"font-semibold text-sm",children:d.catalogItem.brand.country||"Не указана"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Тип"}),e.jsx("div",{className:"font-semibold text-sm",children:d.catalogItem.brand.isOem?"OEM":"Aftermarket"})]})]})})]}),e.jsxs(O,{variant:"glass",children:[e.jsx($,{children:e.jsx(B,{className:"text-sm",children:"Эталонная группа"})}),e.jsxs(L,{className:"space-y-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Название группы"}),e.jsx("div",{className:"font-semibold text-sm",children:d.part.name||"Без названия"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Категория"}),e.jsx("div",{className:"font-semibold text-sm",children:d.part.partCategory.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Путь в иерархии"}),e.jsx("div",{className:"font-mono text-xs",children:d.part.path})]})]})]})]})]}),d.notes&&e.jsxs(O,{variant:"glass",className:"border-info/20 bg-info/5",children:[e.jsx($,{children:e.jsxs(B,{className:"text-info flex items-center gap-2 text-sm",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-info"}),"Примечания по применимости"]})}),e.jsx(L,{children:e.jsx("p",{className:"leading-relaxed text-sm",children:d.notes})})]})]}),e.jsx(me,{value:"media",className:"space-y-4 mt-4",children:e.jsx(He,{mediaAssets:d.catalogItem.mediaAssets,primaryImage:d.catalogItem.image??null,title:d.catalogItem.sku})}),e.jsx(me,{value:"part",className:"space-y-4 mt-4",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-3 text-sm",children:"Медиа эталонной группы"}),e.jsx(He,{mediaAssets:d.part.mediaAssets,primaryImage:d.part.image??null,title:d.part.name||"Эталонная группа"})]}),e.jsxs(O,{variant:"glass",children:[e.jsx($,{children:e.jsx(B,{className:"text-sm",children:"Эталонные атрибуты группы"})}),e.jsx(L,{children:e.jsx("div",{className:"space-y-2",children:d.part.attributes.map(h=>e.jsxs("div",{className:"flex items-center justify-between p-2 rounded bg-card border border-border-strong hover:border-primary/40 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium text-sm",children:h.template.title}),h.template.unit&&e.jsx(I,{variant:"outline",className:"text-xs h-4",children:h.template.unit})]}),e.jsx("div",{className:"font-mono font-bold text-sm",children:h.value})]},h.id))})})]})]})}),e.jsx(me,{value:"attributes",className:"space-y-4 mt-4",children:e.jsxs(O,{variant:"glass",children:[e.jsx($,{children:e.jsx(B,{className:"text-sm",children:"Атрибуты каталожной позиции"})}),e.jsx(L,{children:e.jsx("div",{className:"space-y-2",children:d.catalogItem.attributes.map(h=>e.jsxs("div",{className:"flex items-center justify-between p-2 rounded bg-card border border-border-strong hover:border-primary/40 transition-colors",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium text-sm",children:h.template.title}),h.template.unit&&e.jsx(I,{variant:"outline",className:"text-xs h-4",children:h.template.unit})]}),e.jsx("div",{className:"font-mono font-bold text-sm",children:h.value})]},h.id))})})]})}),e.jsx(me,{value:"applicability",className:"space-y-4 mt-4",children:e.jsxs(O,{variant:"glass",children:[e.jsx($,{children:e.jsx(B,{className:"text-sm",children:"Детали применимости"})}),e.jsxs(L,{className:"space-y-3",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Точность совпадения"}),e.jsx("div",{className:"mt-1",children:e.jsx(ke,{status:d.accuracy})})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"ID применимости"}),e.jsxs("div",{className:"font-mono font-semibold text-sm",children:["#",d.id]})]})]}),d.notes&&e.jsxs("div",{className:"p-3 rounded bg-info/10 border border-info/20",children:[e.jsx("h4",{className:"font-medium text-sm mb-2",children:"Примечания:"}),e.jsx("p",{className:"text-sm leading-relaxed",children:d.notes})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 pt-3 border-t border-border",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Создано"}),e.jsx("div",{className:"text-sm",children:new Date(d.part.createdAt).toLocaleString("ru-RU")})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"Обновлено"}),e.jsx("div",{className:"text-sm",children:new Date(d.part.updatedAt).toLocaleString("ru-RU")})]})]})]})]})})]})]})})]})}function Yo(){return e.jsx(rn,{children:e.jsx(Ao,{})})}export{Yo as default};
