"use client"

import { <PERSON>,Bolt, Save, Calculator, Download, <PERSON><PERSON><PERSON>, <PERSON>, User, <PERSON><PERSON> } from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { ModernInput } from "@/components/ui/modern-input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ThemeToggle } from "./theme-toggle"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface ProfessionalHeaderProps {
  totalCount: number
  filteredCount: number
  searchQuery: string
  onSearchChange: (query: string) => void
  onOpenAI: () => void
}

export function ProfessionalHeader({
  totalCount,
  filteredCount,
  searchQuery,
  onSearchChange,
  onOpenAI,
}: ProfessionalHeaderProps) {
  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/80 backdrop-blur-xl">
      <div className="container flex h-16 max-w-screen-2xl items-center justify-between px-6">
        {/* Logo and Title */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="flex bg-zinc-900 h-10 w-10 items-center justify-center rounded-xl shadow-lg">
              <Bolt className="h-5 w-5 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-xl font-bold tracking-tight">Сашакаталог</h1>
              <p className="text-sm text-muted-foreground">Профессиональная система каталогизации</p>
            </div>
          </div>
        </div>

        {/* Global Search */}
        <div className="flex flex-1 items-center justify-center px-8">
          <div className="relative w-full max-w-lg">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <ModernInput
              placeholder="Поиск по артикулу, описанию, бренду..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10 pr-4"
              variant="ghost"
            />
          </div>
        </div>

        {/* Actions and User */}
        <div className="flex items-center gap-3">
          <Badge variant="outline" className="hidden sm:flex">
            {filteredCount} / {totalCount}
          </Badge>

          <div className="flex items-center gap-2">
            <ModernButton variant="ghost" size="icon" className="relative">
              <Bell className="h-4 w-4" />
              <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-destructive"></span>
            </ModernButton>

            <ModernButton variant="outline" size="sm">
              <Save className="h-4 w-4" />
              <span className="hidden sm:inline">Сохранить</span>
            </ModernButton>

            <ModernButton variant="outline" size="sm">
              <Calculator className="h-4 w-4" />
              <span className="hidden sm:inline">Калькулятор</span>
            </ModernButton>

            <ModernButton variant="outline" size="sm">
              <Download className="h-4 w-4" />
              <span className="hidden sm:inline">Экспорт</span>
            </ModernButton>

            <ModernButton variant="gradient" size="sm" onClick={onOpenAI} className="gap-2">
              <Bot className="h-4 w-4" />
              <span className="hidden sm:inline">AI Помощник</span>
            </ModernButton>
          </div>

          <ThemeToggle />

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <ModernButton variant="ghost" size="icon" className="relative h-9 w-9 rounded-full">
                <Avatar className="h-9 w-9">
                  <AvatarImage src="/avatars/01.png" alt="User" />
                  <AvatarFallback className="bg-primary/10 text-primary">ИИ</AvatarFallback>
                </Avatar>
              </ModernButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">Иван Иванов</p>
                  <p className="text-xs leading-none text-muted-foreground"><EMAIL></p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <User className="mr-2 h-4 w-4" />
                <span>Профиль</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                <span>Настройки</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Выйти</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}
