# PartTec 3.0

Система управления каталогом взаимозаменяемых запчастей с современным технологическим стеком.

## 🏗️ Архитектура проекта

```
parttec3/
├── api/                    # Backend API (tRPC + ZenStack + Prisma)
├── frontend/               # Frontend (Astro + Vue 3 + Tailwind + Volt UI)
└── package.json           # Корневые скрипты для управления проектом
```

## 🚀 Технологический стек

### Frontend
- **[Astro](https://astro.build/)** - Современный статический генератор сайтов
- **[Vue 3](https://vuejs.org/)** - Прогрессивный JavaScript фреймворк с Composition API
- **[Tailwind CSS v4+](https://tailwindcss.com/)** - Utility-first CSS фреймворк
- **[PrimeVue](https://primevue.org/)** - Богатый набор UI компонентов
- **[Volt UI](https://volt.primevue.org/)** - Современная дизайн система
- **[TypeScript](https://www.typescriptlang.org/)** - Типизированный JavaScript

### Backend
- **[tRPC](https://trpc.io/)** - Type-safe API
- **[ZenStack](https://zenstack.dev/)** - Полнофункциональный ORM с авторизацией
- **[Prisma](https://www.prisma.io/)** - Современный ORM для TypeScript
- **[Better-auth](https://www.better-auth.com/)** - Современная система аутентификации
- **[PostgreSQL](https://www.postgresql.org/)** - Реляционная база данных
- **[Bun](https://bun.sh/)** - Быстрый JavaScript runtime

## 🚀 Быстрый старт

### Установка зависимостей

```bash
# Установка зависимостей для всего проекта
npm install

# Установка зависимостей для API
cd api && npm install

# Установка зависимостей для Frontend
cd frontend && npm install
```

### Запуск в режиме разработки

```bash
# Запуск всего проекта (API + Frontend)
npm run dev

# Или запуск отдельно:
npm run dev:api      # Только API
npm run dev:frontend # Только Frontend
```

### Доступ к приложению

- **Frontend**: http://localhost:4321/
- **API**: http://localhost:3000/

## 📦 Основные команды

```bash
# Разработка
npm run dev                 # Запуск API + Frontend
npm run dev:api            # Запуск только API
npm run dev:frontend       # Запуск только Frontend

# Сборка
npm run build              # Сборка Frontend
npm run build:frontend     # Сборка Frontend

# Предварительный просмотр
npm run preview            # Предварительный просмотр Frontend
```

## 🎨 Особенности Frontend

- **Vue Islands**: Vue компоненты гидратируются только при необходимости
- **Volt UI**: Современные компоненты с Tailwind CSS стилизацией
- **TypeScript**: Полная типизация для безопасности разработки
- **Responsive Design**: Адаптивный дизайн для всех устройств
- **Dark Mode**: Поддержка темной темы

## 🔧 Особенности Backend

- **Type-safe API**: Полная типизация между frontend и backend
- **Авторизация**: Встроенная система ролей и разрешений
- **Real-time**: Поддержка WebSocket для real-time обновлений
- **Валидация**: Автоматическая валидация данных с Zod
- **Миграции**: Автоматические миграции базы данных

## 📁 Структура проекта

### Frontend (`/frontend`)
```
frontend/
├── src/
│   ├── components/     # Vue компоненты
│   ├── layouts/        # Astro layouts
│   ├── pages/          # Astro страницы (роутинг)
│   ├── plugins/        # Плагины (PrimeVue и др.)
│   ├── styles/         # Глобальные стили
│   └── volt/           # Volt UI компоненты
└── astro.config.mjs    # Конфигурация Astro
```

### Backend (`/api`)
```
api/
├── routers/           # tRPC роутеры
├── services/          # Бизнес-логика
├── prisma/            # Схема базы данных
├── generated/         # Сгенерированные файлы ZenStack
└── schema.zmodel      # ZenStack схема
```

## 🌟 Готовые возможности

✅ **Полностью настроенный Astro проект**  
✅ **Vue 3 интеграция с TypeScript**  
✅ **Tailwind CSS v4+ с Vite плагином**  
✅ **PrimeVue в unstyled режиме**  
✅ **Volt UI компоненты (Button, Card, InputText)**  
✅ **Адаптивный layout с темизацией**  
✅ **Готовая структура проекта**  
✅ **Алиасы и пути настроены**  
✅ **Dev сервер запущен и работает**  

## 🎯 Следующие шаги

1. **Интеграция с API**: Подключение tRPC клиента
2. **Аутентификация**: Интеграция better-auth
3. **Каталог запчастей**: Создание основных страниц
4. **Интерактивные схемы**: Реализация drag-and-drop редактора
5. **Поиск и фильтрация**: Система поиска запчастей

## 📚 Документация

- [Frontend README](./frontend/README.md) - Подробная документация по frontend
- [API README](./api/README.md) - Документация по backend API

## 🤝 Разработка

Проект готов к разработке! Все основные технологии настроены и интегрированы.
