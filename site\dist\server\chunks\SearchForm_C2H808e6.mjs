import { jsxs, jsx } from 'react/jsx-runtime';
import { useState } from 'react';
import { Search } from 'lucide-react';
import { I as Input } from './input_B2Kb__1-.mjs';
import { B as Button } from './MainLayout_B4WbK6nK.mjs';
import { n as navigate } from './router_vN4ZPF0m.mjs';

function SearchForm({
  onSearch,
  placeholder = "Поиск запчастей...",
  defaultValue = ""
}) {
  const [query, setQuery] = useState(defaultValue);
  const runSearch = (q) => {
    const trimmed = q.trim();
    if (!trimmed) return;
    if (onSearch) {
      onSearch(trimmed);
    } else {
      navigate();
    }
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    runSearch(query);
  };
  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSubmit(e);
    }
  };
  return /* @__PURE__ */ jsxs("form", { onSubmit: handleSubmit, className: "flex w-full max-w-sm items-center space-x-2", children: [
    /* @__PURE__ */ jsxs("div", { className: "relative flex-1", children: [
      /* @__PURE__ */ jsx(Search, { className: "absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" }),
      /* @__PURE__ */ jsx(
        Input,
        {
          type: "text",
          placeholder,
          value: query,
          onChange: (e) => setQuery(e.target.value),
          onKeyDown: handleKeyDown,
          className: "pl-10"
        }
      )
    ] }),
    /* @__PURE__ */ jsx(Button, { type: "submit", size: "sm", children: "Найти" })
  ] });
}

export { SearchForm as S };
