"use client"

import { useState } from "react"
import { ChevronLeft, ChevronRight, Download, FileText, ImageIcon, X, ZoomIn } from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { cn } from "@/lib/utils"
import type { MediaAsset } from "../types/catalog"

interface MediaGalleryProps {
  mediaAssets: MediaAsset[]
  primaryImage?: MediaAsset
  title?: string
  className?: string
}

export function MediaGallery({ mediaAssets, primaryImage, title, className }: MediaGalleryProps) {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [isLightboxOpen, setIsLightboxOpen] = useState(false)
  const [isPDFViewerOpen, setIsPDFViewerOpen] = useState(false)
  const [selectedAsset, setSelectedAsset] = useState<MediaAsset | null>(null)

  // Combine primary image with media assets, avoiding duplicates
  const allAssets = [
    ...(primaryImage ? [primaryImage] : []),
    ...mediaAssets.filter((asset) => asset.id !== primaryImage?.id),
  ]

  if (allAssets.length === 0) {
    return (
      <div
        className={cn(
          "flex items-center justify-center h-48 bg-muted/30 rounded-lg border-2 border-dashed border-border",
          className,
        )}
      >
        <div className="text-center">
          <ImageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Изображения отсутствуют</p>
        </div>
      </div>
    )
  }

  const currentAsset = allAssets[selectedIndex]
  const isImage = currentAsset?.mimeType.startsWith("image/")
  const isPDF = currentAsset?.mimeType === "application/pdf"

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return ""
    const sizes = ["B", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${Math.round((bytes / Math.pow(1024, i)) * 100) / 100} ${sizes[i]}`
  }

  const handleAssetClick = (asset: MediaAsset, index: number) => {
    setSelectedIndex(index)
    setSelectedAsset(asset)

    if (asset.mimeType.startsWith("image/")) {
      setIsLightboxOpen(true)
    } else if (asset.mimeType === "application/pdf") {
      setIsPDFViewerOpen(true)
    }
  }

  const nextImage = () => {
    setSelectedIndex((prev) => (prev + 1) % allAssets.length)
  }

  const prevImage = () => {
    setSelectedIndex((prev) => (prev - 1 + allAssets.length) % allAssets.length)
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Main Display */}
      <div className="relative group">
        <div className="aspect-video bg-muted/30 rounded-lg overflow-hidden border-2 border-border-strong">
          {isImage ? (
            <img
              src={currentAsset.url || "/placeholder.svg"}
              alt={currentAsset.fileName}
              className="w-full h-full object-contain cursor-zoom-in hover:scale-105 transition-transform duration-300"
              onClick={() => handleAssetClick(currentAsset, selectedIndex)}
              loading="lazy"
            />
          ) : isPDF ? (
            <div
              className="w-full h-full flex items-center justify-center cursor-pointer hover:bg-muted/50 transition-colors"
              onClick={() => handleAssetClick(currentAsset, selectedIndex)}
            >
              <div className="text-center">
                <FileText className="h-16 w-16 text-primary mx-auto mb-4" />
                <h3 className="font-semibold mb-2">{currentAsset.fileName}</h3>
                <Badge variant="outline" className="mb-2">
                  PDF
                </Badge>
                {currentAsset.fileSize && (
                  <p className="text-sm text-muted-foreground">{formatFileSize(currentAsset.fileSize)}</p>
                )}
              </div>
            </div>
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center">
                <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <p className="text-sm text-muted-foreground">Неподдерживаемый формат</p>
              </div>
            </div>
          )}
        </div>

        {/* Navigation Arrows */}
        {allAssets.length > 1 && (
          <>
            <ModernButton
              variant="ghost"
              size="icon"
              className="absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-background/80 backdrop-blur-sm"
              onClick={prevImage}
            >
              <ChevronLeft className="h-4 w-4" />
            </ModernButton>
            <ModernButton
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-background/80 backdrop-blur-sm"
              onClick={nextImage}
            >
              <ChevronRight className="h-4 w-4" />
            </ModernButton>
          </>
        )}

        {/* Action Buttons */}
        <div className="absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
          {isImage && (
            <ModernButton
              variant="ghost"
              size="icon"
              className="bg-background/80 backdrop-blur-sm"
              onClick={() => handleAssetClick(currentAsset, selectedIndex)}
            >
              <ZoomIn className="h-4 w-4" />
            </ModernButton>
          )}
          <ModernButton
            variant="ghost"
            size="icon"
            className="bg-background/80 backdrop-blur-sm"
            onClick={() => window.open(currentAsset.url, "_blank")}
          >
            <Download className="h-4 w-4" />
          </ModernButton>
        </div>

        {/* File Info */}
        <div className="absolute bottom-2 left-2 flex gap-2">
          <Badge variant="secondary" className="bg-background/80 backdrop-blur-sm">
            {selectedIndex + 1} / {allAssets.length}
          </Badge>
          <Badge variant="outline" className="bg-background/80 backdrop-blur-sm">
            {currentAsset.mimeType.split("/")[1].toUpperCase()}
          </Badge>
        </div>
      </div>

      {/* Thumbnails */}
      {allAssets.length > 1 && (
        <div className="flex gap-2 overflow-x-auto pb-2">
          {allAssets.map((asset, index) => (
            <button
              key={asset.id}
              onClick={() => setSelectedIndex(index)}
              className={cn(
                "flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all",
                selectedIndex === index ? "border-primary shadow-md" : "border-border hover:border-border-strong",
              )}
            >
              {asset.mimeType.startsWith("image/") ? (
                <img
                  src={asset.url || "/placeholder.svg"}
                  alt={asset.fileName}
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-muted/50">
                  <FileText className="h-6 w-6 text-muted-foreground" />
                </div>
              )}
            </button>
          ))}
        </div>
      )}

      {/* Image Lightbox */}
      <Dialog open={isLightboxOpen} onOpenChange={setIsLightboxOpen}>
        <DialogContent className="max-w-7xl max-h-[95vh] p-0 bg-black/95">
          <div className="relative w-full h-[90vh] flex items-center justify-center">
            <img
              src={selectedAsset?.url || "/placeholder.svg"}
              alt={selectedAsset?.fileName}
              className="max-w-full max-h-full object-contain"
            />

            {/* Close Button */}
            <ModernButton
              variant="ghost"
              size="icon"
              className="absolute top-4 right-4 text-white hover:bg-white/20"
              onClick={() => setIsLightboxOpen(false)}
            >
              <X className="h-6 w-6" />
            </ModernButton>

            {/* Navigation */}
            {allAssets.length > 1 && (
              <>
                <ModernButton
                  variant="ghost"
                  size="icon"
                  className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                  onClick={prevImage}
                >
                  <ChevronLeft className="h-6 w-6" />
                </ModernButton>
                <ModernButton
                  variant="ghost"
                  size="icon"
                  className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                  onClick={nextImage}
                >
                  <ChevronRight className="h-6 w-6" />
                </ModernButton>
              </>
            )}

            {/* Info Bar */}
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex items-center gap-4 bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2 text-white">
              <span className="text-sm">{selectedAsset?.fileName}</span>
              <Badge variant="outline" className="border-white/30 text-white">
                {selectedIndex + 1} / {allAssets.length}
              </Badge>
              {selectedAsset?.fileSize && (
                <span className="text-sm opacity-70">{formatFileSize(selectedAsset.fileSize)}</span>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* PDF Viewer */}
      <Dialog open={isPDFViewerOpen} onOpenChange={setIsPDFViewerOpen}>
        <DialogContent className="max-w-6xl max-h-[95vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{selectedAsset?.fileName}</span>
              <div className="flex gap-2">
                <ModernButton variant="outline" size="sm" onClick={() => window.open(selectedAsset?.url, "_blank")}>
                  <Download className="h-4 w-4 mr-2" />
                  Скачать
                </ModernButton>
                <ModernButton variant="outline" size="sm" onClick={() => window.print()}>
                  <FileText className="h-4 w-4 mr-2" />
                  Печать
                </ModernButton>
              </div>
            </DialogTitle>
          </DialogHeader>

          <div className="w-full h-[70vh] border rounded-lg overflow-hidden">
            <iframe src={selectedAsset?.url} className="w-full h-full" title={selectedAsset?.fileName} />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
