"use client"

import { useState, useMemo } from "react"
import {
  BarChart3,
  Grid3X3,
  List,
  Eye,
  FileText,
  ContrastIcon as Compare,
  Package,
  TrendingUp,
  Clock,
  Building,
  ImageIcon,
} from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { ModernCard, ModernCardContent, ModernCardHeader, ModernCardTitle } from "@/components/ui/modern-card"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { StatusBadge } from "@/components/ui/status-badge"
import { ProfessionalHeader } from "../components/professional-header"
import { ModernFiltersPanel } from "../components/modern-filters-panel"
import { AIAssistantDrawer } from "../components/ai-assistant-drawer"
import { AISearchSuggestions } from "../components/ai-search-suggestions"
import { MediaGallery } from "../components/media-gallery"
import { MediaThumbnail } from "../components/media-thumbnail"
import { useCatalogSearch } from "../hooks/useCatalogSearch"
import type { PartApplicability, AttributeTemplate, SearchFilters } from "../types/catalog"

// Моковые данные для фильтров
const mockCategories = [
  { id: 1, name: "Сальники двигателя", slug: "engine-seals", level: 1, path: "01/01" },
  { id: 2, name: "Сальники трансмиссии", slug: "transmission-seals", level: 1, path: "01/02" },
  { id: 3, name: "Уплотнения гидравлики", slug: "hydraulic-seals", level: 1, path: "01/03" },
]

const mockBrands = [
  { id: 1, name: "SKF", slug: "skf", isOem: false },
  { id: 2, name: "Freudenberg", slug: "freudenberg", isOem: false },
  { id: 3, name: "Caterpillar", slug: "caterpillar", isOem: true },
  { id: 4, name: "Komatsu", slug: "komatsu", isOem: true },
]

const mockAttributeTemplates: AttributeTemplate[] = [
  {
    id: 1,
    name: "inner_diameter",
    title: "Внутренний диаметр",
    dataType: "NUMBER",
    unit: "MM",
    isRequired: true,
    allowedValues: [],
    tolerance: 0.1,
  },
  {
    id: 2,
    name: "outer_diameter",
    title: "Наружный диаметр",
    dataType: "NUMBER",
    unit: "MM",
    isRequired: true,
    allowedValues: [],
    tolerance: 0.1,
  },
  {
    id: 3,
    name: "material",
    title: "Материал",
    dataType: "STRING",
    isRequired: true,
    allowedValues: ["NBR", "FKM", "PTFE", "EPDM"],
  },
  {
    id: 4,
    name: "temperature_min",
    title: "Мин. температура",
    dataType: "NUMBER",
    unit: "C",
    isRequired: false,
    allowedValues: [],
  },
  {
    id: 5,
    name: "pressure_max",
    title: "Макс. давление",
    dataType: "NUMBER",
    unit: "BAR",
    isRequired: false,
    allowedValues: [],
  },
]

export default function ModernCatalogSearch() {
  const { results, filters, setFilters, totalCount, filteredCount } = useCatalogSearch()
  const [viewMode, setViewMode] = useState<"detailed" | "grid" | "table">("detailed")
  const [selectedItems, setSelectedItems] = useState<number[]>([])
  const [selectedApplicability, setSelectedApplicability] = useState<PartApplicability | null>(null)
  const [isAIAssistantOpen, setIsAIAssistantOpen] = useState(false)

  // Вычисляем доступные значения для фильтров
  const availableAttributeValues = useMemo(() => {
    const values: Record<number, string[]> = {}
    const numericStats: Record<number, { min: number; max: number; avg: number }> = {}

    results.forEach((applicability) => {
      applicability.catalogItem.attributes.forEach((attr) => {
        if (attr.template.dataType === "NUMBER" && attr.numericValue !== undefined) {
          if (!numericStats[attr.templateId]) {
            numericStats[attr.templateId] = { min: attr.numericValue, max: attr.numericValue, avg: 0 }
          } else {
            numericStats[attr.templateId].min = Math.min(numericStats[attr.templateId].min, attr.numericValue)
            numericStats[attr.templateId].max = Math.max(numericStats[attr.templateId].max, attr.numericValue)
          }
        } else {
          if (!values[attr.templateId]) {
            values[attr.templateId] = []
          }
          if (!values[attr.templateId].includes(attr.value)) {
            values[attr.templateId].push(attr.value)
          }
        }
      })
    })

    // Вычисляем средние значения
    Object.keys(numericStats).forEach((templateId) => {
      const templateIdNum = Number.parseInt(templateId)
      const allValues = results
        .flatMap((r) => r.catalogItem.attributes)
        .filter((attr) => attr.templateId === templateIdNum && attr.numericValue !== undefined)
        .map((attr) => attr.numericValue!)

      if (allValues.length > 0) {
        numericStats[templateIdNum].avg = allValues.reduce((sum, val) => sum + val, 0) / allValues.length
      }
    })

    return { values, numericStats }
  }, [results])

  const clearAllFilters = () => {
    setFilters({
      query: "",
      categoryIds: [],
      brandIds: [],
      attributeFilters: {},
      accuracyLevels: [],
      isOemOnly: false,
    })
  }

  const activeFiltersCount =
    filters.categoryIds.length +
    filters.brandIds.length +
    Object.keys(filters.attributeFilters).length +
    filters.accuracyLevels.length +
    (filters.isOemOnly ? 1 : 0)

  const handleAIApplyFilters = (aiFilters: Partial<SearchFilters>) => {
    setFilters({
      ...filters,
      ...aiFilters,
    })
  }

  const handleAIUpdateQuery = (query: string) => {
    setFilters({ ...filters, query })
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Professional Header */}
      <ProfessionalHeader
        totalCount={totalCount}
        filteredCount={filteredCount}
        searchQuery={filters.query}
        onSearchChange={(query) => setFilters({ ...filters, query })}
        onOpenAI={() => setIsAIAssistantOpen(true)}
      />

      <div className="flex">
        {/* Modern Filters Panel */}
        <ModernFiltersPanel
          filters={filters}
          setFilters={setFilters}
          activeFiltersCount={activeFiltersCount}
          onClearAll={clearAllFilters}
          mockCategories={mockCategories}
          mockBrands={mockBrands}
          mockAttributeTemplates={mockAttributeTemplates}
          availableAttributeValues={availableAttributeValues}
        />

        {/* Main Content Area */}
        <div className="flex-1 overflow-y-auto">
          <div className="container max-w-none p-4">
            {/* Compact Toolbar */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <h2 className="text-xl font-bold tracking-tight">Результаты поиска</h2>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="px-2 py-1 font-medium text-xs">
                    <Package className="h-3 w-3 mr-1" />
                    {filteredCount} позиций
                  </Badge>
                  {filteredCount !== totalCount && (
                    <Badge variant="outline" className="px-2 py-1 text-xs">
                      из {totalCount} общих
                    </Badge>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* View Mode Toggle */}
                <div className="flex items-center gap-1 p-1 bg-muted/50 rounded border border-border/40">
                  <ModernButton
                    variant={viewMode === "detailed" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("detailed")}
                    className="h-7 w-7 p-0"
                  >
                    <List className="h-3 w-3" />
                  </ModernButton>
                  <ModernButton
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="h-7 w-7 p-0"
                  >
                    <Grid3X3 className="h-3 w-3" />
                  </ModernButton>
                  <ModernButton
                    variant={viewMode === "table" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("table")}
                    className="h-7 w-7 p-0"
                  >
                    <BarChart3 className="h-3 w-3" />
                  </ModernButton>
                </div>

                <ModernButton variant="outline" size="sm" className="gap-1 h-7 px-2 text-xs">
                  <Compare className="h-3 w-3" />
                  Сравнить
                  {selectedItems.length > 0 && (
                    <Badge variant="secondary" className="ml-1 h-4 w-4 rounded-full p-0 text-xs">
                      {selectedItems.length}
                    </Badge>
                  )}
                </ModernButton>
              </div>
            </div>

            {/* AI Search Suggestions */}
            <AISearchSuggestions
              query={filters.query}
              resultsCount={filteredCount}
              onApplyFilters={handleAIApplyFilters}
              onUpdateQuery={handleAIUpdateQuery}
            />

            {/* Results */}
            {results.length === 0 ? (
              <ModernCard variant="elevated" className="text-center py-12 border-2 border-dashed border-border-strong">
                <ModernCardContent>
                  <div className="flex flex-col items-center gap-3">
                    <div className="h-12 w-12 rounded-full bg-muted/50 flex items-center justify-center">
                      <Package className="h-6 w-6 text-muted-foreground" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-1">Ничего не найдено</h3>
                      <p className="text-muted-foreground text-sm max-w-md">
                        Попробуйте изменить критерии поиска или воспользуйтесь AI ассистентом для помощи
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <ModernButton variant="outline" onClick={clearAllFilters} size="sm">
                        Очистить фильтры
                      </ModernButton>
                      <ModernButton variant="gradient" onClick={() => setIsAIAssistantOpen(true)} size="sm">
                        Спросить AI
                      </ModernButton>
                    </div>
                  </div>
                </ModernCardContent>
              </ModernCard>
            ) : (
              <div className="space-y-3 animate-fade-in">
                {results.map((applicability, index) => (
                  <ModernCard
                    key={applicability.id}
                    variant="elevated"
                    className="group hover:shadow-strong transition-all duration-200 animate-slide-up border hover:border-primary/20"
                    style={{ animationDelay: `${index * 30}ms` }}
                  >
                    <ModernCardContent className="p-4">
                      <div className="flex gap-4">
                        {/* Compact Media Thumbnail */}
                        <div className="flex-shrink-0">
                          <MediaThumbnail
                            mediaAsset={applicability.catalogItem.image}
                            size="md"
                            className="cursor-pointer hover:scale-105 transition-transform"
                          />
                        </div>

                        {/* Main Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-3 mb-2">
                                <Checkbox
                                  checked={selectedItems.includes(applicability.id)}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      setSelectedItems([...selectedItems, applicability.id])
                                    } else {
                                      setSelectedItems(selectedItems.filter((id) => id !== applicability.id))
                                    }
                                  }}
                                  className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-4 w-4"
                                />
                                <div className="min-w-0 flex-1">
                                  <h3 className="text-lg font-bold text-primary font-mono tracking-tight truncate">
                                    {applicability.catalogItem.sku}
                                  </h3>
                                  <div className="flex items-center gap-2 mt-1">
                                    <Badge variant="outline" className="font-medium text-xs h-5">
                                      <Building className="h-3 w-3 mr-1" />
                                      {applicability.catalogItem.brand.name}
                                    </Badge>
                                    {applicability.catalogItem.brand.isOem && (
                                      <Badge variant="secondary" className="text-xs h-5">
                                        OEM
                                      </Badge>
                                    )}
                                    {applicability.catalogItem.mediaAssets.length > 0 && (
                                      <Badge variant="outline" className="text-xs h-5">
                                        <ImageIcon className="h-3 w-3 mr-1" />
                                        {applicability.catalogItem.mediaAssets.length}
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </div>

                              <div className="space-y-1 mb-3">
                                <h4 className="font-semibold text-sm">{applicability.part.name}</h4>
                                <p className="text-muted-foreground text-sm leading-relaxed line-clamp-2">
                                  {applicability.catalogItem.description}
                                </p>
                              </div>

                              {applicability.notes && (
                                <div className="p-2 rounded bg-info/10 border border-info/20 mb-3">
                                  <div className="flex items-start gap-2">
                                    <div className="h-4 w-4 rounded-full bg-info/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                                      <div className="h-1.5 w-1.5 rounded-full bg-info" />
                                    </div>
                                    <p className="text-xs text-info-foreground leading-relaxed">
                                      {applicability.notes}
                                    </p>
                                  </div>
                                </div>
                              )}
                            </div>

                            <div className="flex flex-col items-end gap-2 ml-4">
                              <StatusBadge status={applicability.accuracy} size="sm" />
                              <Badge variant="outline" className="text-xs">
                                {applicability.part.partCategory.name}
                              </Badge>
                            </div>
                          </div>

                          {/* Ultra Compact Attributes - Horizontal Pills */}
                          <div className="flex flex-wrap gap-1 mb-3">
                            {applicability.catalogItem.attributes.slice(0, 6).map((attr) => (
                              <div
                                key={attr.id}
                                className="inline-flex items-center gap-1 px-2 py-1 rounded-full bg-card border border-border-strong hover:border-primary/40 transition-colors text-xs"
                              >
                                <span
                                  className="text-muted-foreground font-medium truncate max-w-[80px]"
                                  title={attr.template.title}
                                >
                                  {attr.template.title.split(" ")[0]}:
                                </span>
                                <span className="font-mono font-semibold">{attr.value}</span>
                                {attr.template.unit && (
                                  <span className="text-muted-foreground">{attr.template.unit}</span>
                                )}
                              </div>
                            ))}
                            {applicability.catalogItem.attributes.length > 6 && (
                              <div className="inline-flex items-center px-2 py-1 rounded-full bg-muted/50 border border-border text-xs text-muted-foreground">
                                +{applicability.catalogItem.attributes.length - 6} еще
                              </div>
                            )}
                          </div>

                          {/* Compact Footer */}
                          <div className="flex items-center justify-between pt-2 border-t border-border">
                            <div className="flex items-center gap-2">
                              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <Clock className="h-3 w-3" />
                                <span>{new Date(applicability.part.updatedAt).toLocaleDateString("ru-RU")}</span>
                              </div>
                              <Separator orientation="vertical" className="h-3" />
                              <div className="flex items-center gap-1">
                                <Badge variant="outline" className="text-xs font-mono h-4 px-1">
                                  #{applicability.partId}
                                </Badge>
                                <Badge variant="outline" className="text-xs font-mono h-4 px-1">
                                  #{applicability.catalogItemId}
                                </Badge>
                              </div>
                            </div>

                            <div className="flex items-center gap-1">
                              <Dialog>
                                <DialogTrigger asChild>
                                  <ModernButton
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setSelectedApplicability(applicability)}
                                    className="gap-1 h-7 px-2 text-xs"
                                  >
                                    <Eye className="h-3 w-3" />
                                    Подробности
                                  </ModernButton>
                                </DialogTrigger>
                              </Dialog>

                              <ModernButton variant="outline" size="sm" className="gap-1 h-7 px-2 text-xs">
                                <FileText className="h-3 w-3" />
                                Техданные
                              </ModernButton>

                              <ModernButton variant="gradient" size="sm" className="gap-1 h-7 px-2 text-xs">
                                <TrendingUp className="h-3 w-3" />В корзину
                              </ModernButton>
                            </div>
                          </div>
                        </div>
                      </div>
                    </ModernCardContent>
                  </ModernCard>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* AI Assistant Drawer */}
      <AIAssistantDrawer
        isOpen={isAIAssistantOpen}
        onToggle={() => setIsAIAssistantOpen(!isAIAssistantOpen)}
        onApplyFilters={handleAIApplyFilters}
        currentFilters={filters}
        resultsCount={filteredCount}
      />

      {/* Detailed Modal with Media Gallery */}
      {selectedApplicability && (
        <Dialog open={!!selectedApplicability} onOpenChange={() => setSelectedApplicability(null)}>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-card border-border/40">
            <DialogHeader>
              <DialogTitle className="text-xl font-bold flex items-center justify-between">
                <span className="font-mono">{selectedApplicability.catalogItem.sku}</span>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{selectedApplicability.catalogItem.brand.name}</Badge>
                  <StatusBadge status={selectedApplicability.accuracy} />
                </div>
              </DialogTitle>
              <p className="text-muted-foreground leading-relaxed">{selectedApplicability.catalogItem.description}</p>
            </DialogHeader>

            <Tabs defaultValue="overview" className="mt-4">
              <TabsList className="grid w-full grid-cols-5 bg-muted/50">
                <TabsTrigger value="overview">Обзор</TabsTrigger>
                <TabsTrigger value="media">Медиа</TabsTrigger>
                <TabsTrigger value="part">Эталонная группа</TabsTrigger>
                <TabsTrigger value="attributes">Атрибуты</TabsTrigger>
                <TabsTrigger value="applicability">Применимость</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4 mt-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {/* Media Preview */}
                  <div>
                    <h3 className="font-semibold mb-3">Изображения и документы</h3>
                    <MediaGallery
                      mediaAssets={selectedApplicability.catalogItem.mediaAssets}
                      primaryImage={selectedApplicability.catalogItem.image}
                      title={selectedApplicability.catalogItem.sku}
                    />
                  </div>

                  {/* Info Cards */}
                  <div className="space-y-4">
                    <ModernCard variant="glass">
                      <ModernCardHeader>
                        <ModernCardTitle className="text-sm">Информация о товаре</ModernCardTitle>
                      </ModernCardHeader>
                      <ModernCardContent className="space-y-3">
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <span className="text-xs text-muted-foreground">Артикул</span>
                            <div className="font-mono font-semibold text-sm">
                              {selectedApplicability.catalogItem.sku}
                            </div>
                          </div>
                          <div>
                            <span className="text-xs text-muted-foreground">Бренд</span>
                            <div className="font-semibold text-sm">{selectedApplicability.catalogItem.brand.name}</div>
                          </div>
                          <div>
                            <span className="text-xs text-muted-foreground">Страна</span>
                            <div className="font-semibold text-sm">
                              {selectedApplicability.catalogItem.brand.country || "Не указана"}
                            </div>
                          </div>
                          <div>
                            <span className="text-xs text-muted-foreground">Тип</span>
                            <div className="font-semibold text-sm">
                              {selectedApplicability.catalogItem.brand.isOem ? "OEM" : "Aftermarket"}
                            </div>
                          </div>
                        </div>
                      </ModernCardContent>
                    </ModernCard>

                    <ModernCard variant="glass">
                      <ModernCardHeader>
                        <ModernCardTitle className="text-sm">Эталонная группа</ModernCardTitle>
                      </ModernCardHeader>
                      <ModernCardContent className="space-y-2">
                        <div>
                          <span className="text-xs text-muted-foreground">Название группы</span>
                          <div className="font-semibold text-sm">
                            {selectedApplicability.part.name || "Без названия"}
                          </div>
                        </div>
                        <div>
                          <span className="text-xs text-muted-foreground">Категория</span>
                          <div className="font-semibold text-sm">{selectedApplicability.part.partCategory.name}</div>
                        </div>
                        <div>
                          <span className="text-xs text-muted-foreground">Путь в иерархии</span>
                          <div className="font-mono text-xs">{selectedApplicability.part.path}</div>
                        </div>
                      </ModernCardContent>
                    </ModernCard>
                  </div>
                </div>

                {selectedApplicability.notes && (
                  <ModernCard variant="glass" className="border-info/20 bg-info/5">
                    <ModernCardHeader>
                      <ModernCardTitle className="text-info flex items-center gap-2 text-sm">
                        <div className="h-2 w-2 rounded-full bg-info" />
                        Примечания по применимости
                      </ModernCardTitle>
                    </ModernCardHeader>
                    <ModernCardContent>
                      <p className="leading-relaxed text-sm">{selectedApplicability.notes}</p>
                    </ModernCardContent>
                  </ModernCard>
                )}
              </TabsContent>

              <TabsContent value="media" className="space-y-4 mt-4">
                <MediaGallery
                  mediaAssets={selectedApplicability.catalogItem.mediaAssets}
                  primaryImage={selectedApplicability.catalogItem.image}
                  title={selectedApplicability.catalogItem.sku}
                />
              </TabsContent>

              <TabsContent value="part" className="space-y-4 mt-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold mb-3 text-sm">Медиа эталонной группы</h3>
                    <MediaGallery
                      mediaAssets={selectedApplicability.part.mediaAssets}
                      primaryImage={selectedApplicability.part.image}
                      title={selectedApplicability.part.name || "Эталонная группа"}
                    />
                  </div>

                  <ModernCard variant="glass">
                    <ModernCardHeader>
                      <ModernCardTitle className="text-sm">Эталонные атрибуты группы</ModernCardTitle>
                    </ModernCardHeader>
                    <ModernCardContent>
                      <div className="space-y-2">
                        {selectedApplicability.part.attributes.map((attr) => (
                          <div
                            key={attr.id}
                            className="flex items-center justify-between p-2 rounded bg-card border border-border-strong hover:border-primary/40 transition-colors"
                          >
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-sm">{attr.template.title}</span>
                              {attr.template.unit && (
                                <Badge variant="outline" className="text-xs h-4">
                                  {attr.template.unit}
                                </Badge>
                              )}
                            </div>
                            <div className="font-mono font-bold text-sm">{attr.value}</div>
                          </div>
                        ))}
                      </div>
                    </ModernCardContent>
                  </ModernCard>
                </div>
              </TabsContent>

              <TabsContent value="attributes" className="space-y-4 mt-4">
                <ModernCard variant="glass">
                  <ModernCardHeader>
                    <ModernCardTitle className="text-sm">Атрибуты каталожной позиции</ModernCardTitle>
                  </ModernCardHeader>
                  <ModernCardContent>
                    <div className="space-y-2">
                      {selectedApplicability.catalogItem.attributes.map((attr) => (
                        <div
                          key={attr.id}
                          className="flex items-center justify-between p-2 rounded bg-card border border-border-strong hover:border-primary/40 transition-colors"
                        >
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">{attr.template.title}</span>
                            {attr.template.unit && (
                              <Badge variant="outline" className="text-xs h-4">
                                {attr.template.unit}
                              </Badge>
                            )}
                          </div>
                          <div className="font-mono font-bold text-sm">{attr.value}</div>
                        </div>
                      ))}
                    </div>
                  </ModernCardContent>
                </ModernCard>
              </TabsContent>

              <TabsContent value="applicability" className="space-y-4 mt-4">
                <ModernCard variant="glass">
                  <ModernCardHeader>
                    <ModernCardTitle className="text-sm">Детали применимости</ModernCardTitle>
                  </ModernCardHeader>
                  <ModernCardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <span className="text-xs text-muted-foreground">Точность совпадения</span>
                        <div className="mt-1">
                          <StatusBadge status={selectedApplicability.accuracy} />
                        </div>
                      </div>
                      <div>
                        <span className="text-xs text-muted-foreground">ID применимости</span>
                        <div className="font-mono font-semibold text-sm">#{selectedApplicability.id}</div>
                      </div>
                    </div>

                    {selectedApplicability.notes && (
                      <div className="p-3 rounded bg-info/10 border border-info/20">
                        <h4 className="font-medium text-sm mb-2">Примечания:</h4>
                        <p className="text-sm leading-relaxed">{selectedApplicability.notes}</p>
                      </div>
                    )}

                    <div className="grid grid-cols-2 gap-4 pt-3 border-t border-border">
                      <div>
                        <span className="text-xs text-muted-foreground">Создано</span>
                        <div className="text-sm">
                          {new Date(selectedApplicability.part.createdAt).toLocaleString("ru-RU")}
                        </div>
                      </div>
                      <div>
                        <span className="text-xs text-muted-foreground">Обновлено</span>
                        <div className="text-sm">
                          {new Date(selectedApplicability.part.updatedAt).toLocaleString("ru-RU")}
                        </div>
                      </div>
                    </div>
                  </ModernCardContent>
                </ModernCard>
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
