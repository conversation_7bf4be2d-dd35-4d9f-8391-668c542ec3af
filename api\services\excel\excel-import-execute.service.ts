import ExcelJS from 'exceljs'
import { z } from 'zod'
import type { Context } from '../../trpc'
import type { ConflictMode, ImportCounters, ImportResult } from './types'

const metaSchema = z.object({
  modes: z.object({
    createMissingRefs: z.boolean().default(false),
    onConflict: z.enum(['upsert','update_only','skip','error']).default('upsert'),
  }).optional(),
})

type Row = ExcelJS.Row

type ProcessCtx = {
  db: Context['db']
  createMissingRefs: boolean
  onConflict: ConflictMode
  report: ExcelJS.Workbook
}

function makeCounters(): ImportCounters {
  return { rowsSeen: 0, rowsValid: 0, created: 0, updated: 0, deleted: 0, skipped: 0, errorsCount: 0, warningsCount: 0 }
}

function ensureReportColumns(ws: ExcelJS.Worksheet) {
  const header = (ws.getRow(1).values as any[]).map(v => String(v || '').trim())
  const needResult = !header.includes('__result')
  const needError = !header.includes('__error')
  if (needResult) ws.getRow(1).getCell(header.length + 1).value = '__result'
  if (needError) ws.getRow(1).getCell(header.length + (needResult ? 2 : 1)).value = '__error'
}

function setReport(ws: ExcelJS.Worksheet, rowNumber: number, result: string, error?: string) {
  const header = (ws.getRow(1).values as any[]).map(v => String(v || '').trim())
  let resCol = header.indexOf('__result') + 1
  let errCol = header.indexOf('__error') + 1
  if (resCol <= 0) { resCol = header.length + 1; ws.getRow(1).getCell(resCol).value = '__result' }
  if (errCol <= 0) { errCol = header.length + (resCol === header.length + 1 ? 2 : 1); ws.getRow(1).getCell(errCol).value = '__error' }
  ws.getRow(rowNumber).getCell(resCol).value = result
  if (error) ws.getRow(rowNumber).getCell(errCol).value = error
}

function getHeaderIndex(ws: ExcelJS.Worksheet) {
  const header = (ws.getRow(1).values as any[]).map(v => String(v || '').trim())
  const idx = new Map<string, number>()
  header.forEach((h, i) => { if (h) idx.set(h, i) })
  return idx
}

function cell(row: Row, colName: string, idx: Map<string, number>) {
  const i = idx.get(colName) || 0
  return String(row.getCell(i).value ?? '').trim()
}

async function findBrandId(db: ProcessCtx['db'], brandSlug?: string) {
  if (!brandSlug) return null
  const b = await db.brand.findUnique({ where: { slug: brandSlug }, select: { id: true } } as any)
  return b?.id ?? null
}

// Ensure helpers respect createMissingRefs
async function ensureBrandId(ctx: ProcessCtx, brandSlug?: string): Promise<number | null> {
  if (!brandSlug) return null
  let brandId = await findBrandId(ctx.db, brandSlug)
  if (!brandId && ctx.createMissingRefs) {
    const created = await ctx.db.brand.create({ data: { slug: brandSlug, name: brandSlug } } as any)
    brandId = created.id
  }
  return brandId ?? null
}

async function ensureCatalogItemId(ctx: ProcessCtx, sku?: string, brandSlug?: string): Promise<number | null> {
  if (!sku || !brandSlug) return null
  const brandId = await ensureBrandId(ctx, brandSlug)
  if (!brandId) return null
  const existing = await ctx.db.catalogItem.findUnique({ where: { sku_brandId: { sku, brandId } }, select: { id: true } } as any)
  if (existing) return existing.id
  if (!ctx.createMissingRefs) return null
  const created = await ctx.db.catalogItem.create({ data: { sku, brandId } } as any)
  return created.id
}

async function ensureTemplateId(ctx: ProcessCtx, templateName?: string, numericHint?: number | undefined): Promise<number | null> {
  if (!templateName) return null
  const t = await ctx.db.attributeTemplate.findUnique({ where: { name: templateName }, select: { id: true } } as any)
  if (t?.id) return t.id
  if (!ctx.createMissingRefs) return null
  const dataType = (typeof numericHint === 'number' && !Number.isNaN(numericHint)) ? 'NUMBER' : 'STRING'
  const created = await ctx.db.attributeTemplate.create({ data: { name: templateName, title: templateName, dataType } } as any)
  return created.id
}

async function findCategoryId(db: ProcessCtx['db'], slug?: string) {
  if (!slug) return null
  const c = await db.partCategory.findUnique({ where: { slug }, select: { id: true } } as any)
  return c?.id ?? null
}

async function findTemplateId(db: ProcessCtx['db'], name?: string) {
  if (!name) return null
  const t = await db.attributeTemplate.findUnique({ where: { name }, select: { id: true } } as any)
  return t?.id ?? null
}

async function findPartId(db: ProcessCtx['db'], partCategorySlug?: string, path?: string) {
  if (!partCategorySlug || !path) return null
  const categoryId = await findCategoryId(db, partCategorySlug)
  if (!categoryId) return null
  const p = await db.part.findFirst({ where: { path, partCategoryId: categoryId }, select: { id: true } } as any)
  return p?.id ?? null
}

async function ensurePartId(ctx: ProcessCtx, partCategorySlug?: string, path?: string): Promise<number | null> {
  if (!partCategorySlug || !path) return null
  const categoryId = await findCategoryId(ctx.db, partCategorySlug)
  if (!categoryId) {
    if (!ctx.createMissingRefs) return null
    // Создадим категорию-обертку по slug с name=slug
    const createdCat = await ctx.db.partCategory.create({ data: { slug: partCategorySlug, name: partCategorySlug, level: 0, path: '00' } } as any)
    const partId = await ensurePartId(ctx, partCategorySlug, path)
    return partId
  }
  const existing = await ctx.db.part.findFirst({ where: { path, partCategoryId: categoryId }, select: { id: true } } as any)
  if (existing) return existing.id
  if (!ctx.createMissingRefs) return null
  const created = await ctx.db.part.create({ data: { path, partCategoryId: categoryId, level: 0 } } as any)
  return created.id
}

async function findCatalogItemId(db: ProcessCtx['db'], sku?: string, brandSlug?: string) {
  if (!sku || !brandSlug) return null
  const brandId = await findBrandId(db, brandSlug)
  if (!brandId) return null
  const ci = await db.catalogItem.findUnique({ where: { sku_brandId: { sku, brandId } }, select: { id: true } } as any)
  return ci?.id ?? null
}

export class ExcelImportExecuteService {
  constructor(private db: Context['db']) {}

  async execute(fileBuffer: Buffer, overrides?: { createMissingRefs?: boolean; onConflict?: ConflictMode }): Promise<ImportResult & { reportBase64: string }> {
    const wb = new ExcelJS.Workbook()
    await wb.xlsx.load(fileBuffer)

    const meta = wb.getWorksheet('META')
    if (!meta) throw new Error('Лист META отсутствует')
    const metaParsed = await this.parseMeta(meta)
    if (!metaParsed.success) throw new Error('Ошибка чтения META: ' + metaParsed.error.message)

    const createMissingRefs = overrides?.createMissingRefs ?? (metaParsed.data.modes?.createMissingRefs ?? false)
    const onConflict = overrides?.onConflict ?? (metaParsed.data.modes?.onConflict ?? 'upsert')

    const reportWb = wb // модифицируем исходный для отчета

    const perSheet: Record<string, ImportCounters> = {}
    const errors: ImportResult['errors'] = []
    const warnings: ImportResult['warnings'] = []

    // Обрабатываем поддерживаемые листы в правильном порядке (сначала справочники, потом связи)
    await this.processBrands({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)
    await this.processPartCategories({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)
    await this.processAttributeGroups({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)
    await this.processAttributeTemplates({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)
    await this.processAttributeSynonymGroups({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)
    await this.processAttributeSynonyms({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)
    await this.processEquipmentModels({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)
    await this.processParts({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)
    await this.processCatalogItems({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)

    await this.processPartAttributes({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)
    await this.processCatalogItemAttributes({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)
    await this.processEquipmentModelAttributes({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)

    await this.processPartApplicabilities({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)
    await this.processEquipmentApplicabilities({ db: this.db, createMissingRefs, onConflict, report: reportWb }, wb, perSheet, errors)

    // Генерируем отчет
    const reportBuffer = await reportWb.xlsx.writeBuffer()

    return {
      perSheet,
      errors,
      warnings,
      reportBase64: Buffer.from(reportBuffer).toString('base64'),
    }
  }

  private async parseMeta(ws: ExcelJS.Worksheet) {
    const map: Record<string, unknown> = {}
    ws.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber === 1) return
      const key = String(row.getCell(1).value ?? '')
      const val = String(row.getCell(2).value ?? '')
      if (!key) return
      try { map[key] = JSON.parse(val) } catch { map[key] = val }
    })
    return metaSchema.safeParse(map)
  }

  private async processBrands(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('Brand')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['Brand'] ?? (perSheet['Brand'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const slug = cell(row, 'slug', idx)
      const name = cell(row, 'name', idx)
      const country = cell(row, 'country', idx) || undefined
      const isOemStr = cell(row, 'isOem', idx)
      const isOem = isOemStr ? isOemStr.toLowerCase() === 'true' : undefined

      if (!slug) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Пустой slug'); errors.push({ sheet: 'Brand', rowIndex: rowNumber, message: 'Пустой slug' }); return }

      const existing = await ctx.db.brand.findUnique({ where: { slug }, select: { id: true } } as any)
      if (existing) {
        await ctx.db.brand.update({ where: { slug }, data: { name: name || undefined, country, isOem } } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'Brand', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        await ctx.db.brand.create({ data: { slug, name: name || slug, country, isOem: !!isOem } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED')
      }
    })
  }

  private async processPartCategories(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('PartCategory')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['PartCategory'] ?? (perSheet['PartCategory'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const slug = cell(row, 'slug', idx)
      const name = cell(row, 'name', idx)
      const description = cell(row, 'description', idx) || undefined
      const parentSlug = cell(row, 'parent.slug', idx) || undefined

      if (!slug) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Пустой slug'); errors.push({ sheet: 'PartCategory', rowIndex: rowNumber, message: 'Пустой slug' }); return }

      const parentId = parentSlug ? await findCategoryId(ctx.db, parentSlug) : null
      if (parentSlug && !parentId) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', `Родитель ${parentSlug} не найден`); errors.push({ sheet: 'PartCategory', rowIndex: rowNumber, message: `Родитель ${parentSlug} не найден` }); return }

      const existing = await ctx.db.partCategory.findUnique({ where: { slug }, select: { id: true } } as any)
      if (existing) {
        await ctx.db.partCategory.update({ where: { slug }, data: { name: name || undefined, description, parentId: parentId ?? undefined } } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'PartCategory', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        await ctx.db.partCategory.create({ data: { slug, name: name || slug, description, parentId: parentId ?? undefined, level: 0, path: '00' } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED')
      }
    })
  }

  private async processAttributeTemplates(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('AttributeTemplate')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['AttributeTemplate'] ?? (perSheet['AttributeTemplate'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const name = cell(row, 'name', idx)
      if (!name) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Пустое name'); errors.push({ sheet: 'AttributeTemplate', rowIndex: rowNumber, message: 'Пустое name' }); return }
      const data = {
        title: cell(row, 'title', idx) || undefined,
        dataType: (cell(row, 'dataType', idx) || undefined) as any,
        unit: (cell(row, 'unit', idx) || undefined) as any,
        isRequired: (cell(row, 'isRequired', idx)?.toLowerCase?.() === 'true') || undefined,
        minValue: Number(cell(row, 'minValue', idx)) || undefined,
        maxValue: Number(cell(row, 'maxValue', idx)) || undefined,
        tolerance: Number(cell(row, 'tolerance', idx)) || undefined,
      }
      const existing = await ctx.db.attributeTemplate.findUnique({ where: { name }, select: { id: true } } as any)
      if (existing) {
        await ctx.db.attributeTemplate.update({ where: { name }, data } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'AttributeTemplate', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        await ctx.db.attributeTemplate.create({ data: { name, ...data } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED')
      }
    })
  }

  private async processEquipmentModels(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('EquipmentModel')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['EquipmentModel'] ?? (perSheet['EquipmentModel'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const id = cell(row, 'id', idx) || undefined
      const name = cell(row, 'name', idx)
      const brandSlug = cell(row, 'brand.slug', idx) || undefined
      if (!name && !id) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нужен id или name'); errors.push({ sheet: 'EquipmentModel', rowIndex: rowNumber, message: 'Нужен id или name' }); return }

      const brandId = brandSlug ? await findBrandId(ctx.db, brandSlug) : null
      if (brandSlug && !brandId) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', `Бренд ${brandSlug} не найден`); errors.push({ sheet: 'EquipmentModel', rowIndex: rowNumber, message: `Бренд ${brandSlug} не найден` }); return }

      let existing: { id: string } | null = null
      if (id) existing = await ctx.db.equipmentModel.findUnique({ where: { id }, select: { id: true } } as any)
      if (!existing && name) {
        const list = await ctx.db.equipmentModel.findMany({ where: { name, brandId: brandId ?? undefined }, select: { id: true } } as any)
        if (list.length > 1) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Найдено несколько моделей по (name,brand)'); errors.push({ sheet: 'EquipmentModel', rowIndex: rowNumber, message: 'Найдено несколько моделей по (name,brand)' }); return }
        existing = list[0] || null
      }

      if (existing) {
        await ctx.db.equipmentModel.update({ where: { id: existing.id }, data: { name: name || undefined, brandId: brandId ?? undefined } } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'EquipmentModel', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        const created = await ctx.db.equipmentModel.create({ data: { name: name || 'Unnamed', brandId: brandId ?? undefined } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED', created.id)
      }
    })
  }

  private async processParts(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('Part')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['Part'] ?? (perSheet['Part'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const categorySlug = cell(row, 'partCategory.slug', idx)
      const path = cell(row, 'path', idx)
      const name = cell(row, 'name', idx) || undefined
      const parentPath = cell(row, 'parent.path', idx) || undefined

      if (!categorySlug || !path) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нужны partCategory.slug и path'); errors.push({ sheet: 'Part', rowIndex: rowNumber, message: 'Нужны partCategory.slug и path' }); return }

      const partCategoryId = await findCategoryId(ctx.db, categorySlug)
      if (!partCategoryId) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', `Категория ${categorySlug} не найдена`); errors.push({ sheet: 'Part', rowIndex: rowNumber, message: `Категория ${categorySlug} не найдена` }); return }

      let parentId: number | null = null
      if (parentPath) {
        const p = await ctx.db.part.findFirst({ where: { path: parentPath, partCategoryId }, select: { id: true } } as any)
        if (!p) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', `Родитель path=${parentPath} не найден`); errors.push({ sheet: 'Part', rowIndex: rowNumber, message: `Родитель path=${parentPath} не найден` }); return }
        parentId = p.id
      }

      const existing = await ctx.db.part.findFirst({ where: { path, partCategoryId }, select: { id: true } } as any)
      if (existing) {
        await ctx.db.part.update({ where: { id: existing.id }, data: { name: name ?? undefined, parentId: parentId ?? undefined } } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'Part', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        await ctx.db.part.create({ data: { name: name ?? null, path, level: 0, parentId: parentId ?? undefined, partCategoryId } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED')
      }
    })
  }

  private async processCatalogItems(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('CatalogItem')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['CatalogItem'] ?? (perSheet['CatalogItem'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const sku = cell(row, 'sku', idx)
      const brandSlug = cell(row, 'brand.slug', idx)
      if (!sku || !brandSlug) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нужны sku и brand.slug'); errors.push({ sheet: 'CatalogItem', rowIndex: rowNumber, message: 'Нужны sku и brand.slug' }); return }
      const brandId = await ensureBrandId(ctx, brandSlug)
      if (!brandId) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', `Бренд ${brandSlug} не найден`); errors.push({ sheet: 'CatalogItem', rowIndex: rowNumber, message: `Бренд ${brandSlug} не найден` }); return }

      const existing = await ctx.db.catalogItem.findUnique({ where: { sku_brandId: { sku, brandId } }, select: { id: true } } as any)
      const data = {
        description: cell(row, 'description', idx) || undefined,
        source: cell(row, 'source', idx) || undefined,
        isPublic: (cell(row, 'isPublic', idx)?.toLowerCase?.() === 'true') || undefined,
        brandId,
      }

      if (existing) {
        await ctx.db.catalogItem.update({ where: { id: existing.id }, data } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'CatalogItem', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        await ctx.db.catalogItem.create({ data: { sku, ...data } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED')
      }
    })
  }

  private async processPartAttributes(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('PartAttribute')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['PartAttribute'] ?? (perSheet['PartAttribute'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const partCategorySlug = cell(row, 'partCategory.slug', idx)
      const partPath = cell(row, 'part.path', idx)
      const templateName = cell(row, 'template.name', idx)
      const value = cell(row, 'value', idx)
      const numericValueStr = cell(row, 'numericValue', idx)
      const numericValue = numericValueStr ? Number(numericValueStr) : undefined

      if (!partCategorySlug || !partPath || !templateName) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нужны partCategory.slug, part.path, template.name'); errors.push({ sheet: 'PartAttribute', rowIndex: rowNumber, message: 'Нужны partCategory.slug, part.path, template.name' }); return }

      const partId = await ensurePartId(ctx, partCategorySlug, partPath)
      if (!partId) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Part не найден'); errors.push({ sheet: 'PartAttribute', rowIndex: rowNumber, message: 'Part не найден' }); return }
      const templateId = await ensureTemplateId(ctx, templateName, numericValue)
      if (!templateId) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Template не найден'); errors.push({ sheet: 'PartAttribute', rowIndex: rowNumber, message: 'Template не найден' }); return }

      const existing = await ctx.db.partAttribute.findUnique({ where: { partId_templateId: { partId, templateId } }, select: { id: true } } as any)
      const data: any = { value }
      if (!Number.isNaN(numericValue!)) data.numericValue = numericValue

      if (existing) {
        await ctx.db.partAttribute.update({ where: { id: existing.id }, data } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'PartAttribute', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        await ctx.db.partAttribute.create({ data: { partId, templateId, ...data } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED')
      }
    })
  }

  private async processCatalogItemAttributes(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('CatalogItemAttribute')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['CatalogItemAttribute'] ?? (perSheet['CatalogItemAttribute'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const sku = cell(row, 'catalogItem.sku', idx)
      const brandSlug = cell(row, 'brand.slug', idx)
      const templateName = cell(row, 'template.name', idx)
      const value = cell(row, 'value', idx)
      const numericValueStr = cell(row, 'numericValue', idx)
      const numericValue = numericValueStr ? Number(numericValueStr) : undefined

      if (!sku || !brandSlug || !templateName) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нужны catalogItem.sku, brand.slug, template.name'); errors.push({ sheet: 'CatalogItemAttribute', rowIndex: rowNumber, message: 'Нужны catalogItem.sku, brand.slug, template.name' }); return }

      const catalogItemId = await ensureCatalogItemId(ctx, sku, brandSlug)
      if (!catalogItemId) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'CatalogItem не найден'); errors.push({ sheet: 'CatalogItemAttribute', rowIndex: rowNumber, message: 'CatalogItem не найден' }); return }
      const templateId = await ensureTemplateId(ctx, templateName, numericValue)
      if (!templateId) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Template не найден'); errors.push({ sheet: 'CatalogItemAttribute', rowIndex: rowNumber, message: 'Template не найден' }); return }

      const existing = await ctx.db.catalogItemAttribute.findUnique({ where: { catalogItemId_templateId: { catalogItemId, templateId } }, select: { id: true } } as any)
      const data: any = { value }
      if (!Number.isNaN(numericValue!)) data.numericValue = numericValue

      if (existing) {
        await ctx.db.catalogItemAttribute.update({ where: { id: existing.id }, data } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'CatalogItemAttribute', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        await ctx.db.catalogItemAttribute.create({ data: { catalogItemId, templateId, ...data } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED')
      }
    })
  }

  private async processEquipmentModelAttributes(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('EquipmentModelAttribute')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['EquipmentModelAttribute'] ?? (perSheet['EquipmentModelAttribute'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const equipmentModelId = cell(row, 'equipmentModel.id', idx)
      const templateName = cell(row, 'template.name', idx)
      const value = cell(row, 'value', idx)
      const numericValueStr = cell(row, 'numericValue', idx)
      const numericValue = numericValueStr ? Number(numericValueStr) : undefined

      if (!equipmentModelId || !templateName) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нужны equipmentModel.id, template.name'); errors.push({ sheet: 'EquipmentModelAttribute', rowIndex: rowNumber, message: 'Нужны equipmentModel.id, template.name' }); return }

      const templateId = await ensureTemplateId(ctx, templateName, numericValue)
      if (!templateId) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Template не найден'); errors.push({ sheet: 'EquipmentModelAttribute', rowIndex: rowNumber, message: 'Template не найден' }); return }

      const existing = await ctx.db.equipmentModelAttribute.findUnique({ where: { equipmentModelId_templateId: { equipmentModelId, templateId } }, select: { id: true } } as any)
      const data: any = { value }
      if (!Number.isNaN(numericValue!)) data.numericValue = numericValue

      if (existing) {
        await ctx.db.equipmentModelAttribute.update({ where: { id: existing.id }, data } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'EquipmentModelAttribute', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        await ctx.db.equipmentModelAttribute.create({ data: { equipmentModelId, templateId, ...data } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED')
      }
    })
  }

  private async processPartApplicabilities(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('PartApplicability')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['PartApplicability'] ?? (perSheet['PartApplicability'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const partCategorySlug = cell(row, 'partCategory.slug', idx)
      const partPath = cell(row, 'part.path', idx)
      const sku = cell(row, 'catalogItem.sku', idx)
      const brandSlug = cell(row, 'brand.slug', idx)
      const accuracy = cell(row, 'accuracy', idx) || undefined
      const notes = cell(row, 'notes', idx) || undefined

      if (!partCategorySlug || !partPath || !sku || !brandSlug) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нужны partCategory.slug, part.path, sku, brand.slug'); errors.push({ sheet: 'PartApplicability', rowIndex: rowNumber, message: 'Нужны partCategory.slug, part.path, sku, brand.slug' }); return }

      const partId = await ensurePartId(ctx, partCategorySlug, partPath)
      if (!partId) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Part не найден'); errors.push({ sheet: 'PartApplicability', rowIndex: rowNumber, message: 'Part не найден' }); return }
      const catalogItemId = await findCatalogItemId(ctx.db, sku, brandSlug)
      if (!catalogItemId) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'CatalogItem не найден'); errors.push({ sheet: 'PartApplicability', rowIndex: rowNumber, message: 'CatalogItem не найден' }); return }

      const existing = await ctx.db.partApplicability.findUnique({ where: { partId_catalogItemId: { partId, catalogItemId } }, select: { id: true } } as any)
      const data: any = { accuracy: accuracy as any, notes }

      if (existing) {
        await ctx.db.partApplicability.update({ where: { id: existing.id }, data } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'PartApplicability', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        await ctx.db.partApplicability.create({ data: { partId, catalogItemId, ...data } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED')
      }
    })
  }

  private async processEquipmentApplicabilities(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('EquipmentApplicability')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['EquipmentApplicability'] ?? (perSheet['EquipmentApplicability'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const partCategorySlug = cell(row, 'partCategory.slug', idx)
      const partPath = cell(row, 'part.path', idx)
      const equipmentModelId = cell(row, 'equipmentModel.id', idx)
      const notes = cell(row, 'notes', idx) || undefined

      if (!partCategorySlug || !partPath || !equipmentModelId) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нужны partCategory.slug, part.path, equipmentModel.id'); errors.push({ sheet: 'EquipmentApplicability', rowIndex: rowNumber, message: 'Нужны partCategory.slug, part.path, equipmentModel.id' }); return }

      const partId = await ensurePartId(ctx, partCategorySlug, partPath)
      if (!partId) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Part не найден'); errors.push({ sheet: 'EquipmentApplicability', rowIndex: rowNumber, message: 'Part не найден' }); return }

      const existing = await ctx.db.equipmentApplicability.findUnique({ where: { partId_equipmentModelId: { partId, equipmentModelId } }, select: { id: true } } as any)
      const data: any = { notes }

      if (existing) {
        await ctx.db.equipmentApplicability.update({ where: { id: existing.id }, data } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'EquipmentApplicability', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        await ctx.db.equipmentApplicability.create({ data: { partId, equipmentModelId, ...data } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED')
      }
    })
  }

  private async processAttributeGroups(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('AttributeGroup')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['AttributeGroup'] ?? (perSheet['AttributeGroup'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const name = cell(row, 'name', idx)
      const description = cell(row, 'description', idx) || undefined
      const parentName = cell(row, 'parent.name', idx) || undefined

      if (!name) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Пустое name'); errors.push({ sheet: 'AttributeGroup', rowIndex: rowNumber, message: 'Пустое name' }); return }

      let parentId: number | null = null
      if (parentName) {
        const parent = await ctx.db.attributeGroup.findUnique({ where: { name: parentName }, select: { id: true } } as any)
        if (!parent) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', `Родительская группа ${parentName} не найдена`); errors.push({ sheet: 'AttributeGroup', rowIndex: rowNumber, message: `Родительская группа ${parentName} не найдена` }); return }
        parentId = parent.id
      }

      const existing = await ctx.db.attributeGroup.findUnique({ where: { name }, select: { id: true } } as any)
      if (existing) {
        await ctx.db.attributeGroup.update({ where: { name }, data: { description, parentId } } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'AttributeGroup', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        await ctx.db.attributeGroup.create({ data: { name, description, parentId } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED')
      }
    })
  }

  private async processAttributeSynonymGroups(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('AttributeSynonymGroup')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['AttributeSynonymGroup'] ?? (perSheet['AttributeSynonymGroup'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const templateName = cell(row, 'template.name', idx)
      const name = cell(row, 'name', idx)
      const description = cell(row, 'description', idx) || undefined
      const parentName = cell(row, 'parent.name', idx) || undefined
      const canonicalValue = cell(row, 'canonicalValue', idx) || undefined
      const compatibilityLevel = cell(row, 'compatibilityLevel', idx) || 'EXACT'
      const notes = cell(row, 'notes', idx) || undefined

      if (!templateName || !name) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нужны template.name и name'); errors.push({ sheet: 'AttributeSynonymGroup', rowIndex: rowNumber, message: 'Нужны template.name и name' }); return }

      const template = await ctx.db.attributeTemplate.findUnique({ where: { name: templateName }, select: { id: true } } as any)
      if (!template) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', `Шаблон ${templateName} не найден`); errors.push({ sheet: 'AttributeSynonymGroup', rowIndex: rowNumber, message: `Шаблон ${templateName} не найден` }); return }

      let parentId: number | null = null
      if (parentName) {
        const parent = await ctx.db.attributeSynonymGroup.findFirst({ where: { templateId: template.id, name: parentName }, select: { id: true } } as any)
        if (!parent) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', `Родительская группа ${parentName} не найдена`); errors.push({ sheet: 'AttributeSynonymGroup', rowIndex: rowNumber, message: `Родительская группа ${parentName} не найдена` }); return }
        parentId = parent.id
      }

      const existing = await ctx.db.attributeSynonymGroup.findFirst({ where: { templateId: template.id, name }, select: { id: true } } as any)
      const data = { description, parentId, canonicalValue, compatibilityLevel: compatibilityLevel as any, notes }

      if (existing) {
        await ctx.db.attributeSynonymGroup.update({ where: { id: existing.id }, data } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'AttributeSynonymGroup', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        await ctx.db.attributeSynonymGroup.create({ data: { templateId: template.id, name, ...data } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED')
      }
    })
  }

  private async processAttributeSynonyms(ctx: ProcessCtx, wb: ExcelJS.Workbook, perSheet: Record<string, ImportCounters>, errors: ImportResult['errors']) {
    const ws = wb.getWorksheet('AttributeSynonym')
    if (!ws) return
    ensureReportColumns(ws)
    const idx = getHeaderIndex(ws)
    const counters = perSheet['AttributeSynonym'] ?? (perSheet['AttributeSynonym'] = makeCounters())

    ws.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
      if (rowNumber === 1) return
      counters.rowsSeen++
      const groupName = cell(row, 'group.name', idx)
      const templateName = cell(row, 'template.name', idx)
      const value = cell(row, 'value', idx)
      const notes = cell(row, 'notes', idx) || undefined
      const brandSlug = cell(row, 'brand.slug', idx) || undefined
      const compatibilityLevel = cell(row, 'compatibilityLevel', idx) || undefined

      if (!groupName || !value) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нужны group.name и value'); errors.push({ sheet: 'AttributeSynonym', rowIndex: rowNumber, message: 'Нужны group.name и value' }); return }

      // Найдем группу по имени и шаблону (если указан)
      let group: any
      if (templateName) {
        group = await ctx.db.attributeSynonymGroup.findFirst({
          where: { name: groupName, template: { name: templateName } },
          select: { id: true }
        } as any)
      } else {
        group = await ctx.db.attributeSynonymGroup.findFirst({
          where: { name: groupName },
          select: { id: true }
        } as any)
      }

      if (!group) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', `Группа синонимов ${groupName} не найдена`); errors.push({ sheet: 'AttributeSynonym', rowIndex: rowNumber, message: `Группа синонимов ${groupName} не найдена` }); return }

      let brandId: number | null = null
      if (brandSlug) {
        const brand = await ctx.db.brand.findUnique({ where: { slug: brandSlug }, select: { id: true } } as any)
        if (!brand) { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', `Бренд ${brandSlug} не найден`); errors.push({ sheet: 'AttributeSynonym', rowIndex: rowNumber, message: `Бренд ${brandSlug} не найден` }); return }
        brandId = brand.id
      }

      const existing = await ctx.db.attributeSynonym.findFirst({ where: { groupId: group.id, value }, select: { id: true } } as any)
      const data = { notes, brandId, compatibilityLevel: compatibilityLevel as any }

      if (existing) {
        await ctx.db.attributeSynonym.update({ where: { id: existing.id }, data } as any)
        counters.updated++; counters.rowsValid++; setReport(ws, rowNumber, 'UPDATED')
      } else {
        if (ctx.onConflict === 'update_only' || ctx.onConflict === 'skip') { counters.skipped++; setReport(ws, rowNumber, 'SKIPPED', 'Нет существующей записи'); return }
        if (ctx.onConflict === 'error') { counters.errorsCount++; setReport(ws, rowNumber, 'ERROR', 'Нет существующей записи'); errors.push({ sheet: 'AttributeSynonym', rowIndex: rowNumber, message: 'Нет существующей записи' }); return }
        await ctx.db.attributeSynonym.create({ data: { groupId: group.id, value, ...data } } as any)
        counters.created++; counters.rowsValid++; setReport(ws, rowNumber, 'CREATED')
      }
    })
  }
}
