import { useState } from "react"
import { Search, Package, Filter } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { StatusBadge } from "@/components/ui/status-badge"
import type { CatalogSearchFilters } from "@/types/catalog"

// Простые моковые данные для демонстрации
const mockResults = [
  {
    id: 1,
    partId: 1,
    catalogItemId: 1,
    accuracy: "EXACT_MATCH" as const,
    part: {
      id: 1,
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>и<PERSON> коленвала передний",
      partCategory: {
        id: 1,
        name: "Сальники двигателя",
        slug: "engine-seals"
      }
    },
    catalogItem: {
      id: 1,
      sku: "TC-25x35x7",
      description: "Радиальный сальник для коленчатого вала",
      brand: {
        id: 1,
        name: "SKF",
        slug: "skf",
        isOem: false
      },
      attributes: [
        {
          id: 1,
          value: "25.0",
          template: {
            id: 1,
            title: "Внутренний диаметр",
            unit: "MM"
          }
        },
        {
          id: 2,
          value: "35.0",
          template: {
            id: 2,
            title: "Наружный диаметр", 
            unit: "MM"
          }
        }
      ]
    }
  }
]

const mockCategories = [
  { id: 1, name: "Сальники двигателя" },
  { id: 2, name: "Сальники трансмиссии" },
  { id: 3, name: "Уплотнения гидравлики" }
]

const mockBrands = [
  { id: 1, name: "SKF", isOem: false },
  { id: 2, name: "Freudenberg", isOem: false },
  { id: 3, name: "Caterpillar", isOem: true }
]

export default function SimpleCatalogSearch() {
  const [filters, setFilters] = useState<CatalogSearchFilters>({
    query: "",
    categoryIds: [],
    brandIds: [],
    attributeFilters: {},
    accuracyLevels: [],
    isOemOnly: false,
  })

  const [showFilters, setShowFilters] = useState(false)

  const handleCategoryToggle = (categoryId: number) => {
    setFilters(prev => ({
      ...prev,
      categoryIds: prev.categoryIds.includes(categoryId)
        ? prev.categoryIds.filter(id => id !== categoryId)
        : [...prev.categoryIds, categoryId]
    }))
  }

  const handleBrandToggle = (brandId: number) => {
    setFilters(prev => ({
      ...prev,
      brandIds: prev.brandIds.includes(brandId)
        ? prev.brandIds.filter(id => id !== brandId)
        : [...prev.brandIds, brandId]
    }))
  }

  const activeFiltersCount = filters.categoryIds.length + filters.brandIds.length + (filters.isOemOnly ? 1 : 0)

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="mr-4 flex">
            <h1 className="text-lg font-semibold">PartTec3 Каталог</h1>
          </div>
          <div className="flex flex-1 items-center space-x-2">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Поиск по артикулу, описанию..."
                value={filters.query}
                onChange={(e) => setFilters(prev => ({ ...prev, query: e.target.value }))}
                className="pl-8"
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="gap-2"
            >
              <Filter className="h-4 w-4" />
              Фильтры
              {activeFiltersCount > 0 && (
                <Badge variant="secondary" className="ml-1 h-5 w-5 rounded-full p-0 text-xs">
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </div>
        </div>
      </header>

      <div className="container py-6">
        <div className="flex gap-6">
          {/* Filters Sidebar */}
          {showFilters && (
            <div className="w-64 space-y-4">
              {/* Categories */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Категории</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {mockCategories.map((category) => (
                    <div key={category.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`category-${category.id}`}
                        checked={filters.categoryIds.includes(category.id)}
                        onCheckedChange={() => handleCategoryToggle(category.id)}
                      />
                      <Label
                        htmlFor={`category-${category.id}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {category.name}
                      </Label>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Brands */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Производители</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center space-x-2 mb-3">
                    <Checkbox
                      id="oem-only"
                      checked={filters.isOemOnly}
                      onCheckedChange={(checked) => 
                        setFilters(prev => ({ ...prev, isOemOnly: !!checked }))
                      }
                    />
                    <Label htmlFor="oem-only" className="text-sm font-medium cursor-pointer">
                      Только OEM
                    </Label>
                  </div>
                  <Separator />
                  {mockBrands.map((brand) => (
                    <div key={brand.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`brand-${brand.id}`}
                        checked={filters.brandIds.includes(brand.id)}
                        onCheckedChange={() => handleBrandToggle(brand.id)}
                      />
                      <Label
                        htmlFor={`brand-${brand.id}`}
                        className="text-sm font-normal cursor-pointer flex items-center gap-2"
                      >
                        {brand.name}
                        {brand.isOem && (
                          <Badge variant="outline" className="text-xs">
                            OEM
                          </Badge>
                        )}
                      </Label>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          )}

          {/* Results */}
          <div className="flex-1">
            <div className="mb-4 flex items-center justify-between">
              <h2 className="text-xl font-semibold">Результаты поиска</h2>
              <Badge variant="secondary">
                <Package className="h-3 w-3 mr-1" />
                {mockResults.length} позиций
              </Badge>
            </div>

            <div className="space-y-4">
              {mockResults.map((result) => (
                <Card key={result.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="text-lg font-mono font-bold text-primary mb-1">
                          {result.catalogItem.sku}
                        </h3>
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="outline">
                            {result.catalogItem.brand.name}
                          </Badge>
                          {result.catalogItem.brand.isOem && (
                            <Badge variant="secondary">OEM</Badge>
                          )}
                        </div>
                        <h4 className="font-semibold text-sm mb-1">
                          {result.part.name}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {result.catalogItem.description}
                        </p>
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        <StatusBadge status={result.accuracy} size="sm" />
                        <Badge variant="outline" className="text-xs">
                          {result.part.partCategory.name}
                        </Badge>
                      </div>
                    </div>

                    {/* Attributes */}
                    <div className="flex flex-wrap gap-2 mb-3">
                      {result.catalogItem.attributes.map((attr) => (
                        <div
                          key={attr.id}
                          className="inline-flex items-center gap-1 px-2 py-1 rounded-full bg-muted text-xs"
                        >
                          <span className="text-muted-foreground">
                            {attr.template.title}:
                          </span>
                          <span className="font-mono font-semibold">
                            {attr.value}
                          </span>
                          {attr.template.unit && (
                            <span className="text-muted-foreground">
                              {attr.template.unit}
                            </span>
                          )}
                        </div>
                      ))}
                    </div>

                    <div className="flex items-center justify-between pt-2 border-t">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs font-mono">
                          #{result.partId}
                        </Badge>
                        <Badge variant="outline" className="text-xs font-mono">
                          #{result.catalogItemId}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          Подробности
                        </Button>
                        <Button size="sm">
                          В корзину
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}