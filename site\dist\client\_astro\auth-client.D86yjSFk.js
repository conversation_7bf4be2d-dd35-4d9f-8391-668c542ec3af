import{r as D}from"./index.GzzYzsTh.js";var se=Object.defineProperty,oe=Object.defineProperties,ie=Object.getOwnPropertyDescriptors,J=Object.getOwnPropertySymbols,ae=Object.prototype.hasOwnProperty,ue=Object.prototype.propertyIsEnumerable,V=(e,t,r)=>t in e?se(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,w=(e,t)=>{for(var r in t||(t={}))ae.call(t,r)&&V(e,r,t[r]);if(J)for(var r of J(t))ue.call(t,r)&&V(e,r,t[r]);return e},R=(e,t)=>oe(e,ie(t)),ce=class extends Error{constructor(e,t,r){super(t||e.toString(),{cause:r}),this.status=e,this.statusText=t,this.error=r}},le=async(e,t)=>{var r,n,s,a,o,l;let c=t||{};const i={onRequest:[t?.onRequest],onResponse:[t?.onResponse],onSuccess:[t?.onSuccess],onError:[t?.onError],onRetry:[t?.onRetry]};if(!t||!t?.plugins)return{url:e,options:c,hooks:i};for(const u of t?.plugins||[]){if(u.init){const f=await((r=u.init)==null?void 0:r.call(u,e.toString(),t));c=f.options||c,e=f.url}i.onRequest.push((n=u.hooks)==null?void 0:n.onRequest),i.onResponse.push((s=u.hooks)==null?void 0:s.onResponse),i.onSuccess.push((a=u.hooks)==null?void 0:a.onSuccess),i.onError.push((o=u.hooks)==null?void 0:o.onError),i.onRetry.push((l=u.hooks)==null?void 0:l.onRetry)}return{url:e,options:c,hooks:i}},G=class{constructor(e){this.options=e}shouldAttemptRetry(e,t){return this.options.shouldRetry?Promise.resolve(e<this.options.attempts&&this.options.shouldRetry(t)):Promise.resolve(e<this.options.attempts)}getDelay(){return this.options.delay}},fe=class{constructor(e){this.options=e}shouldAttemptRetry(e,t){return this.options.shouldRetry?Promise.resolve(e<this.options.attempts&&this.options.shouldRetry(t)):Promise.resolve(e<this.options.attempts)}getDelay(e){return Math.min(this.options.maxDelay,this.options.baseDelay*2**e)}};function de(e){if(typeof e=="number")return new G({type:"linear",attempts:e,delay:1e3});switch(e.type){case"linear":return new G(e);case"exponential":return new fe(e);default:throw new Error("Invalid retry strategy")}}var he=async e=>{const t={},r=async n=>typeof n=="function"?await n():n;if(e?.auth){if(e.auth.type==="Bearer"){const n=await r(e.auth.token);if(!n)return t;t.authorization=`Bearer ${n}`}else if(e.auth.type==="Basic"){const n=r(e.auth.username),s=r(e.auth.password);if(!n||!s)return t;t.authorization=`Basic ${btoa(`${n}:${s}`)}`}else if(e.auth.type==="Custom"){const n=r(e.auth.value);if(!n)return t;t.authorization=`${r(e.auth.prefix)} ${n}`}}return t},pe=/^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;function ye(e){const t=e.headers.get("content-type"),r=new Set(["image/svg","application/xml","application/xhtml","application/html"]);if(!t)return"json";const n=t.split(";").shift()||"";return pe.test(n)?"json":r.has(n)||n.startsWith("text/")?"text":"blob"}function ge(e){try{return JSON.parse(e),!0}catch{return!1}}function z(e){if(e===void 0)return!1;const t=typeof e;return t==="string"||t==="number"||t==="boolean"||t===null?!0:t!=="object"?!1:Array.isArray(e)?!0:e.buffer?!1:e.constructor&&e.constructor.name==="Object"||typeof e.toJSON=="function"}function Q(e){try{return JSON.parse(e)}catch{return e}}function X(e){return typeof e=="function"}function me(e){if(e?.customFetchImpl)return e.customFetchImpl;if(typeof globalThis<"u"&&X(globalThis.fetch))return globalThis.fetch;if(typeof window<"u"&&X(window.fetch))return window.fetch;throw new Error("No fetch implementation found")}async function ve(e){const t=new Headers(e?.headers),r=await he(e);for(const[n,s]of Object.entries(r||{}))t.set(n,s);if(!t.has("content-type")){const n=we(e?.body);n&&t.set("content-type",n)}return t}function we(e){return z(e)?"application/json":null}function _e(e){if(!e?.body)return null;const t=new Headers(e?.headers);if(z(e.body)&&!t.has("content-type")){for(const[r,n]of Object.entries(e?.body))n instanceof Date&&(e.body[r]=n.toISOString());return JSON.stringify(e.body)}return e.body}function be(e,t){var r;if(t?.method)return t.method.toUpperCase();if(e.startsWith("@")){const n=(r=e.split("@")[1])==null?void 0:r.split("/")[0];return K.includes(n)?n.toUpperCase():t?.body?"POST":"GET"}return t?.body?"POST":"GET"}function Re(e,t){let r;return!e?.signal&&e?.timeout&&(r=setTimeout(()=>t?.abort(),e?.timeout)),{abortTimeout:r,clearTimeout:()=>{r&&clearTimeout(r)}}}var Se=class Z extends Error{constructor(t,r){super(r||JSON.stringify(t,null,2)),this.issues=t,Object.setPrototypeOf(this,Z.prototype)}};async function C(e,t){let r=await e["~standard"].validate(t);if(r.issues)throw new Se(r.issues);return r.value}var K=["get","post","put","patch","delete"],Te=e=>({id:"apply-schema",name:"Apply Schema",version:"1.0.0",async init(t,r){var n,s,a,o;const l=((s=(n=e.plugins)==null?void 0:n.find(c=>{var i;return(i=c.schema)!=null&&i.config?t.startsWith(c.schema.config.baseURL||"")||t.startsWith(c.schema.config.prefix||""):!1}))==null?void 0:s.schema)||e.schema;if(l){let c=t;(a=l.config)!=null&&a.prefix&&c.startsWith(l.config.prefix)&&(c=c.replace(l.config.prefix,""),l.config.baseURL&&(t=t.replace(l.config.prefix,l.config.baseURL))),(o=l.config)!=null&&o.baseURL&&c.startsWith(l.config.baseURL)&&(c=c.replace(l.config.baseURL,""));const i=l.schema[c];if(i){let u=R(w({},r),{method:i.method,output:i.output});return r?.disableValidation||(u=R(w({},u),{body:i.input?await C(i.input,r?.body):r?.body,params:i.params?await C(i.params,r?.params):r?.params,query:i.query?await C(i.query,r?.query):r?.query})),{url:t,options:u}}}return{url:t,options:r}}}),Oe=e=>{async function t(r,n){const s=R(w(w({},e),n),{plugins:[...e?.plugins||[],Te(e||{})]});if(e?.catchAllError)try{return await M(r,s)}catch(a){return{data:null,error:{status:500,statusText:"Fetch Error",message:"Fetch related error. Captured by catchAllError option. See error property for more details.",error:a}}}return await M(r,s)}return t};function Pe(e,t){let{baseURL:r,params:n,query:s}=t||{query:{},params:{},baseURL:""},a=e.startsWith("http")?e.split("/").slice(0,3).join("/"):r||"";if(e.startsWith("@")){const f=e.toString().split("@")[1].split("/")[0];K.includes(f)&&(e=e.replace(`@${f}/`,"/"))}a.endsWith("/")||(a+="/");let[o,l]=e.replace(a,"").split("?");const c=new URLSearchParams(l);for(const[f,h]of Object.entries(s||{}))h!=null&&c.set(f,String(h));if(n)if(Array.isArray(n)){const f=o.split("/").filter(h=>h.startsWith(":"));for(const[h,_]of f.entries()){const S=n[h];o=o.replace(_,S)}}else for(const[f,h]of Object.entries(n))o=o.replace(`:${f}`,String(h));o=o.split("/").map(encodeURIComponent).join("/"),o.startsWith("/")&&(o=o.slice(1));let i=c.toString();return i=i.length>0?`?${i}`.replace(/\+/g,"%20"):"",a.startsWith("http")?new URL(`${o}${i}`,a):`${a}${o}${i}`}var M=async(e,t)=>{var r,n,s,a,o,l,c,i;const{hooks:u,url:f,options:h}=await le(e,t),_=me(h),S=new AbortController,b=(r=h.signal)!=null?r:S.signal,T=Pe(f,h),A=_e(h),E=await ve(h),d=be(f,h);let p=R(w({},h),{url:T,headers:E,body:A,method:d,signal:b});for(const m of u.onRequest)if(m){const g=await m(p);g instanceof Object&&(p=g)}("pipeTo"in p&&typeof p.pipeTo=="function"||typeof((n=t?.body)==null?void 0:n.pipe)=="function")&&("duplex"in p||(p.duplex="half"));const{clearTimeout:I}=Re(h,S);let y=await _(p.url,p);I();const W={response:y,request:p};for(const m of u.onResponse)if(m){const g=await m(R(w({},W),{response:(s=t?.hookOptions)!=null&&s.cloneResponse?y.clone():y}));g instanceof Response?y=g:g instanceof Object&&(y=g.response)}if(y.ok){if(!(p.method!=="HEAD"))return{data:"",error:null};const g=ye(y),O={data:"",response:y,request:p};if(g==="json"||g==="text"){const P=await y.text(),ne=await((a=p.jsonParser)!=null?a:Q)(P);O.data=ne}else O.data=await y[g]();p?.output&&p.output&&!p.disableValidation&&(O.data=await C(p.output,O.data));for(const P of u.onSuccess)P&&await P(R(w({},O),{response:(o=t?.hookOptions)!=null&&o.cloneResponse?y.clone():y}));return t?.throw?O.data:{data:O.data,error:null}}const te=(l=t?.jsonParser)!=null?l:Q,N=await y.text(),H=ge(N),$=H?await te(N):null,re={response:y,responseText:N,request:p,error:R(w({},$),{status:y.status,statusText:y.statusText})};for(const m of u.onError)m&&await m(R(w({},re),{response:(c=t?.hookOptions)!=null&&c.cloneResponse?y.clone():y}));if(t?.retry){const m=de(t.retry),g=(i=t.retryAttempt)!=null?i:0;if(await m.shouldAttemptRetry(g,y)){for(const P of u.onRetry)P&&await P(W);const O=m.getDelay(g);return await new Promise(P=>setTimeout(P,O)),await M(e,R(w({},t),{retryAttempt:g+1}))}}if(t?.throw)throw new ce(y.status,y.statusText,H?$:N);return{data:null,error:R(w({},$),{status:y.status,statusText:y.statusText})}},Ee={};const F=Object.create(null),x=e=>Ee||globalThis.Deno?.env.toObject()||globalThis.__env__||(e?F:globalThis),L=new Proxy(F,{get(e,t){return x()[t]??F[t]},has(e,t){const r=x();return t in r||t in F},set(e,t,r){const n=x(!0);return n[t]=r,!0},deleteProperty(e,t){if(!t)return!1;const r=x(!0);return delete r[t],!0},ownKeys(){const e=x(!0);return Object.keys(e)}});class Ue extends Error{constructor(t,r){super(t),this.name="BetterAuthError",this.message=t,this.cause=r,this.stack=""}}function Ae(e){try{return new URL(e).pathname!=="/"}catch{throw new Ue(`Invalid base URL: ${e}. Please provide a valid base URL.`)}}function B(e,t="/api/auth"){return Ae(e)?e:(t=t.startsWith("/")?t:`/${t}`,`${e.replace(/\/+$/,"")}${t}`)}function Le(e,t,r){if(e)return B(e,t);const n=L.BETTER_AUTH_URL||L.NEXT_PUBLIC_BETTER_AUTH_URL||L.PUBLIC_BETTER_AUTH_URL||L.NUXT_PUBLIC_BETTER_AUTH_URL||L.NUXT_PUBLIC_AUTH_URL||(L.BASE_URL!=="/"?L.BASE_URL:void 0);if(n)return B(n,t);if(typeof window<"u"&&window.location)return B(window.location.origin,t)}let v=[],U=0;const k=4;let ee=e=>{let t=[],r={get(){return r.lc||r.listen(()=>{})(),r.value},lc:0,listen(n){return r.lc=t.push(n),()=>{for(let a=U+k;a<v.length;)v[a]===n?v.splice(a,k):a+=k;let s=t.indexOf(n);~s&&(t.splice(s,1),--r.lc||r.off())}},notify(n,s){let a=!v.length;for(let o of t)v.push(o,r.value,n,s);if(a){for(U=0;U<v.length;U+=k)v[U](v[U+1],v[U+2],v[U+3]);v.length=0}},off(){},set(n){let s=r.value;s!==n&&(r.value=n,r.notify(s))},subscribe(n){let s=r.listen(n);return n(r.value),s},value:e};return r};const xe=5,q=6,j=10;let Ie=(e,t,r,n)=>(e.events=e.events||{},e.events[r+j]||(e.events[r+j]=n(s=>{e.events[r].reduceRight((a,o)=>(o(a),a),{shared:{},...s})})),e.events[r]=e.events[r]||[],e.events[r].push(t),()=>{let s=e.events[r],a=s.indexOf(t);s.splice(a,1),s.length||(delete e.events[r],e.events[r+j](),delete e.events[r+j])}),Ne=1e3,ke=(e,t)=>Ie(e,n=>{let s=t(n);s&&e.events[q].push(s)},xe,n=>{let s=e.listen;e.listen=(...o)=>(!e.lc&&!e.active&&(e.active=!0,n()),s(...o));let a=e.off;return e.events[q]=[],e.off=()=>{a(),setTimeout(()=>{if(e.active&&!e.lc){e.active=!1;for(let o of e.events[q])o();e.events[q]=[]}},Ne)},()=>{e.listen=s,e.off=a}});function qe(e,t,r){let n=new Set(t).add(void 0);return e.listen((s,a,o)=>{n.has(o)&&r(s,a,o)})}const je=typeof window>"u",Ce=(e,t,r,n)=>{const s=ee({data:null,error:null,isPending:!0,isRefetching:!1,refetch:()=>a()}),a=()=>{const l=typeof n=="function"?n({data:s.get().data,error:s.get().error,isPending:s.get().isPending}):n;return r(t,{...l,async onSuccess(c){s.set({data:c.data,error:null,isPending:!1,isRefetching:!1,refetch:s.value.refetch}),await l?.onSuccess?.(c)},async onError(c){const{request:i}=c,u=typeof i.retry=="number"?i.retry:i.retry?.attempts,f=i.retryAttempt||0;u&&f<u||(s.set({error:c.error,data:null,isPending:!1,isRefetching:!1,refetch:s.value.refetch}),await l?.onError?.(c))},async onRequest(c){const i=s.get();s.set({isPending:i.data===null,data:i.data,error:null,isRefetching:!0,refetch:s.value.refetch}),await l?.onRequest?.(c)}})};e=Array.isArray(e)?e:[e];let o=!1;for(const l of e)l.subscribe(()=>{je||(o?a():ke(s,()=>(setTimeout(()=>{a()},0),o=!0,()=>{s.off(),l.off()})))});return s},Fe={proto:/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,constructor:/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,protoShort:/"__proto__"\s*:/,constructorShort:/"constructor"\s*:/},$e=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/,Y={true:!0,false:!1,null:null,undefined:void 0,nan:Number.NaN,infinity:Number.POSITIVE_INFINITY,"-infinity":Number.NEGATIVE_INFINITY},De=/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{1,7}))?(?:Z|([+-])(\d{2}):(\d{2}))$/;function Be(e){return e instanceof Date&&!isNaN(e.getTime())}function Me(e){const t=De.exec(e);if(!t)return null;const[,r,n,s,a,o,l,c,i,u,f]=t;let h=new Date(Date.UTC(parseInt(r,10),parseInt(n,10)-1,parseInt(s,10),parseInt(a,10),parseInt(o,10),parseInt(l,10),c?parseInt(c.padEnd(3,"0"),10):0));if(i){const _=(parseInt(u,10)*60+parseInt(f,10))*(i==="+"?-1:1);h.setUTCMinutes(h.getUTCMinutes()+_)}return Be(h)?h:null}function We(e,t={}){const{strict:r=!1,warnings:n=!1,reviver:s,parseDates:a=!0}=t;if(typeof e!="string")return e;const o=e.trim();if(o[0]==='"'&&o.endsWith('"')&&!o.slice(1,-1).includes('"'))return o.slice(1,-1);const l=o.toLowerCase();if(l.length<=9&&l in Y)return Y[l];if(!$e.test(o)){if(r)throw new SyntaxError("[better-json] Invalid JSON");return e}if(Object.entries(Fe).some(([i,u])=>{const f=u.test(o);return f&&n&&console.warn(`[better-json] Detected potential prototype pollution attempt using ${i} pattern`),f})&&r)throw new Error("[better-json] Potential prototype pollution attempt detected");try{return JSON.parse(o,(u,f)=>{if(u==="__proto__"||u==="constructor"&&f&&typeof f=="object"&&"prototype"in f){n&&console.warn(`[better-json] Dropping "${u}" key to prevent prototype pollution`);return}if(a&&typeof f=="string"){const h=Me(f);if(h)return h}return s?s(u,f):f})}catch(i){if(r)throw i;return e}}function He(e,t={strict:!0}){return We(e,t)}const Je={id:"redirect",name:"Redirect",hooks:{onSuccess(e){if(e.data?.url&&e.data?.redirect&&typeof window<"u"&&window.location&&window.location)try{window.location.href=e.data.url}catch{}}}};function Ve(e){const t=ee(!1);return{session:Ce(t,"/get-session",e,{method:"GET"}),$sessionSignal:t}}const Ge=e=>{const t="credentials"in Request.prototype,r=Le(e?.baseURL,e?.basePath),n=e?.plugins?.flatMap(d=>d.fetchPlugins).filter(d=>d!==void 0)||[],s={id:"lifecycle-hooks",name:"lifecycle-hooks",hooks:{onSuccess:e?.fetchOptions?.onSuccess,onError:e?.fetchOptions?.onError,onRequest:e?.fetchOptions?.onRequest,onResponse:e?.fetchOptions?.onResponse}},{onSuccess:a,onError:o,onRequest:l,onResponse:c,...i}=e?.fetchOptions||{},u=Oe({baseURL:r,...t?{credentials:"include"}:{},method:"GET",jsonParser(d){return d?He(d,{strict:!1}):null},customFetchImpl:async(d,p)=>{try{return await fetch(d,p)}catch{return Response.error()}},...i,plugins:[s,...i.plugins||[],...e?.disableDefaultFetchPlugins?[]:[Je],...n]}),{$sessionSignal:f,session:h}=Ve(u),_=e?.plugins||[];let S={},b={$sessionSignal:f,session:h},T={"/sign-out":"POST","/revoke-sessions":"POST","/revoke-other-sessions":"POST","/delete-user":"POST"};const A=[{signal:"$sessionSignal",matcher(d){return d==="/sign-out"||d==="/update-user"||d.startsWith("/sign-in")||d.startsWith("/sign-up")||d==="/delete-user"||d==="/verify-email"}}];for(const d of _)d.getAtoms&&Object.assign(b,d.getAtoms?.(u)),d.pathMethods&&Object.assign(T,d.pathMethods),d.atomListeners&&A.push(...d.atomListeners);const E={notify:d=>{b[d].set(!b[d].get())},listen:(d,p)=>{b[d].subscribe(p)},atoms:b};for(const d of _)d.getActions&&Object.assign(S,d.getActions?.(u,E,e));return{pluginsActions:S,pluginsAtoms:b,pluginPathMethods:T,atomListeners:A,$fetch:u,$store:E}};function Qe(e,t,r){const n=t[e],{fetchOptions:s,query:a,...o}=r||{};return n||(s?.method?s.method:o&&Object.keys(o).length>0?"POST":"GET")}function Xe(e,t,r,n,s){function a(o=[]){return new Proxy(function(){},{get(l,c){const i=[...o,c];let u=e;for(const f of i)if(u&&typeof u=="object"&&f in u)u=u[f];else{u=void 0;break}return typeof u=="function"?u:a(i)},apply:async(l,c,i)=>{const u="/"+o.map(E=>E.replace(/[A-Z]/g,d=>`-${d.toLowerCase()}`)).join("/"),f=i[0]||{},h=i[1]||{},{query:_,fetchOptions:S,...b}=f,T={...h,...S},A=Qe(u,r,f);return await t(u,{...T,body:A==="GET"?void 0:{...b,...T?.body||{}},query:_||T?.query,method:A,async onSuccess(E){await T?.onSuccess?.(E);const d=s?.find(y=>y.matcher(u));if(!d)return;const p=n[d.signal];if(!p)return;const I=p.get();setTimeout(()=>{p.set(!I)},10)}})}})}return a()}function Ye(e,t={}){let r=D.useRef(e.get());const{keys:n,deps:s=[e,n]}=t;let a=D.useCallback(l=>{const c=i=>{r.current!==i&&(r.current=i,l())};return c(e.value),n?.length?qe(e,n,c):e.listen(c)},s),o=()=>r.current;return D.useSyncExternalStore(a,o,o)}function ze(e){return`use${Ze(e)}`}function Ze(e){return e.charAt(0).toUpperCase()+e.slice(1)}function Ke(e){const{pluginPathMethods:t,pluginsActions:r,pluginsAtoms:n,$fetch:s,$store:a,atomListeners:o}=Ge(e);let l={};for(const[u,f]of Object.entries(n))l[ze(u)]=()=>Ye(f);const c={...r,...l,$fetch:s,$store:a};return Xe(c,s,t,n,o)}const et=e=>({id:"additional-fields-client",$InferServerPlugin:{}}),tt="http://localhost:3000",st=Ke({baseURL:tt,fetchOptions:{credentials:"include",timeout:1e4},plugins:[et()]});export{st as a};
