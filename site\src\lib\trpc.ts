import { createTRPCReact } from '@trpc/react-query';
import { createTRPCClient, httpBatchLink, loggerLink } from '@trpc/client';
import superjson from 'superjson';
import type { AppRouter } from '../../../api/router';

// Базовый URL API (совместимо с better-auth клиентом)
const baseURL = import.meta.env.PUBLIC_API_URL || 'http://localhost:3000';

// React Query tRPC клиент для компонентов
export const trpc = createTRPCReact<AppRouter>();

// Обычный tRPC клиент для использования в Astro компонентах
export const trpcClient = createTRPCClient<AppRouter>({
  links: [
    loggerLink({
      enabled: () => import.meta.env.DEV,
    }),
    httpBatchLink({
      url: `${baseURL}/trpc`,
      transformer: superjson,
      fetch: (url, options) =>
        fetch(url, {
          ...(options as RequestInit),
          credentials: 'include',
        }),
    }),
  ],
});

// Утилита для получения базового URL в рантайме (SSR/SPA)
export const getBaseUrl = () => {
  if (typeof window !== 'undefined') return '';
  return baseURL;
};
