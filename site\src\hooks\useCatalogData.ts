"use client"

import { useMemo } from "react"
import { trpc } from "@/lib/trpc"

// Хук для загрузки категорий
export function useCategories(options?: { rootOnly?: boolean; take?: number }) {
  const { data: categories, isLoading, error } = trpc.site.catalog.categories.useQuery({
    rootOnly: options?.rootOnly ?? false,
    take: options?.take ?? 200
  }, {
    staleTime: 10 * 60_000, // 10 минут - категории меняются очень редко
    gcTime: 60 * 60_000, // 1 час в кэше
  })

  const formattedCategories = useMemo(() => {
    return categories?.map(cat => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug,
      level: cat.level,
      path: cat.path
    })) || []
  }, [categories])

  return {
    categories: formattedCategories,
    isLoading,
    error
  }
}

// Хук для загрузки брендов
export function useBrands(options?: { take?: number }) {
  const { data: brands, isLoading, error } = trpc.site.catalog.brands.useQuery({
    take: options?.take ?? 100
  }, {
    staleTime: 10 * 60_000, // 10 минут - бренды меняются редко
    gcTime: 60 * 60_000, // 1 час в кэше
  })

  const formattedBrands = useMemo(() => {
    return brands?.map(brand => ({
      id: brand.id,
      name: brand.name,
      slug: brand.slug,
      isOem: brand.isOem,
      country: brand.country
    })) || []
  }, [brands])

  return {
    brands: formattedBrands,
    isLoading,
    error
  }
}

// Хук для загрузки шаблонов атрибутов
export function useAttributeTemplates(options?: { take?: number }) {
  const { data: templates, isLoading, error } = trpc.site.attributes.templates.useQuery({
    take: options?.take ?? 100
  }, {
    staleTime: 15 * 60_000, // 15 минут - шаблоны атрибутов меняются очень редко
    gcTime: 60 * 60_000, // 1 час в кэше
  })

  const formattedTemplates = useMemo(() => {
    return templates?.map(template => ({
      id: template.id,
      name: template.name,
      title: template.title,
      dataType: template.dataType,
      unit: template.unit,
      isRequired: template.isRequired,
      allowedValues: template.allowedValues,
      tolerance: template.tolerance
    })) || []
  }, [templates])

  return {
    templates: formattedTemplates,
    isLoading,
    error
  }
}

// Хук для получения статистики атрибутов
export function useAttributeStats(templateId: number) {
  const { data: stats, isLoading, error } = trpc.site.attributes.stats.useQuery(
    { templateId },
    { enabled: !!templateId }
  )

  return {
    stats,
    isLoading,
    error
  }
}

// Комбинированный хук для всех данных каталога
export function useCatalogMetadata() {
  const categories = useCategories()
  const brands = useBrands()
  const templates = useAttributeTemplates()

  const isLoading = categories.isLoading || brands.isLoading || templates.isLoading
  const hasError = categories.error || brands.error || templates.error

  return {
    categories: categories.categories,
    brands: brands.brands,
    templates: templates.templates,
    isLoading,
    hasError,
    errors: {
      categories: categories.error,
      brands: brands.error,
      templates: templates.error
    }
  }
}
