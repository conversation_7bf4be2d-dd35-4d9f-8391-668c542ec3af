"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { StatusBadge } from "@/components/ui/status-badge"
import { ModernCard, ModernCardContent, ModernCardHeader, ModernCardTitle } from "@/components/ui/modern-card"
import { MediaGallery } from "../pro/MediaGallery"
import type { PartApplicability } from "@/types/catalog"

export default function ItemDetailsIsland() {
  const [selectedApplicability, setSelectedApplicability] = useState<PartApplicability | null>(null)

  // Слушаем события от других компонентов
  useEffect(() => {
    const handleOpenDetails = (event: CustomEvent<PartApplicability>) => {
      setSelectedApplicability(event.detail)
    }

    window.addEventListener('openItemDetails', handleOpenDetails as EventListener)
    return () => window.removeEventListener('openItemDetails', handleOpenDetails as EventListener)
  }, [])

  const handleClose = () => {
    setSelectedApplicability(null)
  }

  if (!selectedApplicability) {
    return null
  }

  return (
    <Dialog open={!!selectedApplicability} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-card border-border/40">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold flex items-center justify-between">
            <span className="font-mono">{selectedApplicability.catalogItem.sku}</span>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{selectedApplicability.catalogItem.brand.name}</Badge>
              <StatusBadge status={selectedApplicability.accuracy as any} />
            </div>
          </DialogTitle>
          <p className="text-muted-foreground leading-relaxed">
            {selectedApplicability.catalogItem.description}
          </p>
        </DialogHeader>

        <Tabs defaultValue="overview" className="mt-4">
          <TabsList className="grid w-full grid-cols-5 bg-muted/50">
            <TabsTrigger value="overview">Обзор</TabsTrigger>
            <TabsTrigger value="media">Медиа</TabsTrigger>
            <TabsTrigger value="part">Эталонная группа</TabsTrigger>
            <TabsTrigger value="attributes">Атрибуты</TabsTrigger>
            <TabsTrigger value="applicability">Применимость</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-3">Изображения и документы</h3>
                <MediaGallery 
                  mediaAssets={selectedApplicability.catalogItem.mediaAssets} 
                  primaryImage={selectedApplicability.catalogItem.image ?? null} 
                  title={selectedApplicability.catalogItem.sku} 
                />
              </div>

              <div className="space-y-4">
                <ModernCard variant="glass">
                  <ModernCardHeader>
                    <ModernCardTitle className="text-sm">Информация о товаре</ModernCardTitle>
                  </ModernCardHeader>
                  <ModernCardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <span className="text-xs text-muted-foreground">Артикул</span>
                        <div className="font-mono font-semibold text-sm">
                          {selectedApplicability.catalogItem.sku}
                        </div>
                      </div>
                      <div>
                        <span className="text-xs text-muted-foreground">Бренд</span>
                        <div className="font-semibold text-sm">
                          {selectedApplicability.catalogItem.brand.name}
                        </div>
                      </div>
                      <div>
                        <span className="text-xs text-muted-foreground">Страна</span>
                        <div className="font-semibold text-sm">
                          {selectedApplicability.catalogItem.brand.country || "Не указана"}
                        </div>
                      </div>
                      <div>
                        <span className="text-xs text-muted-foreground">Тип</span>
                        <div className="font-semibold text-sm">
                          {selectedApplicability.catalogItem.brand.isOem ? "OEM" : "Aftermarket"}
                        </div>
                      </div>
                    </div>
                  </ModernCardContent>
                </ModernCard>

                <ModernCard variant="glass">
                  <ModernCardHeader>
                    <ModernCardTitle className="text-sm">Эталонная группа</ModernCardTitle>
                  </ModernCardHeader>
                  <ModernCardContent className="space-y-2">
                    <div>
                      <span className="text-xs text-muted-foreground">Название группы</span>
                      <div className="font-semibold text-sm">
                        {selectedApplicability.part.name || "Без названия"}
                      </div>
                    </div>
                    <div>
                      <span className="text-xs text-muted-foreground">Категория</span>
                      <div className="font-semibold text-sm">
                        {selectedApplicability.part.partCategory.name}
                      </div>
                    </div>
                    <div>
                      <span className="text-xs text-muted-foreground">Путь в иерархии</span>
                      <div className="font-mono text-xs">{selectedApplicability.part.path}</div>
                    </div>
                  </ModernCardContent>
                </ModernCard>
              </div>
            </div>

            {selectedApplicability.notes && (
              <ModernCard variant="glass" className="border-info/20 bg-info/5">
                <ModernCardHeader>
                  <ModernCardTitle className="text-info flex items-center gap-2 text-sm">
                    <div className="h-2 w-2 rounded-full bg-info" />
                    Примечания по применимости
                  </ModernCardTitle>
                </ModernCardHeader>
                <ModernCardContent>
                  <p className="leading-relaxed text-sm">{selectedApplicability.notes}</p>
                </ModernCardContent>
              </ModernCard>
            )}
          </TabsContent>

          <TabsContent value="media" className="space-y-4 mt-4">
            <MediaGallery 
              mediaAssets={selectedApplicability.catalogItem.mediaAssets} 
              primaryImage={selectedApplicability.catalogItem.image ?? null} 
              title={selectedApplicability.catalogItem.sku} 
            />
          </TabsContent>

          <TabsContent value="part" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-3 text-sm">Медиа эталонной группы</h3>
                <MediaGallery 
                  mediaAssets={selectedApplicability.part.mediaAssets} 
                  primaryImage={selectedApplicability.part.image ?? null} 
                  title={selectedApplicability.part.name || "Эталонная группа"} 
                />
              </div>

              <ModernCard variant="glass">
                <ModernCardHeader>
                  <ModernCardTitle className="text-sm">Эталонные атрибуты группы</ModernCardTitle>
                </ModernCardHeader>
                <ModernCardContent>
                  <div className="space-y-2">
                    {selectedApplicability.part.attributes.map((attr) => (
                      <div 
                        key={attr.id} 
                        className="flex items-center justify-between p-2 rounded bg-card border border-border-strong hover:border-primary/40 transition-colors"
                      >
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">{attr.template.title}</span>
                          {attr.template.unit && (
                            <Badge variant="outline" className="text-xs h-4">{attr.template.unit}</Badge>
                          )}
                        </div>
                        <div className="font-mono font-bold text-sm">{attr.value}</div>
                      </div>
                    ))}
                  </div>
                </ModernCardContent>
              </ModernCard>
            </div>
          </TabsContent>

          <TabsContent value="attributes" className="space-y-4 mt-4">
            <ModernCard variant="glass">
              <ModernCardHeader>
                <ModernCardTitle className="text-sm">Атрибуты каталожной позиции</ModernCardTitle>
              </ModernCardHeader>
              <ModernCardContent>
                <div className="space-y-2">
                  {selectedApplicability.catalogItem.attributes.map((attr) => (
                    <div 
                      key={attr.id} 
                      className="flex items-center justify-between p-2 rounded bg-card border border-border-strong hover:border-primary/40 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">{attr.template.title}</span>
                        {attr.template.unit && (
                          <Badge variant="outline" className="text-xs h-4">{attr.template.unit}</Badge>
                        )}
                      </div>
                      <div className="font-mono font-bold text-sm">{attr.value}</div>
                    </div>
                  ))}
                </div>
              </ModernCardContent>
            </ModernCard>
          </TabsContent>

          <TabsContent value="applicability" className="space-y-4 mt-4">
            <ModernCard variant="glass">
              <ModernCardHeader>
                <ModernCardTitle className="text-sm">Детали применимости</ModernCardTitle>
              </ModernCardHeader>
              <ModernCardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-muted-foreground">Точность совпадения</span>
                    <div className="mt-1">
                      <StatusBadge status={selectedApplicability.accuracy as any} />
                    </div>
                  </div>
                  <div>
                    <span className="text-xs text-muted-foreground">ID применимости</span>
                    <div className="font-mono font-semibold text-sm">#{selectedApplicability.id}</div>
                  </div>
                </div>

                {selectedApplicability.notes && (
                  <div className="p-3 rounded bg-info/10 border border-info/20">
                    <h4 className="font-medium text-sm mb-2">Примечания:</h4>
                    <p className="text-sm leading-relaxed">{selectedApplicability.notes}</p>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4 pt-3 border-t border-border">
                  <div>
                    <span className="text-xs text-muted-foreground">Создано</span>
                    <div className="text-sm">
                      {new Date(selectedApplicability.part.createdAt).toLocaleString("ru-RU")}
                    </div>
                  </div>
                  <div>
                    <span className="text-xs text-muted-foreground">Обновлено</span>
                    <div className="text-sm">
                      {new Date(selectedApplicability.part.updatedAt).toLocaleString("ru-RU")}
                    </div>
                  </div>
                </div>
              </ModernCardContent>
            </ModernCard>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
