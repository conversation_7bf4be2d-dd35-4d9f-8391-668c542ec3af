import{j as s}from"./jsx-runtime.D_zvdyIk.js";import{C as l,b as i,c as r,d as c,a as n}from"./card.DUNQ4BBf.js";import{B as m}from"./badge.BTnnEvrQ.js";import{n as d}from"./router.WLLD8StG.js";import"./index.GzzYzsTh.js";import"./utils.CBfrqCZ4.js";import"./index.3rXK4OIH.js";function N({brand:e,onClick:a}){const t=()=>{a?a():d(`/catalog/brands/${e.slug}`)};return s.jsxs(l,{className:"cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]",onClick:t,children:[s.jsx(i,{className:"pb-3",children:s.jsxs("div",{className:"flex items-start justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsx(r,{className:"text-lg line-clamp-1",children:e.name}),e.country&&s.jsx(c,{className:"mt-1",children:e.country})]}),s.jsx("div",{className:"flex flex-col gap-1",children:s.jsx(m,{variant:e.isOem?"default":"secondary",children:e.isOem?"OEM":"Aftermarket"})})]})}),s.jsx(n,{className:"pt-0",children:e._count&&s.jsx("div",{className:"space-y-2",children:s.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-muted-foreground",children:"Запчасти:"}),s.jsx("span",{className:"font-medium",children:e._count.catalogItems})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-muted-foreground",children:"Модели:"}),s.jsx("span",{className:"font-medium",children:e._count.equipmentModel})]})]})})})]})}export{N as BrandCard};
